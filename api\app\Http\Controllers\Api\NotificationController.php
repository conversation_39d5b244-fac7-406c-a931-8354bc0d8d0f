<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class NotificationController extends Controller
{


    //all notifications
    public function all()
    {
        $user = User::find(auth()->id());
        $notifications = $user->notifications;
        $notificationsData = [];
        foreach ($notifications as $notification) {
            // Access the message and read_at fields
            $message = $notification->data['message'];
            $readAt = $notification->read_at ? Carbon::parse($notification->read_at)->diffForHumans() : null;
            $createdAt = Carbon::parse($notification->created_at)->diffForHumans();
            $type = $notification->type;
            $notification_id = $notification->id;
            $notificationsData[] = [
                'type' => $type,
                'id' => $notification_id,
                'message' => $message,
                'read_at' => $readAt,
                'created_at' => $createdAt
            ];
        }
        return response($notificationsData);
    }
    //unread notifications
    public function index()
    {
        $user = User::find(auth()->id());
        $notifications = $user->unreadNotifications;
        $notificationsData = [];
        foreach ($notifications as $notification) {
            // Access the message and read_at fields
            $message = $notification->data['message'];
            $readAt = $notification->read_at ? Carbon::parse($notification->read_at)->diffForHumans() : null;
            $createdAt = Carbon::parse($notification->created_at)->diffForHumans();
            $notification_id = $notification->id;
            $type = $notification->type;
            $notificationsData[] = [
                'id' => $notification_id,
                'type' => $type,
                'message' => $message,
                'read_at' => $readAt,
                'created_at' => $createdAt
            ];
        }
        return response($notificationsData);
    }
    //count notifications
    public function countUnreadNotifications()
    {
        $user = User::find(auth()->id());
        $notifications = $user->unreadNotifications->count();
        return response($notifications);
    }
    //seen 
    public function viewedNotifications()
    {
        $user = User::find(auth()->id());
        $notifications = $user->readNotifications;
        $notificationData = [];

        foreach ($notifications as $notification) {
            // Access the message and read_at fields
            $message = $notification->data['message'];
            $readAt = $notification->read_at ? Carbon::parse($notification->read_at)->diffForHumans() : null;
            $createdAt = Carbon::parse($notification->created_at)->diffForHumans();
            $notification_id = $notification->id;
            $type = $notification->type;
            $notificationData[] = [
                'id' => $notification_id,
                'type' => $type,
                'message' => $message,
                'read_at' => $readAt,
                'created_at' => $createdAt
            ];
        }
        return response($notificationData);
    }
    //markit as read
    public function markAsReadNotification($notification_id)
    {
        $user = User::find(auth()->id());
        $notification = $user->notifications->where('id', $notification_id)->first();

        if ($notification && !$notification->read_at) {
            $notification->markAsRead();
        }

        if ($notification->data['message'] && $notification->type == "App\Notifications\ContractFollowUpNotification") {

            $filepath = $notification->data['message']['contract_document'];
            if (Storage::fileExists($filepath)) {
                $path = Storage::path($filepath);
                $file = file_get_contents($path);
                $ext = pathinfo($path, PATHINFO_EXTENSION);
                $base64File = base64_encode($file);
            }
            return response(['notification' => $notification, 'document' => $base64File ?? '', 'ext' => $ext ?? ''], 200);
        } else {
            return response($notification);
        }
    }

    //mark all of it as read
    public function markAllAsRead()
    {

        $user = User::find(auth()->id());
        $user->unreadNotifications->markAsRead();

        return response('', 204);
    }

    public function clearNotifications()
    {
        $user = User::find(auth()->id());

        $user->readNotifications->each(function ($notification) {
            $notification->delete();
        });

        return response('', 204);
    }

    //delete notifications
    public function delete($id)
    {
        $user = User::find(auth()->id());
        $notification = $user->notifications->where('id', $id)->first();
        $notification->delete();
        return response('', 204);
    }
}
