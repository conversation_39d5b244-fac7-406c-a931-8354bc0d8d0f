<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('hire_requisitions', function (Blueprint $table) {
            $table->id();
            $table->date('requisition_date');
            $table->string('department');
            $table->string('position');
            $table->string('salary_level');
            $table->decimal('salary_range_from', 10, 2);
            $table->decimal('salary_range_to', 10, 2);
            $table->boolean('approved')->default(false);
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('hire_requisitions');
    }
};
