import { useQuery } from "@tanstack/react-query";
import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import api from "../../../api/config/axiosConfig";
import {
  Box,
  Button,
  CircularProgress,
  Typography,
  useTheme,
} from "@mui/material";
import Header from "../../../components/Header";
import { DataGrid } from "@mui/x-data-grid";
import { tokens } from "../../../utils/theme";
import { useState } from "react";
import { useStateContext } from "../../../context/ContextProvider";
import { useEmployeeData } from "../../../api/userApi/clinetHook";

function LeaveTakenDetail() {
  const location = useLocation();
  const data = location.state;
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [rows, setRows] = useState([]);
  const { setNotification } = useStateContext();
  const navigate = useNavigate();
  const {data:emp}=useEmployeeData(data.emp_basic_id)
  const options = {
    weekday: "short",
    month: "short",
    day: "numeric",
    year: "numeric",
  };
  const {
    data: leaveDetail,
    isLoading,
    isFetched,
  } =
   useQuery(
    ["leaveDetail", data.emp_basic_id],
    () =>
      api.get(`leave_detail/${data.emp_basic_id}`).then(({ data }) => {
        setRows(
          data.map((row) => ({
            ...row,
            from_date: new Date(row.from_date).toLocaleDateString(
              "en-US",
              options
            ),
            reg_date: new Date(row.reg_date).toLocaleDateString(
              "en-US",
              options
            ),
            to_date: new Date(row.to_date).toLocaleDateString("en-US", options),
          }))
        );
        return data;
      }),
    {
      enabled: !!data.emp_basic_id,
    }
  );
  const columns = [
    {
      field: "id",
      headerName: "ID",
    },
    {
      field: "leave_taken",
      headerName: "LeaveTaken",
    },
    {
      field: "from_expired",
      headerName: "FromExpired",
    },
    {
      field: "from_date",
      headerName: "startDate",
      flex: 1,
    },
    {
      field: "to_date",
      headerName: "toDate",
      flex: 1,
    },
    {
      field: "reg_date",
      headerName: "RegistrationDate",
      flex: 1,
    },
    {
      field: "reason",
      headerName: "Reason",
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 1,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          navigate(`/employees/add-leaves/${params.row.id}`,{
            state:data
          });
        };
        return (
          <Box
            width="100%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "remove",
      headerName: "Remove",
      flex: 1,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          api.delete(`leave/${params.row.id}`).then(() => {
            setRows(rows.filter((row) => row.id !== params.row.id));
            setNotification("leave record deleted successfully");
          });
        };
        return (
          <Box
            width="100%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                remove
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];
  return (
    <Box m={2} maxWidth="90%">
      <Header
        title="List Of Leave Taken Until Now"
        subtitle={
          isFetched && `Employee Name: ${emp.first_name} ${emp.middle_name} ${emp.last_name}`
        }
      />
      {isLoading ? (
        <CircularProgress />
      ) : (
        <DataGrid columns={columns} rows={rows} autoHeight />
      )}
    </Box>
  );
}

export default LeaveTakenDetail;
