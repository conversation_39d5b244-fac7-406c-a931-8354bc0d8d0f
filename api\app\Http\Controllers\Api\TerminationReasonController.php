<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\TerminationReason;
use Illuminate\Http\Request;

class TerminationReasonController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(TerminationReason::all());
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();

        $result = TerminationReason::create($data);

        return response('success', 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\TerminationReason  $terminationReason
     * @return \Illuminate\Http\Response
     */
    public function show(TerminationReason $terminationReason)
    {
        return response($terminationReason);
    }


    public function update(Request $request, TerminationReason $terminationReason)
    {

        $terminationReason->update($request->all());

        return response($terminationReason);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\TerminationReason  $terminationReason
     * @return \Illuminate\Http\Response
     */
    public function destroy(TerminationReason $terminationReason)
    {
        $terminationReason->delete();

        return response('', 204);
    }
}
