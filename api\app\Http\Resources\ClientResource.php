<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ClientResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return parent::toArray($request);
        // return [
        //     'id' => $this->id,
        //     'company_name' => $this->company_name,
        //     'first_name' => $this->first_name,
        //     'last_name' => $this->last_name,
        //     'company_code' => $this->company_code,
        //     'sh_id' => $this->sh_id,
        //     'tel' => $this->tel,
        // ];
    }
}
