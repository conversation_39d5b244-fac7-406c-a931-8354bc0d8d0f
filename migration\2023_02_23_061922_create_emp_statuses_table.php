<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('emp_statuses', function (Blueprint $table) {
            //provident fund and pension status are already defiend in emp basic table
            $table->id();
            $table->unsignedBigInteger('emp_basic_id');
            $table->foreign('emp_basic_id')->references('id')->on('emp_basics')->onDelete('cascade');
            $table->tinyInteger('certificate_medical')->nullable();
            $table->tinyInteger('marital_status')->nullable();
            $table->tinyInteger('driving_license')->nullable();
            $table->tinyInteger('criminal_record')->nullable();
            $table->tinyInteger('living_certificate')->nullable();
            $table->tinyInteger('labor_union');
            $table->tinyInteger('credit_association');
            $table->tinyInteger('sport_association');
            $table->tinyInteger('social_support')->nullable();
            $table->tinyInteger('finggure_print');
            $table->tinyInteger('holiday');
            $table->string('eng_lang')->nullable();
            $table->string('user_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('emp_statuses');
    }
};
