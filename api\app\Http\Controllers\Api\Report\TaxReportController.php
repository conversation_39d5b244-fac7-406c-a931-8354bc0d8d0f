<?php

namespace App\Http\Controllers\Api\Report;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\settings\PayrollDate;

class TaxReportController extends Controller
{
    /**
     * Generates a query to retrieve payroll information for active employees.
     *
     * Joins the payrolls table with emp_basics and clients tables to select
     * various attributes such as employee's full name, ID, TIN number, start
     * date, basic salary, and client's company details. It also calculates
     * pension and provident totals, as well as non-taxable allowances.
     * Filters the results to include only employees with an active
     * termination status.
     *
     * @return \Illuminate\Database\Query\Builder The query builder instance
     *     for fetching the payroll information.
     */
    public function query()
    {
        $query = DB::table('payrolls')
            ->leftJoin('emp_basics', 'payrolls.emp_basic_id', '=', 'emp_basics.id')
            ->join('clients', 'payrolls.client_id', '=', 'clients.id')
            ->leftJoin('tax_centers', 'clients.tax_center', '=', 'tax_centers.id')
            ->leftJoin('emp_banks', 'payrolls.emp_basic_id', '=', 'emp_banks.emp_basic_id')
            ->where('emp_basics.termination_status', '=', 0)
            ->select(
                'emp_basics.id',
                'emp_basics.tin_no',
                'emp_basics.start_date',
                'emp_basics.basic_salary',
                DB::raw("CONCAT(emp_basics.first_name,' ', COALESCE(emp_basics.middle_name, '')) as fullname"),
                'clients.company_name',
                'clients.company_code',
                'clients.tax_center',
                'payrolls.pay_date',
                'payrolls.pension_emp',
                'payrolls.pension_comp',
                'payrolls.provident_emp',
                'payrolls.provident_sns',
                DB::raw('ROUND((payrolls.pension_emp+payrolls.pension_comp), 2) AS pension_total'),
                DB::raw('ROUND((payrolls.provident_emp+payrolls.provident_sns), 2) AS provident_total'),
                'payrolls.net_pay',
                'payrolls.transport_allowance',
                'payrolls.income_tax',
                'payrolls.gross_salary',
                'tax_centers.tax_center_name',
                'payrolls.*',
                'payrolls.id as payroll_id'
            );
        return $query;
    }
    /**
     * This function will return the income tax report 
     * based on the filter conditions provided in the request
     * @param Request $request
     * @return \Illuminate\Http\Response
     */

    public function incomeTaxReport(Request $request)
    {
        $firstDate = $request->input('firstDate');
        $lastDate = $request->input('lastDate');
        $clientId = $request->input('clientId');
        $year = $request->input('year');
        $taxCenter = $request->input('taxCenter');
        $company = $request->input('company');
        $status = $request->input('status');
        $currentPayrollDate = $request->input('current');
        $payrollDate = PayrollDate::latest()->first();
        $region = $request->input('region');
        $bankCode = $request->input('bankCode');

        //Log::info('incomeTaxReport: request: ' . json_encode($request->all()));

        $nextYear = Carbon::parse($year)->addYear()->format('Y-m-d');

        $query = $this->query();

        if ($status != 1) {
            // Apply additional conditions only if status is not 1
            if ($firstDate && $lastDate) {
                $query->whereBetween('payrolls.pay_date', [$firstDate, $lastDate]);
                //log::info('pay date', [$firstDate, $lastDate, $query]);
            }
            if ($clientId) {
                $query->where('payrolls.client_id', $clientId);
            }
            if ($company) {
                $query->where('clients.company_code', 'like', "%$company%");
            }
            if ($taxCenter) {
                $query->where('clients.tax_center', $taxCenter);
            }
            if ($year) {
                $query->whereBetween('payrolls.pay_date', [$year, $nextYear]);
            }
            if ($currentPayrollDate != 0) {
                $query->whereBetween('payrolls.pay_date', [$payrollDate['start_date'], $payrollDate['end_date']]);
            }
            if ($region) {
                $query->where('clients.region', 'like', "%$region%");
            }
            if ($bankCode) {
                $query->where('emp_banks.bank_code', 'like', "%$bankCode%");
            }
        }

        $results = $query->get();

        Log::info('incomeTaxReport: results count: ' . count($results));

        return response($results);
    }


    public function pensionTaxReport(Request $request)
    {
        //Log::info('pensionTaxReport: request: ' . json_encode($request->all()));

        $firstDate = $request->input('firstDate');
        $lastDate = $request->input('lastDate');
        $clientId = $request->input('clientId');
        $year = $request->input('year');
        $taxCenter = $request->input('taxCenter');
        $company = $request->input('company');
        $status = $request->input('status');
        $currentPayrollDate = $request->input('current');
        $region = $request->input('region');
        $payrollDate = PayrollDate::latest()->first();
        $bankCode = $request->input('bankCode');

        //Log::info('incomeTaxReport: request: ' . json_encode($request->all()));

        $nextYear = Carbon::parse($year)->addYear()->format('Y-m-d');

        // if ($startDate === null && $endDate === null && $taxCenter === null) {
        //     Log::error('pensionTaxReport: startDate, endDate, and taxCenter are required');
        //     return response()->json(['error' => 'startDate, endDate, and taxCenter are required'], 400);
        // }

        $query = $this->query();

        if ($status != 1) {
            if ($firstDate && $lastDate) {
                $query->whereBetween('payrolls.pay_date', [$firstDate, $lastDate]);
                //log::info('pay date', [$firstDate, $lastDate, $query]);
            }
            if ($clientId) {
                $query->where('payrolls.client_id', $clientId);
            }
            if ($company) {
                $query->where('clients.company_code', 'like', "%$company%");
            }
            if ($taxCenter) {
                Log::info('Filtering by tax center: ' . $taxCenter);
                $query->where('clients.pension_center', '=', $taxCenter);
            }
            if ($year) {
                $query->whereBetween('payrolls.pay_date', [$year, $nextYear]);
            }
            if ($currentPayrollDate != 0) {
                $query->whereBetween('payrolls.pay_date', [$payrollDate['start_date'], $payrollDate['end_date']]);
            }
            if ($region) {
                $query->where('clients.region', 'like', "%$region%");
            }
            if ($bankCode) {
                $query->where('emp_banks.bank_code', 'like', "%$bankCode%");
            }
        }
        //Log::info('pensionTaxReport: query: ' . json_encode($query->toSql()));

        $results = $query->where('payrolls.pension_emp', '>', 0)->get();
        
        if ($results === null) {
            //Log::error('pensionTaxReport: No records found');
            return response()->json(['error' => 'No records found'], 404);
        }

       // Log::info('pensionTaxReport: results: ' . json_encode($results));
        return response($results);
    }


}