<?php

namespace App\Http\Controllers\Api\settings;

use App\Http\Controllers\Controller;
use App\Models\settings\Provident;
use Illuminate\Http\Request;

class ProvidentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(Provident::latest()->first());
    }



    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = Provident::create([
            'provident_emp' => $request->input('provident_emp'),
            'provident_sns' => $request->input('provident_sns')
        ]);

        return response($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\settings\Provident  $provident
     * @return \Illuminate\Http\Response
     */
    public function show(Provident $provident)
    {
        return response($provident);
    }



    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\settings\Provident  $provident
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request,  $id)
    {
        $provident=Provident::find($id);
        $provident->update([
            'provident_emp' => $request->input('provident_emp'),
            'provident_sns' => $request->input('provident_sns')
        ]);

        return response($provident);
    }
}
