import { Navigate, useParams } from "react-router-dom";
import { useStateContext } from "../../context/ContextProvider";
import api from "../config/axiosConfig";

export const register = (payload, setErrors, setNotification, checkedItems) => {

  api
    .post("users", payload, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    })
    .then(({ data }) => {

      if (data.data.userPermission) {
        api
          .put(`permissions/${data.data.permissionId}`, {
            user_id: data.data.id,
            routes: JSON.stringify(checkedItems),
            roles: data.data.role,
          })
          .then(() => {
            setNotification("user is created successfully");
          });
      } else {
        api
          .post("permissions", {
            user_id: data.data.id,
            routes: JSON.stringify(checkedItems),
            roles: data.data.role,
          })
          .then(() => {
            setNotification("user is created successfully");
          });
      }
    })
    .catch((err) => {
      if (err.response && err.response.status === 422) {
        if (err.response.data.errors) {
          setErrors(err.response.data.errors);
        }
      }
    });
};

const login = (payload, setToken, setUser, setErrors) => {
  api
    .post("login", payload)
    .then(({ data }) => {
      setUser(data.user);
      setToken(data.token);
    })
    .catch((err) => {
      if (err.response && err.response.status === 422) {
        if (err.response.data.errors) {
          setErrors(err.response.data.errors);
        } else {
          setErrors({
            email: [err.response.data.message],
          });
        }
      } else {
        setErrors({
          connection: ["net::ERR_CONNECTION_REFUSED  PLEASE CHECK THE SERVER"],
        });
      }
    });
};
export const logout = (setUser, setToken) => {
  api
    .delete("/logout")
    .then(() => {
      setUser({});
      setToken(null);
    })
    .catch((err) => {
      console.error(err);
    });
};
export default login;
