<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AssignResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return parent::toArray($request);

        // return [
        //     'id'=>$this->id,
        //     'client_id'=>$this->client_id,
        //     'emp_basic_id'=>$this->emp_basic_id,
        //     'position'=>$this->position
        // ];
    }
}
