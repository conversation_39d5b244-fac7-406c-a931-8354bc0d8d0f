import React from "react";
import Header from "../../components/Header";
import {
  Box,
  Button,
  CircularProgress,
  Typography,
  useTheme,
} from "@mui/material";
import { tokens } from "../../utils/theme";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import api from "../../api/config/axiosConfig";
import { DataGrid } from "@mui/x-data-grid";
import { useStateContext } from "../../context/ContextProvider";

function ListOfClientDeduct({ selectedRow, clientId }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const queryClient = useQueryClient();
  const { setNotification } = useStateContext();
  const { data: deduct, isLoading } = useQuery(
    ["ded", clientId],
    () => api.get(`client-deduct/${clientId}`).then(({ data }) => data),
    {
      staleTime: 600000,
      enabled: !!clientId,
    }
  );
  const columns = [
    {
      field: "id",
      headerName: "ID",
    },
    {
      field: "labor_union",
      headerName: "laborUnion",
    },
    {
      field: "creadit_association",
      headerName: "creaditAssociation",
    },
    {
      field: "sport_association",
      headerName: "sportAssociation",
    },
    {
      field: "type",
      headerName: "Type",
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 0.7,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          selectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "delete",
      headerName: "Delete",
      flex: 0.8,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deleteDeduct = useMutation(
          (id) => api.delete(`client-deduct/${id}`),
          {
            onSuccess: () => {
              queryClient.invalidateQueries({ queryKey: "ded" });
              setNotification("ClientDeduct record deleted successfully");
            },
          }
        );
        const deleteIt = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              "Are you sure you want to delete the selected record?"
            )
          ) {
            return;
          }

          deleteDeduct.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deleteIt}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];
  return (
    <Box>
      <Header title="List of Client deductable" heading="h5" />
      {isLoading ? (
        <CircularProgress />
      ) : (
        <DataGrid rows={deduct} columns={columns} autoHeight hideFooter />
      )}
    </Box>
  );
}

export default ListOfClientDeduct;
