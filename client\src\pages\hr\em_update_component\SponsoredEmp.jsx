import { <PERSON>rid, Box, Button, TextField } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React from "react";
import { useEffect } from "react";
import { useState } from "react";
import api from "../../../api/config/axiosConfig";
import { useStateContext } from "../../../context/ContextProvider";

function SponsoredEmp({ id }) {
  const { errors, setErrors, setNotification, user } = useStateContext();
  const [isEmp, setIsEmp] = useState(false);

  const [formData, setFormData] = useState({
    emp_basic_id: "",
    name: "",
    company_name: "",
    phone: "",
    office_phone: "",
    effective_date: "",
    expire_date: "",
    user_id: user.id
  });

  useEffect(() => {
    setFormData({
      ...formData,
      user_id: user.id
    })
  }, [user])

  if (id) {
    useEffect(() => {
      api
        .get(`emp_basics/${id}?all=true`, { params: { all: 'sponserd' } })
        .then(({ data }) => {
          if (data.data.sponsored_employee) {
            setFormData(data.data.sponsored_employee);
            setIsEmp(true);
          }
        });
    }, [id]);
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const updateSponserdEmployee = useMutation(
    (formdata) => api.put(`sponsord_emp/${formdata.id}`, formdata),
    {
      onSuccess: () => {
        setNotification("updated successfully");
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );

  const addSponsoredEmployee = useMutation(
    (payload) => api.post("sponsord_emp", payload),
    {
      onSuccess: () => {
        setNotification("created Successfully");
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );

  const handleSubmit = (e) => {
    e.preventDefault();
    if (isEmp) {
      updateSponserdEmployee.mutate(formData);
    } else {
      const data = new FormData(e.currentTarget);

      const payload = {
        emp_basic_id: id,
        name: data.get('name'),
        company_name: data.get('company_name'),
        phone: data.get('phone'),
        office_phone: data.get('office_phone'),
        effective_date: data.get('effective_date'),
        expire_date: data.get('expire_date'),
        user_id: user.id,
      };
      
      addSponsoredEmployee.mutate(payload);
    }
  };
  
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.name == ""}
            margin="normal"
            label="Full Name"
            name="name"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.name}
            helperText={errors.name ? errors.name[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.company_name == ""}
            margin="normal"
            label="Comapny Name"
            name="company_name"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.company_name}
            helperText={errors.company_name ? errors.company_name[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.phone == ""}
            margin="normal"
            label="Phone Number"
            name="phone"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.phone}
            helperText={errors.phone ? errors.phone[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.office_phone == ""}
            margin="normal"
            label="Office Number"
            name="office_phone"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.office_phone}
            helperText={errors.office_phone ? errors.office_phone[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.effective_date == ""}
            margin="normal"
            label="Effective Date"
            name="effective_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.effective_date}
            helperText={errors.effective_date ? errors.effective_date[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.expire_date == ""}
            margin="normal"
            label="Expire Date"
            name="expire_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.expire_date}
            helperText={errors.expire_date ? errors.expire_date[0] : null}
          />
        </Grid>
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
          {isEmp ? <div>update</div> : <div>create</div>}
        </Button>
      </Box>
    </Box>
  );
}

export default SponsoredEmp;
