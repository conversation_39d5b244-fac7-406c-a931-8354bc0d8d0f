import { useQuery, useQueryClient } from "@tanstack/react-query";
import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import api from "../../../api/config/axiosConfig";
import { tokens } from "../../../utils/theme.js";
import { useStateContext } from "../../../context/ContextProvider";
import _CircularProgress from "../../../components/_CircularProgress";
import {
  Box,
  Button,
  CircularProgress,
  Container,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import Header from "../../../components/Header";

import { DataGrid } from "@mui/x-data-grid";

function CalculatePayroll() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const location = useLocation();
  const empobj = location.state;
  const [rows, setRows] = useState([]);
  const [issueDate, setIssueDate] = useState(new Date());
  const { setNotification, user } = useStateContext();
  const [isdate, setIsdate] = useState(true);
  const queryClient = useQueryClient();
  const [month, setMonth] = useState("");
  const [year, setYear] = useState("");
  const [companyName, setCompanyName] = useState("");
  const navigate = useNavigate();


  const {
    data: getdata,
    isLoading,
    isFetched,
  } = useQuery(
    ["calcPayroll"],
    () =>
      api
        .get("getpayroll", {
          params: { emp_id: empobj.emp_id, client_id: empobj.clientId },
        })
        .then(({ data }) => {
          const transformedData = data.map((row, index) => ({
            ...row,
            id: index + 1,
            full_name: `${row.first_name} ${row.middle_name} ${row.last_name}`,
          }));
          
          const firstRow = transformedData[0];
          setMonth(firstRow.month);
          setYear(firstRow.year);
          setCompanyName(firstRow.company_name);
  
          setRows(transformedData);
          console.log(transformedData);
  
          return transformedData;
        }),
    {
      enabled: !!empobj.clientId,
    }
  );
  

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
    },
    {
      field: 'full_name',
      headerName: 'FullName',
      width: '200',
    },
    {
      field: 'emp_start_date',
      headerName: 'StartDate',
    },
    {
      field: 'basic_salary',
      headerName: 'Basic Salary',
    },
    {
      field: 'over_time',
      headerName: 'overtime',
    },
    {
      field: 'transport_allowance',
      headerName: 'Transport Allowance',
    },
    {
      field: 'provident',
      headerName: 'Has provident',
    },
    {
      field: 'pension',
      headerName: 'has Pension',
    },
    {
      field: 'emp_client_additional',
      headerName: 'Client Additional',
    },
    {
      field: 'total_work_duration',
      headerName: 'Total WorkDuration',
    },
    {
      field: 'absence',
      headerName: 'Total Absence Duration',
    },
    {
      field: 'total_overtime_hours',
      headerName: 'Total Overtime Hours',
    },
    {
      field: 'total_additional_hours',
      headerName: 'Total Additional Hours',
    },
    {
      field: 'total_no_pc',
      headerName: 'Total No Of PC',
    },
    {
      field: 'pc_rate_type',
      headerName: 'PcRate Type',
    },
    {
      field: 'pcrate',
      headerName: 'PcRate',
    },
    {
      field: 'total_pcrate_salary',
      headerName: 'PcRate Salary',
    },
    {
      field: 'add_pc_rate_type',
      headerName: 'Additional PcrateType',
    },
    {
      field: 'add_pcrate',
      headerName: 'Additional Pcrate',
    },
    {
      field: 'total_add_pcrate_salary',
      headerName: 'Total Additional PCRate Salary',
      // width:'150'
    },
    {
      field: 'additional_nontaxable_allowance',
      headerName: 'Additional NonTaxable Allowance',
    },
    {
      field: 'nontax_transport_allowance',
      headerName: 'NonTax Transport Allowance',
    },
    {
      field: 'nontax_desert_allowance',
      headerName: 'NonTax Desert Allowance',
    },
    {
      field: 'nontax_mobile_allowance',
      headerName: 'NonTax Mobile Allowance',
    },
    {
      field: 'nontax_cleaning_allowance',
      headerName: 'NonTax Cleaning Allowance',
    },
    {
      field: 'nontax_other_allowance',
      headerName: 'NonTax Other Allowance',
    },
    {
      field: 'nontax_position_allowance',
      headerName: 'NonTax Position Allowance',
    },
    {
      field: 'nontax_dsa_allowance',
      headerName: 'NonTax Dsa Allowance',
    },
    {
      field: 'total_taxable_dsa_salary',
      headerName: 'Taxable Dsa Allowance',
    },
    {
      field: 'sport_association',
      headerName: 'Sport Association',
    },
    {
      field: 'creadit_association',
      headerName: 'Creadit Association',
    },
    {
      field: 'social_support',
      headerName: 'social Support',
    },
    {
      field: 'labor_union',
      headerName: 'Labor Union',
    },
    {
      field: 'medical_cost',
      headerName: 'MedicalLoan',
    },
    {
      field: 'loan',
      headerName: 'Company Loan',
    },
    {
      field: 'labor_loan',
      headerName: 'Labor Loan',
    },
    {
      field: 'costsharing_loan',
      headerName: 'Cost Sharing Loan',
    },
    {
      field: 'other_loan',
      headerName: 'other Loan',
    },

    {
      field: 'trans_ded',
      headerName: 'Transport Deduction',
    },
    {
      field: 'total_add_Deduction',
      headerName: 'Total Additional Deduction',
    },
    // {
    //   field: "total_add_reason",
    //   headerName: "TotalAddReason",
    // },
    {
      field: 'taxable_transport_allowance',
      headerName: 'Taxable Transport Allowance',
    },
    {
      field: 'taxable_housing_allowance',
      headerName: 'Taxable Housing Allowance',
    },
    {
      field: 'taxable_position_allowance',
      headerName: 'Taxable Position Allowance',
    },
    {
      field: 'taxable_other_allowance',
      headerName: 'Taxable Other Allowance',
    },
    {
      field: 'pensionEmp',
      headerName: 'pension Emp',
    },
    {
      field: 'pensionComp',
      headerName: 'pension Comp',
    },
    {
      field: 'providentEmp',
      headerName: 'provident Emp',
    },
    {
      field: 'providentComp',
      headerName: 'provident Comp',
    },
    {
      field: 'incomeTax',
      headerName: 'incomeTax',
    },
    {
      field: 'employee_medical',
      headerName: 'Employee Medical',
    },
    {
      field: 'absence_deduction_summry',
      headerName: 'Absence Deduction Summry',
    },
    {
      field: 'total_additional_ded_summry',
      headerName: 'Total Additional Deduction Summry',
    },
    {
      field: 'sum_taxable_income',
      headerName: 'Sum Taxable Income',
      cellClassName: 'custom-tax',
      headerClassName: 'custom-tax',
    },

    {
      field: 'non_taxable_income_summry',
      headerName: 'Non Taxable Income Summry',
      cellClassName: 'custom-non-tax',
      headerClassName: 'custom-non-tax',
    },
    {
      field: 'total_deduction_summry',
      headerName: 'Total Deduction Summry',
      cellClassName: 'custom-ded',
      headerClassName: 'custom-ded',
    },
    {
      field: 'net_payment',
      headerName: 'Net Payment',
      cellClassName: 'custom-pay',
      headerClassName: 'custom-pay',
    },
    {
      field: 'remove',
      headerName: 'Remove',
      headerClassName: 'custom-pay-header',
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          setRows(rows.filter((row) => row.id !== params.row.id));
        };
        return (
          <Box
            width="100%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                remove
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];

  const submitList = () => {
    const payload = rows.map((row) => ({
      ...row,
      user_id: user.id,
      pay_date: issueDate,
    }));
    api.post("payroll", payload).then(() => {
      setNotification("payroll is saved Successfully");
      queryClient.invalidateQueries({ queryKey: ["getpayroll"] });
      setTimeout(() => {
        navigate("/finance/payroll");
      }, 1000);
    });
  };
  return (
    <Box m="10px">
      {/* <Header title="Prepared Pyaroll List" /> */}
      <Header
            title={`PREPARED PAYROLL LIST  FOR THE MONTH OF ${month} ${year}`}
            subtitle={`Location: ${companyName}`}
          />
      {isLoading && <CircularProgress />}
      {isFetched && (
        <Box
          sx={{
            "& .MuiDataGrid-columnHeaderTitle": {
              whiteSpace: "normal",
              wordWrap: "break-word",
              overflowWrap: "normal",
              lineHeight: "normal",
            },
            "& .MuiDataGrid-columnHeader": {
              height: "unset !important",
              paddingX: "20px",
              width:'unset !important',
            },
          }}
        >
          <DataGrid
            columns={columns}
            rows={rows}
            hideFooter
            autoHeight
            headerHeight={100}
          />
          <Box display="flex" justifyContent="flex-end" marginTop="16px">
            <TextField
              label="issue date"
              type="date"
              name="issue_date"
              value={issueDate}
              onChange={(e) => {
                const date = new Date(e.target.value);
                const formattedDate = date.toISOString().split("T")[0];
                setIssueDate(formattedDate);
                setIsdate(false);
              }}
            />
            <Button
              onClick={submitList}
              variant="contained"
              color="success"
              sx={{ ml: "16px", paddingX: "40px" }}
              disabled={isdate}
            >
              <Typography color={colors.grey[100]}>save</Typography>
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
}

export default CalculatePayroll;
