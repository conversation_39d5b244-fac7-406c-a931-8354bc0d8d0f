<?php

namespace App\Http\Controllers;

use App\Models\CollectedPayment;
use App\Models\PaymentFollowUp;
use Carbon\Carbon;
use Illuminate\Http\Request;

class CollectedPaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
        $collectedPayment = CollectedPayment::all();
        return response()->json($collectedPayment);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'contract_id' => 'required|exists:payment_follow_ups,id',
            'client_id' => 'required|exists:clients,id',
            'collector_name' => 'required|string',
            'amount' => 'required|numeric',
            'collection_date' => 'required|date',
            'collection_type' => 'required|string',
            'comment' => 'nullable|string',
            'user_id' => 'required|exists:users,id',
        ]);

        // Retrieve the existing PaymentFollowUp
        $contract = PaymentFollowUp::findOrFail($validated['contract_id']);

        // Update the status to 'Collected'
        $contract->update(['status' => 'Collected']);

        // Create the CollectedPayment record
        $collectedPayment = CollectedPayment::create($validated);

        // Return the response
        return response()->json([
            'collected_payment' => $collectedPayment,
            'new_payment_follow_up' => $contract,
        ], 201);
    }


    /**
     * Display the specified resource.
     *
     * @param  \App\Models\CollectedPayment  $collectedPayment
     * @return \Illuminate\Http\Response
     */
    public function show(CollectedPayment $collectedPayment)
    {
        //
        return response()->json($collectedPayment);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\CollectedPayment  $collectedPayment
     * @return \Illuminate\Http\Response
     */
    public function edit(CollectedPayment $collectedPayment)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\CollectedPayment  $collectedPayment
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, CollectedPayment $collectedPayment)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\CollectedPayment  $collectedPayment
     * @return \Illuminate\Http\Response
     */
    public function destroy(CollectedPayment $collectedPayment)
    {
        //
    }
}
