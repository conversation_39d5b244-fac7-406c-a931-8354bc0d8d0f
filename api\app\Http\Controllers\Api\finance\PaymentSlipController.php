<?php

namespace App\Http\Controllers\Api\finance;

use App\Http\Controllers\Api\helper\PayrollHelper;
use App\Http\Controllers\Controller;
use App\Models\finance\Payroll;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentSlipController extends Controller
{

    public $basicSalaray;

    public function __construct()
    {

        $this->basicSalaray = 0;
    }
    public function getSlip($id)
    {
        $collect = collect([]);
        $phelper = new PayrollHelper();
        $paydate = $phelper->setPayrollDate();
        $payrolls = DB::table('payrolls')->select(
            'payrolls.emp_basic_id',
            'emp_basics.first_name as first_name',
            'emp_basics.middle_name as middle_name',
            'emp_basics.last_name as last_name',
            'clients.company_name',
            'payrolls.basic_salary',
            'payrolls.transport_allowance',
            'payrolls.over_time',
            'payrolls.absence',
            'payrolls.pension_emp',
            'payrolls.pension_comp',
            'payrolls.provident_emp',
            'payrolls.provident_sns',
            'payrolls.income_tax',
            'payrolls.gross_salary',
            'payrolls.nontax_dsa_allowance',
            'payrolls.tax_dsa_allowance',
            DB::raw('(payrolls.nontax_transport_allowance - payrolls.nontax_medical_allowance + payrolls.nontax_desert_allowance + payrolls.nontax_position_allowance + payrolls.nontax_other_allowance + payrolls.nontax_mobile_allowance + payrolls.nontax_cleaning_allowance + payrolls.nontax_dsa_allowance + payrolls.additional_nontax_allowance) AS nontax_allowance'),
            'payrolls.net_pay',
            'payrolls.other_transport_allowance',
            'payrolls.total_deduction',
            'payrolls.additional',
            'payrolls.nontax_medical_allowance',
        )->join('emp_basics', 'emp_basics.id', '=', 'payrolls.emp_basic_id')->join('clients', 'clients.id', '=', 'payrolls.client_id')->where('client_id', $id)->whereBetween('payrolls.pay_date', [$paydate['startDate'], $paydate['endDate']])->get();
        foreach ($payrolls as $payroll) {
            $getPcRate = $phelper->getPcRate($payroll->emp_basic_id, $paydate['startDate'], $paydate['endDate']);
            $this->basicSalaray = $payroll->basic_salary + $getPcRate['totalNoPc'] * $getPcRate['pcRate'];
            $object = [
                'emp_basic_id' => $payroll->emp_basic_id,
                'first_name' => $payroll->first_name,
                'middle_name' => $payroll->middle_name,
                'last_name' => $payroll->last_name,
                'company_name' => $payroll->company_name,
                'total_basic_salary' => $this->basicSalaray,
                'basic_salary' => $payroll->basic_salary,
                'transport_allowance' => $payroll->transport_allowance,
                'over_time' => $payroll->over_time,
                'absence' => $payroll->absence,
                'pension_emp' => $payroll->pension_emp,
                'pension_comp' => $payroll->pension_comp,
                'provident_emp' => $payroll->provident_emp,
                'provident_sns' => $payroll->provident_sns,
                'income_tax' => $payroll->income_tax,
                'gross_salary' => $payroll->gross_salary,
                'nontax_dsa_allowance' => $payroll->nontax_dsa_allowance,
                'tax_dsa_allowance' => $payroll->tax_dsa_allowance,
                'pc_rate' => $getPcRate['pcRate'],
                'no_pc' => $getPcRate['totalNoPc'],
                'pc_type' => $getPcRate['pcType'],
                'net_pay'=>$payroll->net_pay,
                'other_transport_allowance'=>$payroll->other_transport_allowance,
                'total_deduction'=>$payroll->total_deduction,
                'additional'=>$payroll->additional,
                'nontax_medical_allowance'=>$payroll->nontax_medical_allowance,
                'deffrent_deduction'=>$payroll->absence,
                'nontax_allowance'=>$payroll->nontax_allowance
            ];

            $collect->push($object);
        }
        return response($collect);
    }
}


