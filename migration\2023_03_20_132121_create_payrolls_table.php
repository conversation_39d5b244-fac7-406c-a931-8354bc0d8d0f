<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

ini_set('memory_limit', '1024M');

return new class extends Migration
{

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payrolls', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('emp_basic_id');
            $table->unsignedBigInteger('client_id');
            $table->foreign('emp_basic_id')->references('id')->on('emp_basics')->onDelete('cascade');
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->string('total_work_duration')->default(256);
            $table->float('basic_salary')->nullable();
            $table->float('all_holiday')->nullable();
            $table->float('transport_allowance')->nullable();
            $table->float('over_time')->nullable();
            $table->float('absence')->default(0);
            $table->float('medical_cost')->nullable();
            $table->float('loan')->default(0);
            $table->float('other_loan')->nullable();
            $table->float('labor_union')->nullable();
            $table->float('social_supprot')->default(0);
            $table->float('creadit_association')->default(0);
            $table->float('sport_association')->default(0);
            $table->float('additional')->default(0);
            $table->float('provident_emp')->default(0);
            $table->float('provident_sns')->default(0);
            $table->float('income_tax')->default(0);
            $table->float('gross_salary')->default(0);
            $table->float('total_deduction')->default(0);
            $table->float('net_pay')->default(0);
            $table->float('other_transport_allowance')->default(0);
            $table->float('transport_allowance_deduction')->default(0);
            $table->float('lw_out_pay_ded')->default(0);
            $table->text('add_ded_reason')->nullable();
            $table->text('oth_loan_reason')->nullable();
            $table->date('pay_date')->format('Y-m-d')->nullable();
            $table->float('pension_emp')->default(0);
            $table->float('pension_comp')->default(0);
            $table->float('pcrate_salary')->default(0);
            $table->float('addpcreate_salary')->default(0);
            $table->float('nontax_transport_allowance')->default(0);
            $table->float('nontax_desert_allowance')->default(0);
            $table->float('nontax_position_allowance')->default(0);
            $table->float('nontax_other_allowance')->default(0);
            $table->float('nontax_mobile_allowance')->default(0);
            $table->float('nontax_cleaning_allowance')->default(0);
            $table->float('nontax_dsa_allowance')->default(0);
            $table->float('nontax_medical_allowance')->default(0);
            $table->float('additional_nontax_allowance')->default(0);
            $table->float('housing_allowance')->default(0)->nullable();
            $table->float('tax_dsa_allowance')->default(0)->nullable();
            $table->float('exchange_rate')->default(0)->nullable();
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });

        // $tableName = 'payroll';
        // $databaseName='edomiasdb_backup';
        // $newColumnNames = [
        //     'PyId' => 'id','Cid' => 'client_id', 'Eid' => 'emp_basic_id', 'Total_Work_Duration' => 'total_work_duration', 'Basic_Salary' => 'basic_salary', 'Holiday_All' => 'all_holiday', 'Transport_Allowance' => 'transport_allowance', 'Over_Time' => 'over_time', 'Absence' => 'absence', 'Medical_Cost' => 'medical_cost', 'Loan' => 'loan', 'Other_Loan' => 'other_loan', 'Labor_Union' => 'labor_union', 'Social_Support' => 'social_supprot', 'Creadit_Association' => 'creadit_association', 'Sport_Association' => 'sport_association', 'Additional' => 'additional', 'Provident_Emp' => 'provident_emp', 'Provident_Sas' => 'provident_sns', 'Income_Tax' => 'income_tax', 'Gross_Salary' => 'gross_salary', 'Total_Deduction' => 'total_deduction', 'Net_Pay' => 'net_pay', 'Other_Transport_Allowance' => 'other_transport_allowance', 'Transport_Allowance_Dedu' => 'transport_allowance_deduction', 'LWoutPay_Ded' => 'lw_out_pay_ded', 'Add_ded_reason'=>'add_ded_reason', 'Oth_Loan_reason' => 'oth_loan_reason', 'Pay_Date' => 'pay_date', 'Pension_Emp' => 'pension_emp', 'Pension_Com' => 'pension_comp', 'Pcrate_Salary' => 'pcrate_salary', 'AddPcrate_Salary' => 'addpcreate_salary', 'NonTax_Transport_Allowance' => 'nontax_transport_allowance', 'NonTax_Desert_Allowance' => 'nontax_desert_allowance', 'NonTax_Position_Allowance' => 'nontax_position_allowance', 'NonTax_Other_Allowance' => 'nontax_other_allowance', 'NonTax_Mobile_Allowance' => 'nontax_mobile_allowance', 'NonTax_Cleaning_Allowance' => 'nontax_cleaning_allowance', 'Additional_NonTax_Allowance' => 'additional_nontax_allowance', 'NonTax_Dsa_Allowance' => 'nontax_dsa_allowance', 'Tax_Dsa_Allowance' => 'tax_dsa_allowance', 'Exchange_Rate' => 'exchange_rate', 'Uid' => 'user_id', 'NonTax_Medical_Allowance' => 'nontax_medical_allowance', 'Housing_Allowance' => 'housing_allowance',
        // ];
        // $sqlFilePath = Storage::path('payroll.sql');
        // $sqlFileContents = File::get($sqlFilePath);
        // DB::unprepared($sqlFileContents);
        // $mappedArray = [];
        // $rows = DB::table("$databaseName.$tableName")->get();
        // foreach ($rows as $row) {
        //     $mappedRow = [];
        //     foreach ($newColumnNames as $oldColumn => $newColumnName) {
        //         $mappedRow[$newColumnName] = $row->$oldColumn;
        //     }
        //     $mappedArray[] = $mappedRow;
        // }
        // DB::table('payrolls')->insert($mappedArray);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payrolls');
    }
};
