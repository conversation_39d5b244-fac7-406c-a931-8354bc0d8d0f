<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ExamScore;
use Illuminate\Http\Request;

class ExamScoreController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return ExamScore::all();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'applicant_id' => 'required|exists:applicants,id',
            'exam_date' => 'required|date',
            'score' => 'required|numeric',
        ]);

        $examScore = ExamScore::create($validatedData);
        return $examScore;
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        return ExamScore::find($id);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $examScore = ExamScore::find($id);

        $validatedData = $request->validate([
            'applicant_id' => 'sometimes|required|exists:applicants,id',
            'exam_date' => 'sometimes|required|date',
            'score' => 'sometimes|required|numeric',
        ]);

        $examScore->update($validatedData);
        return $examScore;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        return ExamScore::destroy($id);
    }
}
