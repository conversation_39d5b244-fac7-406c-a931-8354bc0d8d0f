<?php

namespace App\Http\Controllers\Api\store;

use App\Http\Controllers\Controller;
use App\Http\Resources\InventoryMovementResource;
use App\Models\InventoryMovement;
use App\Models\storeItems;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class InventoryMovementController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return InventoryMovementResource::collection(InventoryMovement::with(['item', 'creator', 'request'])->get());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'item_id' => 'required|exists:store_items,id',
            'movement_type' => 'required|in:inward,outward',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            $item = storeItems::findOrFail($request->item_id);

            if ($request->movement_type === 'inward') {
                $item->increment('stock_quantity', $request->quantity);
            } else {
                if ($item->stock_quantity < $request->quantity) {
                    throw new \Exception('Not enough stock available.');
                }
                $item->decrement('stock_quantity', $request->quantity);
            }

            $movement = InventoryMovement::create([
                'item_id' => $request->item_id,
                'movement_type' => $request->movement_type,
                'quantity' => $request->quantity,
                'notes' => $request->notes,
                'created_by' => Auth::id(),
            ]);

            DB::commit();

            return new InventoryMovementResource($movement);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(InventoryMovement $inventoryMovement)
    {
        return new InventoryMovementResource($inventoryMovement->load(['item', 'creator', 'request']));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, InventoryMovement $inventoryMovement)
    {
        // Inventory movements are generally not updated to maintain a clear audit trail.
        return response()->json(['message' => 'Inventory movements cannot be updated.'], 405);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(InventoryMovement $inventoryMovement)
    {
        $inventoryMovement->delete();

        return response()->noContent();
    }

}