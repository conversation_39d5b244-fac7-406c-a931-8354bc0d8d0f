import { Box, Grid, Button, TextField, MenuItem } from "@mui/material";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import React from "react";
import { useState } from "react";
import api from "../../api/config/axiosConfig";
import { useStateContext } from "../../context/ContextProvider";
import { useEffect } from "react";
const type = [
  {
    lable: "birr",
    value: "birr",
  },
  {
    lable: "% from salary",
    value: "labor",
  },
];
function DeductForm({ clientId, editData }) {
  const [deduct, setDeduct] = useState({
    labor_union: "",
    creadit_association: "",
    sport_association: "",
    type: "",
  });
  const { setNotification, user } = useStateContext();
  const queryClient = useQueryClient();
  const handleChange = (e) => {
    setDeduct({
      ...deduct,
      [e.target.name]: e.target.value,
    });
  };
  useEffect(() => {
    if (editData && editData.id) {
      setDeduct(editData);
    }
  }, [editData]);
  const createDeduct = useMutation((data) => api.post("client-deduct", data), {
    onSuccess: () => {
      setNotification("Client Deductable added successfully");
      queryClient.invalidateQueries({ queryKey: ["ded"] });
    },
  });
  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    const payload = {
      client_id: clientId,
      user_id: user.id,
      labor_union: data.get("labor_union"),
      creadit_association: data.get("creadit_association"),
      sport_association: data.get("sport_association"),
      type: data.get("type"),
    };
    if (editData && editData.id) {
      api.put(`/client-deduct/${editData.id}`, payload).then(() => {
        setNotification("deduct record updated successfully");
        queryClient.invalidateQueries({ queryKey: ["ded"] });
      });
    } else {
      createDeduct.mutate(payload);
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="LaborUnion"
            name="labor_union"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={deduct.labor_union ?? ""}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Sport Association"
            name="sport_association"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={deduct.sport_association}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Creadit Association "
            name="creadit_association"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={deduct.creadit_association}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Type"
            name="type"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={deduct.type ? deduct.type : "birr"}
          >
            {type.map((rate) => (
              <MenuItem key={rate.value} value={rate.value}>
                {rate.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
      </Grid>
      <span style={{ float: "inline-start" }}>
        <Button type="submit" variant="contained" color="success">
          {editData.id ? <>update</> : <>create</>}
        </Button>
      </span>
    </Box>
  );
}

export default DeductForm;
