<?php

namespace App\Http\Controllers\Api\settings;

use App\Http\Controllers\Controller;
use App\Models\settings\Pension;
use Illuminate\Http\Request;

class PensionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(Pension::latest()->first());
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
   

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data=Pension::create([
            'emp_pension'=>$request->input('emp_pension'),
            'comp_pension'=>$request->input('comp_pension'),

        ]);

        return response($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\settings\Pension  $pension
     * @return \Illuminate\Http\Response
     */
    public function show(Pension $pension)
    {
        return response($pension);
    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\settings\Pension  $pension
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request,  $id)
    {
        $pension=Pension::find($id);
        $pension->update([
            'emp_pension'=>$request->input('emp_pension'),
            'comp_pension'=>$request->input('comp_pension'),
        ]);

        return response($pension);
    }
   
}
