import { Box, Grid } from "@mui/material";
import React from "react";
import { useState } from "react";
import Header from "../../components/Header";
import LeftSide from "../../components/LeftSide";
import RightSide from "../../components/RightSide";
import ListOfTerminatedEmployee from "./ListOfTerminatedEmployee";
import TerminateEmployee from "./TerminateEmployee";

function Terminate() {
  const links = ["Terminate Employee", "List Of Terminated Employee"];
  const components = [TerminateEmployee, ListOfTerminatedEmployee];
  const [selectedLinkIndex, setSelectedLinkIndex] = useState(0);
  const handleLinkClick = (index) => {
    setSelectedLinkIndex(index);
  };
  return (
    <Box m="10px">
      <Header title="Terminate Employee" subtitle="terminate employee here" />
      <Grid container spacing={2}>
        <LeftSide
          links={links}
          handleLinkClick={handleLinkClick}
          activeLink={selectedLinkIndex}
          m={12}
        />
        <RightSide
          components={components}
          selectedLinkIndex={selectedLinkIndex}
        />
      </Grid>
    </Box>
  );
}

export default Terminate;
