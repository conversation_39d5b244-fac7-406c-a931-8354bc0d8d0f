import React from "react";
import { Navigate } from "react-router-dom";
import { useStateContext } from "../context/ContextProvider";
import { CircularProgress, Container } from "@mui/material";

function ProtectedRoute({ childern, roles,path}) {
  const { user } = useStateContext();
  const userRole = user.role;
  const paths=user.user_permission
  if (userRole == null) {
    return (
      <Container sx={{display:'flex',justifyContent:'center'}}>
        <CircularProgress />
      </Container>
    );
  }
  if (roles.includes(userRole) &&  paths[0].routes.includes(path)) {
    return childern;
  } else {
    return <Navigate to="/unauthorizedpage" />;
  }
}

export default ProtectedRoute;
