import React from "react";

import { Container } from "@mui/material";
import EducationalForm from "./EducationalForm";
import { useState } from "react";
import EducationalList from "./EducationalList";
function EducationalInformation({ id }) {
  const [editData, setEditData] = useState({});
  const onSelectRow = (value) => {
    setEditData(value);
  };

  return (
    <Container>
      <EducationalForm data={editData} empId={id} />
      <EducationalList id={id} selectedRow={onSelectRow} />
    </Container>
  );
}

export default EducationalInformation;
