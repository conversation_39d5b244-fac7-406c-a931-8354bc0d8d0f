<?php

namespace Database\Factories;

use App\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Client>
 */
class ClientFactory extends Factory
{

    protected $model=Client::class;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        
        return [
            'first_name'=>$this->faker->name() ,
            'last_name'=> $this->faker->name(),
            'company_name'=> $this->faker->name(),
            'company_code'=> $this->faker->name(),
            'city'=> $this->faker->name(),
            'sub_city'=> $this->faker->name(),
            'street_address1'=> $this->faker->name(),
            'street_address2'=> $this->faker->name(),
            'kebele'=> $this->faker->name(),
            'house_no'=> $this->faker->name(),
            'tel'=> $this->faker->name(),
            'residence'=> $this->faker->name(),
            'start_date'=> $this->faker->name(),
            'end_date'=> $this->faker->name(),
            'sh_id'=> $this->faker->name(),
            'guard_no'=> $this->faker->name(),
            'leader_no'=> $this->faker->name(),
            'guard_salary'=> $this->faker->name(),
            'leader_salary'=> $this->faker->name(),
            'guard_lsalary'=> $this->faker->name(),
            'leader_lsalary'=> $this->faker->name(),
            'guard_transport'=> $this->faker->name(),
            'guard_transport_rate'=> $this->faker->name(),
            'rate_type'=> $this->faker->name(),
            'email'=> $this->faker->name(),
            'location'=> $this->faker->name(),
            'jv_number'=> $this->faker->name(),
            'total_working_hours'=> $this->faker->name(),
            'tax_center'=> $this->faker->name(),
        ];
    }
}
