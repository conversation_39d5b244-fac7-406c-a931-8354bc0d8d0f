<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\TransferResource;
use App\Models\EmpBasic;
use App\Models\Transfer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TransferController extends Controller
{
    /**
     * Display a listing of the resource.
     *
    //  * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection 
     */
    public function index()
    {

        $table = DB::table('transfers')->select(
            
            'transfers.emp_basic_id',
            'transfers.effective_date',
            'transfers.reason',
            'emp_basics.first_name as first_name',
            'from_clients.company_name as f_company_name' ,
            'to_clients.company_name as t_company_name' ,

        )->join('emp_basics', 'emp_basics.id', '=', 'transfers.emp_basic_id')->join('clients as to_clients', 'to_clients.id', '=', 'transfers.t_client_id')
        // ->join('clients as from_clients', 'from_clients.id', '=', 'transfers.f_client_id')->get();
        ->join('clients as from_clients', 'from_clients.id', '=', 'transfers.f_client_id')->orderByDesc('transfers.effective_date')->get();



        return response($table);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \App\Http\Resources\TransferResource
     */
    public function store(Request $request)
    {
        $data = Transfer::create([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'f_client_id' => $request->input('f_client_id'),
            't_client_id' => $request->input('t_client_id'),
            'effective_date' => $request->input('effective_date'),
            'reason' => $request->input('reason'),
        ]);

        return new TransferResource($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *  @return \App\Http\Resources\TransferResource
     */
    public function show($id)
    {
        return new TransferResource(Transfer::find($id));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @@return \App\Http\Resources\TransferResource
     */
    public function update(Request $request, Transfer $transfer)
    {
        $transfer->update([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'f_client_id' => $request->input('f_client_id'),
            't_client_id' => $request->input('t_client_id'),
            'effective_date' => $request->input('effective_date'),
            'reason' => $request->input('reason'),
        ]);

        return new TransferResource($transfer);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Transfer $transfer)
    {
        $transfer->delete();

        return response('', 204);
    }
}
