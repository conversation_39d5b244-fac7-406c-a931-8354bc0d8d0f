<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExportOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_number',
        'customer_id',
        'total_weight_kg',
        'price_per_kg',
        'total_price',
        'incoterm',
        'shipping_date',
        'delivery_port',
        'status',
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function shipment()
    {
        return $this->hasOne(Shipment::class);
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function customsDeclarations()
    {
        return $this->hasMany(CustomsDeclaration::class);
    }

    public function coffeeBatches()
    {
        return $this->belongsToMany(CoffeeBatch::class, 'export_order_batches')->withPivot('allocated_weight_kg');
    }
}
