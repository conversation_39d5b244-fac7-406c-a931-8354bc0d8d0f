<?php

namespace App\Console;

use App\Models\ContractFollowUp;
use App\Models\PaymentFollowUp;
use App\Models\User;
use App\Notifications\ContractFollowUpNotification;
use App\Notifications\PaymentFollowupNotification;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->call(function () {
            $this->contract();
        })->everyMinute();
        $schedule->call(function () {
            $this->payment();
        })->everyMinute();
    }
    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }

    public function contract()
    {
        $results = ContractFollowUp::with(['client'])
            ->whereMonth('last_triggerd_date', '=', date('m', strtotime(now())))
            ->whereYear('last_triggerd_date', '=', date('Y', strtotime(now())))
            ->whereDate('end_date', '>=', now())
            ->get();

        foreach ($results as $result) {
            $overdue = false;
            if ($result['last_triggerd_date'] <= now()) {
                $dueDateLefts = Carbon::parse($result['last_schedule'])->diffInDays(now()) + 1;
                if (date($result['last_schedule']) <= date($result['last_triggerd_date'])) {
                    $overdue = true;
                }
                $users = User::whereIn('role', ['admin', 'hr'])->get();
                Notification::send($users, new ContractFollowUpNotification(
                    contract_id: $result['id'],
                    client_id: $result['client_id'],
                    daysLeftUntilDue: $dueDateLefts,
                    start_date: $result['start_date'],
                    end_date: $result['end_date'],
                    contract_document: $result['contract_document'],
                    contract_schedule: $result['contract_schedule'],
                    message: $result['message'],
                    last_triggerd_date: $result['last_triggerd_date'],
                    last_schedule: $result['last_schedule'],
                    overdue: $overdue,
                    company_name: $result->client->company_name,
                    triggering_date: $result['last_triggerd_date']
                ));
            }
        }
    }

    /**
     * Send payment follow up notifications
     *
     * This method will be called every minute by the scheduler, and it will send notifications
     * for all payment follow ups that are due today or overdue.
     *
     * @return void
     */
    public function payment()
    {
        $now = now();

        // Get all payment follow ups that are due today or overdue
        $paymentFollowUps = PaymentFollowUp::where('status', NULL)
            ->whereDay('last_triggerd_date', '<=', $now)
            ->get();

        // Get all users with role admin or hr
        $users = User::whereIn('role', ['admin', 'hr'])->get();

        // Get all payment follow ups that are due today or overdue, but only for records
        // that do not have a status (i.e. they are not yet completed)
        $results = PaymentFollowUp::with(['client'])
            ->whereMonth('last_triggerd_date', '=', date('m', strtotime(now())))
            //->whereYear('last_triggerd_date', '=', date('Y', strtotime(now())))
            //->whereDate('end_date', '>=', now())
            ->where('status', NULL)
            ->get();

        Log::info($paymentFollowUps, [now()->toDateTimeString()]);

        // Loop through the payment follow ups and send a notification for each one
        foreach ($paymentFollowUps as $result) {
            $overdue = false;

            // Check if the payment follow up is overdue
            if ($result['last_triggerd_date'] <= now()) {
                $dueDateLefts = Carbon::parse($result['end_date'])->diffInDays(now());

                // Check if the payment follow up is overdue
                if (date($result['last_schedule']) <= date($result['last_triggerd_date'])) {
                    $overdue = true;
                }

                // Send a notification to all users with role admin or hr
                Notification::send($users, new PaymentFollowupNotification(
                    payment_id: $result['id'],
                    client_id: $result['client_id'],
                    daysLeftUntilDue: $dueDateLefts,
                    start_date: $result['start_date'],
                    end_date: $result['end_date'],
                    collection_schedule: $result['collection_schedule'],
                    message: $result['message'],
                    last_schedule: $result['last_schedule'],
                    overdue: $overdue,
                    triggering_date: $result['last_triggerd_date']
                ));
            }
        }

        foreach ($paymentFollowUps as $paymentFollowUp) {
        }
    }
}
