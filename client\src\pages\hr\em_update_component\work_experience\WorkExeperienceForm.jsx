import { Box, Grid, TextField, Button, MenuItem } from "@mui/material";
import React, { useEffect } from "react";
import { useStateContext } from "../../../../context/ContextProvider";
import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import api from "../../../../api/config/axiosConfig";
const WorkExeperienceForm = ({ empId, data }) => {
  const { errors, setErrors, setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    emp_basic_id: " ",
    organization: " ",
    position: " ",
    start_date: " ",
    end_date: " ",
  });
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  if (data) {
    useEffect(() => {
      setFormData(data);
    }, [data]);
  }
  const createMutation = useMutation(
    (payload) => api.post(`emp_work_experiance`, payload),
    {
      onSuccess: () => {
        setNotification("work ecperience create Successfully");
        queryClient.invalidateQueries({ queryKey: "work_experiance" });
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );
  const updateMutation = useMutation(
    (payload) => api.put(`emp_work_experiance/${formData.id}`, payload),
    {
      onSuccess: () => {
        setNotification("work ecperience updated Successfully");
        queryClient.invalidateQueries({ queryKey: "work_experiance" });
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );
  const handleSubmit = (e) => {
    e.preventDefault();
    if (data && data.id) {
      updateMutation.mutate(formData);
    } else {
      const form = new FormData(e.currentTarget);
      const payload = {
        emp_basic_id: empId,
        organization: form.get("organization"),
        position: form.get("position"),
        start_date: form.get("start_date"),
        end_date: form.get("end_date"),
      };
      createMutation.mutate(payload);
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.organization == ""}
            margin="normal"
            label="Organization"
            name="organization"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.organization}
            helperText={errors.organization ? errors.organization[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.position == ""}
            margin="normal"
            label="position"
            name="position"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.position}
            helperText={errors.position ? errors.position[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.start_date == ""}
            margin="normal"
            label="Start Date"
            name="start_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.start_date}
            helperText={errors.start_date ? errors.start_date[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.end_date == ""}
            margin="normal"
            label="End Date"
            name="end_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.end_date}
            helperText={errors.end_date ? errors.end_date[0] : null}
          />
        </Grid>
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
          {data && data.id ? <div>update</div> : <div>create</div>}
        </Button>
      </Box>
    </Box>
  );
};

export default WorkExeperienceForm;
