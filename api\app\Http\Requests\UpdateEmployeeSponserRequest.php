<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateEmployeeSponserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'emp_basic_id'=>'required',
            'name'=>'required',
            'age'=>'required',
            'gender'=>'required',
            'company_name'=>'required',
            'position'=>'required',
            'city'=>'required',
            'sub_city'=>'required',
            'kebele'=>'required',
            'house_no'=>'required',
            'id_no'=>'required',
            'office_tel'=>'required',
            'mobile'=>'required',
            'property_type'=>'required',
            'position2'=>'required',
            'certificate_type'=>'required',
            'effective_date'=>'required|date',
            'expire_date'=>'required|date',
        ];
    }
}
