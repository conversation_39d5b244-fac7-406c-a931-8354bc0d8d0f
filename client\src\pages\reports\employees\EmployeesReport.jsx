import React from "react";
import { useState } from "react";
import { Box, Button, } from "@mui/material";
import { useNavigate } from "react-router-dom";
import FilterEmolyee from "./FilterEmolyee";
const EmployeesReport = () => {
  const [firstDate, setFirstDate] = useState("");
  const [lastDate, setLastDate] = useState("");
  const [clientId, setclientId] = useState("");
  const [year, setYear] = useState("");
  const [gender, setGender] = useState("");
  const [region, setRegion] = useState("");
  const [bankCode, setBankCode] = useState("");

  const navigate = useNavigate();
  const passFromDate = (fromDate) => {
    setFirstDate(fromDate);
  };
  const passProjectId = (projectId) => {
    setclientId(projectId);
  };
  const passToDate = (toDate) => {
    setLastDate(toDate);
  };
  const fullYear = (year) => {
    setYear(year);
  };
  const passGender = (gender) => {
    setGender(gender);
  };
  const passRegion = (region) => {
    setRegion(region);
  };

  const passBank = (bankCode) => { 
    setBankCode(bankCode);
  }
  const handleButtonClick = () => {
    if (firstDate || lastDate || clientId || year || gender || region || bankCode) {
      navigate("/reports/employee/employees_list", {
        state: {
          firstDate:firstDate,
          lastDate:lastDate,
          clientId  :clientId,
          year:year,
          gender:gender,
          region: region,
          bankCode: bankCode,
          status: 0,
        },
      });
    } else {
      navigate("/reports/employee/employees_list", {
        state: {
          status: 1,
        },
      });
    }
  };
  return (
    <Box margin={1}>
      <FilterEmolyee
        passFromDate={passFromDate}
        passProjectId={passProjectId}
        passToDate={passToDate}
        passYear={fullYear}
        passGender={passGender}
        passRegion={passRegion}
        passBank={passBank}
      />
      <Button
        sx={{
          margin:'5px',
          width:"300px"
        }}
        variant="contained"
        color="success"
        onClick={handleButtonClick}
      >
        filter
      </Button>
    </Box>
  );
};

export default EmployeesReport;
