import React from 'react'
import {
    Box,
    TextField,
    Typography,
    useTheme,
  } from "@mui/material";
import Header from '../../../components/Header';
import getDaysInMonth from '../../../helper/monthLength';
function GeneralInformation({empobj,client_name,startDate}) {
  return (
    <Box>
    <Header title="general infromation" heading="h6" />
    <TextField
      label="Employee Id No"
      value="EIA/11540/2022"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Employee Name"
      value={`${empobj.employee.first_name} ${empobj.employee.middle_name} ${empobj.employee.last_name}`}
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Location"
      value={client_name || ""}
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="monthLength"
      value={getDaysInMonth(
        startDate.getFullYear(),
        startDate.getMonth() + 1
      )}
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="start_date"
      value={empobj.employee.start_date}
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
  </Box>
  )
}

export default GeneralInformation