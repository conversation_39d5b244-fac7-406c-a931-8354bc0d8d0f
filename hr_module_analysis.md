# HR Module Analysis - ERP System

## Overview
This ERP system features a comprehensive HR module built with **React + Vite** frontend and **<PERSON><PERSON> 9** backend. The system manages employee lifecycle from registration to payroll and leave management.

## Frontend Architecture (React + Vite)

### Technology Stack
- **Framework**: React 18.2.0
- **Build Tool**: Vite 4.0.0
- **UI Library**: Material-UI (MUI) 5.11.7
- **State Management**: React Query (@tanstack/react-query) 4.24.4
- **HTTP Client**: Axios 1.3.1
- **Routing**: React Router DOM 6.8.0

### Frontend Structure
```
client/src/pages/hr/
├── HR.jsx                    # Main HR dashboard
├── Position.jsx              # Position management
├── assign/                   # Employee assignment module
├── em_update_component/      # Employee update components
├── leave/                    # Leave management
├── registration_component/   # Employee registration
└── transfer_employee/        # Employee transfer
```

### Key Frontend Components

#### 1. Main HR Dashboard (`HR.jsx`)
- **Purpose**: Central navigation hub for HR functions
- **Features**: 
  - Register Employee
  - Update Employee
  - Transfer Employee
  - Assign Employee
  - Add Position
  - Employee Leave

#### 2. Employee Registration (`RegisterEmployee.jsx`)
- **Type**: Multi-step form with stepper UI
- **Steps**:
  1. Basic Information
  2. Employee Detail Information
  3. Employee Allowance
  4. Employee Bank Information
- **Components**:
  - `BasicImployeeInfo.jsx`
  - `EmployeeDetail.jsx`
  - `EmployeeAllowance.jsx`
  - `EmployeeBankForm.jsx`

#### 3. Leave Management (`Leave.jsx`)
- **Features**:
  - Apply Leave
  - Leave Taken Detail
  - Leave Taken In Two Years
  - Employee Leave Summary
  - Leave Lookup Summary
- **Validation**: Checks if employee has worked less than 182 days

#### 4. Employee Update Module
- **Components**:
  - Emergency Contact
  - Language Skills
  - Sponsored Employee
  - Update Allowance/Bank/Basic Info
  - Family/Spouse Information
  - Loan Management
  - Disciplinary Actions
  - Educational Information
  - Work Experience

## Backend Architecture (Laravel 9)

### Technology Stack
- **Framework**: Laravel 9.19
- **PHP Version**: 8.0.2+
- **Authentication**: Laravel Sanctum 3.0
- **Database**: MySQL (inferred from migrations)
- **Additional Packages**: 
  - Carbon (date manipulation)
  - Doctrine DBAL (database abstraction)
  - Spatie Laravel Backup

### Backend Structure
```
api/app/
├── Http/Controllers/Api/
│   ├── EmpBasicController.php
│   ├── EmpDetailController.php
│   ├── EmpBankController.php
│   ├── EmpAllowanceController.php
│   ├── AnnualLeaveController.php
│   ├── AssignController.php
│   ├── TransferController.php
│   └── PositionController.php
├── Models/
│   ├── EmpBasic.php
│   ├── EmpDetail.php
│   ├── EmpBank.php
│   ├── EmpAllowance.php
│   ├── AnnualLeave.php
│   └── Position.php
└── Http/Resources/
    └── EmpBasicResource.php
```

### Database Schema

#### Core Employee Tables
1. **emp_basics** - Core employee information
   - Personal details (name, gender, DOB)
   - Employment details (position, salary, start_date)
   - Tax information (TIN, pension_id)
   - Termination status

2. **emp_details** - Extended employee information
3. **emp_banks** - Banking information
4. **emp_allowances** - Salary allowances
5. **annual_leaves** - Leave records
   - Leave periods (from_date, to_date)
   - Leave calculations (leave_taken, from_expired)
   - Client association

#### Supporting Tables
- **positions** - Job positions
- **clients** - Client organizations
- **assigns** - Employee-client assignments
- **transfers** - Employee transfers
- **awards** - Employee awards
- **disciplines** - Disciplinary actions

### Key API Endpoints

#### Employee Management
- `GET/POST /api/emp_basics` - Employee basic information
- `GET/POST /api/emp_details` - Employee details
- `GET/POST /api/emp_banks` - Banking information
- `GET/POST /api/emp_allowances` - Allowances

#### Leave Management
- `GET/POST /api/leave` - Annual leave operations
- `GET /api/leave_detail/{emp_basic_id}` - Leave details
- `GET /api/leave_twoyears/{emp_basic_id}` - Two-year leave history
- `GET /api/leaveId/{id}` - Specific leave record

#### Assignment & Transfer
- `GET/POST /api/assign` - Employee assignments
- `GET /api/assign/client/{client_id}` - Client assignments
- `GET/POST /api/transfer` - Employee transfers

## Key Features

### 1. Employee Registration
- Multi-step registration process
- Comprehensive data collection
- Form validation and error handling
- Progress tracking with stepper UI

### 2. Leave Management System
- Annual leave calculation
- Leave balance tracking
- Historical leave records
- Minimum service period validation (182 days)
- Leave liability calculations

### 3. Employee Assignment
- Client-based assignments
- Assignment history tracking
- Payroll list generation

### 4. Transfer Management
- Inter-client transfers
- Transfer history
- Status tracking

### 5. Position Management
- Job position definitions
- Position-based assignments

## Authentication & Security
- **Frontend**: Token-based authentication with automatic logout on 401
- **Backend**: Laravel Sanctum for API authentication
- **Authorization**: Bearer token in request headers

## Data Flow
1. **Frontend** makes API calls using Axios with interceptors
2. **Authentication** handled via Bearer tokens
3. **Backend** processes requests through Laravel controllers
4. **Database** operations through Eloquent ORM
5. **Response** formatted using Laravel Resources

## Development Environment
- **Frontend Dev Server**: Vite on port 4000
- **API Base URL**: Configured via environment variables
- **Database**: MySQL with comprehensive migration system

## Notable Implementation Details
- Complex leave calculation logic with multiple date considerations
- Multi-step form handling with state management
- Comprehensive employee data model with relationships
- Client-based employee organization
- Extensive reporting capabilities for payroll and tax purposes
