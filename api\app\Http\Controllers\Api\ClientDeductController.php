<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\clientDeduct;
use Illuminate\Http\Request;

class ClientDeductController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(clientDeduct::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = clientDeduct::create([
            'client_id' => $request->input('client_id'),
            'labor_union' => $request->input('labor_union'),
            'creadit_association' => $request->input('creadit_association'),
            'sport_association' => $request->input('sport_association'),
            'type' => $request->input('type'),
            'user_id' => $request->input('user_id'),

        ]);

        return response($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $deduct = clientDeduct::where('client_id', $id)->get();
        return response($deduct);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $deduct = clientDeduct::find($id);

        $deduct->update([
            'client_id' => $request->input('client_id'),
            'labor_union' => $request->input('labor_union'),
            'creadit_association' => $request->input('creadit_association'),
            'sport_association' => $request->input('sport_association'),
            'type' => $request->input('type'),
            'user_id' => $request->input('user_id'),

        ]);
        return response($deduct);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $deduct = clientDeduct::find($id);

        $deduct->delete();

        return response('', 204);
    }
}
