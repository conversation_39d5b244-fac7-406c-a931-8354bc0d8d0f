import {
  <PERSON>,
  Button,
  CircularProgress,
  Typography,
  useTheme,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React from 'react';
import api from '../../../api/config/axiosConfig';
import Header from '../../../components/Header';
import { useStateContext } from '../../../context/ContextProvider';
import { tokens } from '../../../utils/theme';
function AbsenceTakenList({ emp_basic_id, selectedRow }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const queryClient = useQueryClient();
  const { setNotification } = useStateContext();

  const {
    data: absence,
    isLoading,
    isFetched,
  } = useQuery(
    ['absence', emp_basic_id],
    async () =>
      await api.get(`absence/${emp_basic_id}`).then(({ data }) => data.data),
    {
      enabled: !!emp_basic_id,
      staleTime: 60000,
    }
  );

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      flex: 0.2,
    },
    {
      field: 'start_date',
      headerName: 'StartDate',
      flex: 0.5,
    },
    {
      field: 'end_date',
      headerName: 'To Date',
      flex: 0.5,
    },
    {
      field: 'hour_day',
      headerName: 'no of days or hours worked',
      flex: 0.8,
    },
    {
      field: 'minutes',
      headerName: 'minutes',
      flex: 0.5,
    },
    {
      field: 'rate_type',
      headerName: 'Rate Type',
      flex: 0.5,
    },

    {
      field: 'penality_rate',
      headerName: 'penality Rate',
      flex: 0.5,
    },
    {
      field: 'edit',
      headerName: 'Edit',
      flex: 0.7,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          selectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: 'delete',
      headerName: 'Delete',
      flex: 0.8,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deletAbsence = useMutation((id) => api.delete(`absence/${id}`), {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: 'absence' });
            setNotification('Absence record deleted successfully');
          },
        });
        const deleteAbsence = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              'Are you sure you want to delete the selected  tax center?'
            )
          ) {
            return;
          }

          deletAbsence.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deleteAbsence}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];
  return (
    <>
      <Header title="list of absence record" heading="h5" />
      {isLoading && <CircularProgress />}
      {isFetched && (
        <DataGrid
          columns={columns}
          hideFooter={true}
          autoHeight={true}
          rows={absence}
        />
      )}
    </>
  );
}

export default AbsenceTakenList;
