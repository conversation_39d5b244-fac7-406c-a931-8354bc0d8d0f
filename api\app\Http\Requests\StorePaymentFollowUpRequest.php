<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePaymentFollowUpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'client_id' => 'required',
            'collection_schedule' => 'required',
            'start_date' => 'date|required',
            'end_date' => 'date|required',
            'reminder' => 'required|integer',
            'message' => '',
            'amount' => '',
            'user_id' => 'integer',
            'contract_length' => 'required|integer',
        ];
    }
}
