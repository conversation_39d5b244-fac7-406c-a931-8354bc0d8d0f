import {
  Card,
  useTheme,
  CardContent,
  Typography,
  Box,
  Grid,
  Divider,
} from "@mui/material";
import React from "react";
import { tokens } from "../utils/theme";

const MetricsCard = ({ title, content, isDate = false, percentage = "" }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const currentDate = new Date();
  const startDate = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth(),
    1
  );
  const endDate = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth() + 1,
    0
  );

  const formattedStartDate = startDate.toLocaleDateString("en-US", {
    day: "numeric",
    month: "short",
  });

  const formattedEndDate = endDate.toLocaleDateString("en-US", {
    day: "numeric",
    month: "short",
  });

  return (
    <Card
      variant="elevation"
      style={{
        color: colors.grey,
        background: colors.primary[400],
        padding: 5,
      }}
    >
      <CardContent>
        <Grid container>
          <Grid item sm={8}>
            <Box display="flex flexDirection:column">
              <Typography variant="body2" color={colors.grey[100]}>
                {title}
              </Typography>
              <Typography variant="h3" color={colors.blueAccent[500]}>
                {content}
                
              </Typography>
              {percentage && (
                <>
                  <Divider />
                  <Typography
                    variant="body1"
                    color={
                      percentage >= 50
                        ? colors.greenAccent[500]
                        : colors.redAccent[500]
                    }
                  >
                    {`${percentage} % success`}
                  </Typography>
                </>
              )}
            </Box>
          </Grid>
          <Grid item sm={4}>
            {isDate && (
              <Typography
                sx={{
                  float: "right",
                }}
              >{`${formattedStartDate}-${formattedEndDate}`}</Typography>
            )}
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default MetricsCard;
