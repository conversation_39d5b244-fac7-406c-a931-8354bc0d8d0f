
import { Box, TextField, Autocomplete, useTheme } from '@mui/material';
import { useState, useEffect } from 'react';
import Minisearch from 'minisearch';
import { tokens } from '../utils/theme';
import { useClientData, useEmpData } from '../api/userApi/clinetHook';

function EmpSearchBar({ onClientSelect, variant }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [clientId, setClientId] = useState('');
  const [results, setResults] = useState([]);
  const { data: emp_basics, isLoading } = useEmpData();

  useEffect(() => {
    if (!emp_basics) return;

    const miniSearch = new Minisearch({
      fields: ['first_name'],
      storeFields: ['id', 'first_name', 'middle_name', 'last_name'],
    });

    miniSearch.addAll(emp_basics);

    const handleChange = (e, value) => {
      setClientId(value ? value.first_name : '');
      const res = miniSearch.search(value ? value.first_name : '', {
        fuzzy: 0.3,
      });
      setResults(res);
    };

    return () => {
      // Cleanup logic if needed
    };
  }, [emp_basics]);

  if (!emp_basics) {
    return 'loading ...';
  }

  return (
    <Autocomplete
      options={results.length > 0 ? results : emp_basics}
      getOptionLabel={(option) =>
        `${option.first_name} ${option.middle_name} ${option.last_name} - id ${option.id}`
      }
      onChange={(e, value) => {
        setClientId(value ? value.first_name : '');
        onClientSelect({ value: value, clicked: true });
        setResults([]);
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          margin="normal"
          label="Search Employee here"
          name="client_id"
          fullWidth
          variant={variant ?? 'standard'}
          type="search"
          InputLabelProps={{
            color: 'success',
          }}
          value={clientId}
        />
      )}
    />
  );
}

export default EmpSearchBar;
