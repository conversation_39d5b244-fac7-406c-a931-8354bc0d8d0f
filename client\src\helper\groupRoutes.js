export const groupRoutes = (routes) => {
  if (!routes) {
    return {};
  }

  const grouped = {
    HR: [],
    "HR List": [],
    Client: [],
    Finance: [],
    Store: [],
    "Coffee Export": [],
    Settings: [],
    Other: [],
  };

  const hrListPaths = [
    "/employees/assign",
    "/employees/transfer",
    "/employees/transfer/:id",
    "/employees/leave",
    "/employees/leaves",
    "/employees/add-leaves",
    "/employees/add-leaves/:id",
    "/employees/leave-detail",
    "/employees/leave-detail-two",
    "/employees/leave-summary",
    "/employees/leave-summary-lookup",
  ];

  routes.children.forEach((route) => {
    if (hrListPaths.includes(route.path)) {
      grouped["HR List"].push(route);
    } else if (route.path.startsWith("employees")) {
      grouped.HR.push(route);
    } else if (route.path.startsWith("clients")) {
      grouped.Client.push(route);
    } else if (route.path.startsWith("finance")) {
      grouped.Finance.push(route);
    } else if (
      route.path.startsWith("store") ||
      route.path.startsWith("settings/category")
    ) {
      grouped.Store.push(route);
    } else if (route.path.startsWith("store/approval")) {
      grouped.Store.push(route);
    } else if (route.path.startsWith("coffeepage")) {
      grouped["Coffee Export"].push(route);
    } else if (route.path.startsWith("settings")) {
      grouped.Settings.push(route);
    } else {
      grouped.Other.push(route);
    }
  });

  return grouped;
};
