import React from "react";
import Header from "../../../components/Header";
import {
  Button,
  Typography,
  useTheme,
  Box,
  CircularProgress,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { useStateContext } from "../../../context/ContextProvider";
import { tokens } from "../../../utils/theme";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";

function AddDeductableList({ emp_basic_id, selectedRow }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const {
    data: additional_deductable,
    isLoading,
    isFetched,
  } = useQuery(
    ["additional_deductable", emp_basic_id],
    async () =>
      await api
        .get(`additional_deductable/${emp_basic_id}`)
        .then(({ data }) => data.data),
    {
      enabled: !!emp_basic_id,
      staleTime: 60000,
    }
  );
  const columns = [
    {
      field: "id",
      headerName: "ID",
      flex: 0.2,
    },
    {
      field: "deduct_date",
      headerName: "deductable date",
      flex: 0.5,
    },
    {
      field: "duration",
      headerName: "no-of-days/hours-worked",
      flex: 0.8,
    },
    {
      field: "rate",
      headerName: "Rate Type",
      flex: 0.5,
    },

    {
      field: "amount",
      headerName: "Salary-per-hour/day",
      flex: 0.5,
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 0.7,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          selectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "delete",
      headerName: "Delete",
      flex: 0.8,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deletAdditionalDeductable = useMutation(
          (id) => api.delete(`additional_deductable/${id}`),
          {
            onSuccess: () => {
              setNotification(
                "Additional Deductable record deleted successfully"
              );
              queryClient.invalidateQueries({
                queryKey: "additional_deductable",
              });
            },
          }
        );
        const deleteAdditionalDeductable = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              "Are you sure you want to delete the selected  Additional deductable record?"
            )
          ) {
            return;
          }
          deletAdditionalDeductable.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deleteAdditionalDeductable}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];
  return (
    <Box>
      <Header title="List Of unpaid Deduction List" heading="h5" />
      {isLoading && <CircularProgress />}
      {isFetched && (
        <DataGrid
          columns={columns}
          hideFooter
          autoHeight
          rows={additional_deductable}
        />
      )}
    </Box>
  );
}

export default AddDeductableList;
