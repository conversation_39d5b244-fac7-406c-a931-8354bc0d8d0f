import React, { useEffect, useState } from "react";

import { useParams } from "react-router-dom";

import { Box, Grid, useTheme } from "@mui/material";

import Header from "../../../components/Header";
import LeftSide from "../../../components/LeftSide";
import RightSide from "../../../components/RightSide";
import UpdateBasicInfo from "./UpdateBasicInfo";
import UpdateEmpDetail from "./UpdateEmpDetail";
import UpdateAllowance from "./UpdateAllowance";
import UpdateBank from "./UpdateBank";
import UpdateLoan from "./UpdateLoan";
import UpdateSponser from "./UpdateSponser";
import UpdateStatus from "./UpdateStatus";
import EducationalInformation from "./educational_info/EducationalInformation";
import WorkExperiance from "./work_experience/WorkExperiance";
import EmergencyContact from "./EmergencyContact";
import LangugeSkill from "./LangugeSkill";
import UpdateFamilySpouce from "./UpdateFamilySpouce";
import Recomandation from "./recommandation/Recomandation";
import SponsoredEmp from "./SponsoredEmp";
import Discipline from "./disciplnary_action/Discipline"
import api from "../../../api/config/axiosConfig";
import { useQuery } from "@tanstack/react-query";

import _LinearProgress from "../../../components/_LinearProgress";
function UpdateComponent() {
 
  const links = [
    "Basic Employee Info",
    "Employee Detail",
    "Employee Allowance",
    "Employee Bank",
    // "Employee Loan",
    "Employee Sponser",
    "Sponsored Employee",
    "Employee Status",
    "Educational Information",
    "Work Experiance",
    "Emergency Contact",
    "Name Of Spouse and Family ",
    "Recomandation",
    "Discipline Action",
    "Additional Language Skill",
  ];
  const { id } = useParams();

  const components = [
    UpdateBasicInfo,
    UpdateEmpDetail,
    UpdateAllowance,
    UpdateBank,
    // UpdateLoan,
    UpdateSponser,
    SponsoredEmp,
    UpdateStatus,
    EducationalInformation,
    WorkExperiance,
    EmergencyContact,
    UpdateFamilySpouce,
    Recomandation,
    Discipline,
    LangugeSkill,
  ];
  
  const [selectedLinkIndex, setSelectedLinkIndex] = useState(0);
  const [selectedLink, setSelectedLink] = useState(links[0]);
  const handleLinkClick = (index) => {
    setSelectedLinkIndex(index);
    setSelectedLink(links[index]);
  };
  // const [emp, setEmp] = useState({});
  // if (id) {
  //   useEffect(() => {
  //     api.get(`emp_basics/${id}`).then(({ data }) => {
  //       setEmp(data);
  //     });
  //   }, [id]);
  // }
  const { isLoading ,data:emp} = useQuery(["updateEmp"], () =>
   api.get(`emp_basics/${id}`).then(({ data }) => data.data),{
      refetchOnWindowFocus:false,
      staleTime:6000,
      onSuccess:()=>{
      }
    }
  );
 
  // 
  // 
  return isLoading ? (
    <_LinearProgress />
  ) : (
    <Box m="20px">
      <Box display="flex" justifyContent="space-between">
        <Header
          subtitle={`Employee Name: ${emp.first_name} ${emp.middle_name} ${emp.last_name}`}
        />
        <Box sx={{ marginRight: "400px" }}>
          <Header subtitle={selectedLink} />
        </Box>
      </Box>

      <Grid container spacing={2}>
        <LeftSide
          links={links}
          handleLinkClick={handleLinkClick}
          activeLink={selectedLinkIndex}
        />
        <RightSide
          components={components}
          selectedLinkIndex={selectedLinkIndex}
          id={id}
        />
      </Grid>
    </Box>
  );
}

export default UpdateComponent;
