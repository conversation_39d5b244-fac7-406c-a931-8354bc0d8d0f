import React, { useState } from "react";
import { Box, Grid, Paper, useTheme } from "@mui/material";
import Header from "../../../components/Header";
import SearchBar from "../../../components/SearchBar";
import { tokens } from "../../../utils/theme";
import ListOfPayRoll from "./ListOfPayRoll";
function DraftPayroll() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [clicked, setClicked] = useState(false);
  const [clientId, setClientId] = useState("");
  const handleClientSelect = ({ value, clicked }) => {
    setClientId(value.id);
    setClicked(clicked);
  };


  return !clicked ? (
    <Box m="20px">
      <Header
        title="Draft Payroll"
        subtitle="select client to watch Draft payroll"
      />

      <Grid container spacing={2}>
        <Grid item xs={12} sm={4}>
          <Paper
            sx={{
              padding: "1rem",
              color: colors.grey[100],
              background: colors.primary[400],
            }}
          >
            <SearchBar  onClientSelect={handleClientSelect} />
          </Paper>
        </Grid>
      </Grid>
    </Box>
  ) : (
    <ListOfPayRoll clientId={clientId} />
  );
}

export default DraftPayroll;
