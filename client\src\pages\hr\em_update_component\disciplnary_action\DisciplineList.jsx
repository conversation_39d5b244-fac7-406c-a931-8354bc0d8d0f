import React from "react";
import EditR<PERSON>Cell from "../../../../components/EditRenderCell";
import Header from "../../../../components/Header";
import { DataGrid } from "@mui/x-data-grid";
import DeleteRenderCell from "../../../../components/DeleteRenderCell";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import api from "../../../../api/config/axiosConfig";
import { useStateContext } from "../../../../context/ContextProvider";
import { CircularProgress } from "@mui/material";

const DisciplineList = ({ id, selectedRow }) => {
  const queryClinet = useQueryClient();
  const {
    data: disciplinary,
    isLoading,
    isError,
    isFetched,
  } = useQuery(
    ["discipline", id],
    () =>
      api
        .get(`emp_basics/${id}`, { params: { all: "dicipline" } })
        .then(({ data }) => {
          if (data.data.discipline) {
            return data.data.discipline;
          }
        }),
    {
      enabled: !!id,
      refetchOnWindowFocus: false,
      staleTime: 600000,
    }
  );
  const { setNotification } = useStateContext();
  const deleteRow = useMutation(
    (id) => api.delete(`discipline/${id}`),
    {
      onSuccess: () => {
        queryClinet.invalidateQueries({ queryKey: "discipline" });
        setNotification("Discipline record is deleted Successfully");
      },
    }
  );

  const handleDelete = (id) => {
    if (
      !window.confirm("Are you sure you want to delete the selected  record?")
    ) {
      return;
    }
    deleteRow.mutate(id);
  };
  const columns = [
    {
      field: "id",
      headerName: "Id",
    },
    {
      field: "record_type",
      headerName: "Record Type",
    },
    {
      field: "subject",
      headerName: "Subject",
    },
    {
      field: "reason",
      headerName: "Reason",
    },
    {
      field: "ref_no",
      headerName: "Subject",
    },
    {
      field: "to_date",
      headerName: "Effective Date",
    },
    {
      field: "edit",
      headerName: "Edit",
      filterable: false,
      sortable: false,
      hide: false,
      renderCell: (params) => <EditRenderCell onClick={(e)=>{
        e.stopPropagation();
        selectedRow(params.row)
      }} title='Edit' />,
    },

    {
      field: "delete",
      headerName: "Delete",
      filterable: false,
      sortable: false,
      hide: false,
      renderCell: (params) => (
        <DeleteRenderCell
          handleDelete={()=>handleDelete(params.row.id)}
        />
      ),
    },
  ];
  return (
    <div style={{ marginTop: "15px" }}>
      <Header title="List of disciplinary action" heading="h5" />
      {!isFetched && <CircularProgress />}
      {isFetched && (
        <DataGrid columns={columns} rows={disciplinary} hideFooter autoHeight />
      )}
    </div>
  );
};

export default DisciplineList;
