import { useStateContext } from "../../../context/ContextProvider";
import React, { useState, useEffect } from "react";
import { Grid, Box, TextField, MenuItem, Button } from "@mui/material";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";

function AdditionalNonTaxableForm({ formData, editData, setEditData }) {
  const queryClient = useQueryClient();

  const [nontaxable, setNonTaxable] = useState({
    transport_allowance: "",
    mobile_allowance: "",
    cleaning_allowance: "",
    position_allowance: "",
    other_allowance: "",
    desert_allowance: "",
    medical_allowance: "",
    effective_date: "",
  });

  useEffect(() => {
    if (editData) {
      setNonTaxable(editData);
    }
  }, [editData]);

  const { setNotification, user } = useStateContext();

  const createNonTaxable = useMutation((data) => api.post("nontaxable", data), {
    onSuccess: () => {
      setNotification("Additional Non Taxable inserted successfully");
      queryClient.invalidateQueries({ queryKey: "nontaxable" });
    },
  });

  const handleChange = (e) => {
    setNonTaxable({
      ...nontaxable,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    
    const payload = {
      emp_basic_id: formData.emp_basic_id,
      transport_allowance: data.get("transport_allowance"),
      mobile_allowance: data.get("mobile_allowance"),
      cleaning_allowance: data.get("cleaning_allowance"),
      position_allowance: data.get("position_allowance"),
      other_allowance: data.get("other_allowance"),
      desert_allowance: data.get("desert_allowance"),
      medical_allowance: data.get("medical_allowance"),
      effective_date: data.get("effective_date"),
      user_id: user.id,
    };

    if (editData && editData.id) {
      api.put(`nontaxable/${editData.id}`, payload).then(() => {
        setNotification("Additional Non Taxable Record updated successfully");
        setEditData({});
        queryClient.invalidateQueries({ queryKey: "nontaxable" });
      });
    } else {
      createNonTaxable.mutate(payload);
    }

  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Transport Allowance"
            name="transport_allowance"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={nontaxable.transport_allowance}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Position Allowance"
            name="position_allowance"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={nontaxable.position_allowance}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Cleaning Allowance"
            name="cleaning_allowance"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={nontaxable.cleaning_allowance}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Mobile Allowance"
            name="mobile_allowance"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={nontaxable.mobile_allowance}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Desert allowance"
            name="desert_allowance"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={nontaxable.desert_allowance}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Medical Allowance"
            name="medical_allowance"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={nontaxable.medical_allowance}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Other Allowance"
            name="other_allowance"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={nontaxable.other_allowance}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="effective date"
            name="effective_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={
              nontaxable.effective_date || new Date().toISOString().slice(0, 10)
            }
          />
        </Grid>
      </Grid>
      <span style={{ float: "inline-start" }}>
        <Button type="submit" variant="contained" color="success">
          {editData.id ? <>update</> : <>create</>}
        </Button>
      </span>
    </Box>
  );
}

export default AdditionalNonTaxableForm;
