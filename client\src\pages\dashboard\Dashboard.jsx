import { Box, CircularProgress, Container, Grid, Button, Typography, useTheme } from '@mui/material';
import { DataGrid } from "@mui/x-data-grid";

import React, { useEffect } from 'react';

import Header from '../../components/Header';
import NotificationCard from './notifications/NotificationCard';
import MetricsCard from '../../components/MetricsCard';
import { useQuery } from '@tanstack/react-query';
import api from '../../api/config/axiosConfig';
import NavigationCard from './NavigationCard';
import { formatNumber } from '../../helper/valueFormatter';
import { tokens } from "../../utils/theme";
//import { use } from 'react';
import { Link } from "react-router-dom";

import { useUnreadNotifications } from '../../api/userApi/clinetHook'

function Dashboard() {

  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const {data:notifications,isLoading: isLoadingNotification}=useUnreadNotifications()

  const {
    data: getmetrics,
    isLoading,
    isFetched,
  } = useQuery(
    ['get-metrics'],
    () => api.get('getMetrics').then(({ data }) => data),
    {
      refetchOnWindowFocus: false,
    }
  );

  const { data: getThisMonthPayments, isLoading: isLoadingPayment } = useQuery(
    ['paymentsOfThisMonth'],
    () =>
      api.get('paymentsOfThisMonth').then(({ data }) => {
        console.log(data.data);
        return data.data;
      }),
    {
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (getThisMonthPayments) {
      console.log(getThisMonthPayments);
    }
  }, [getThisMonthPayments]);

  const columns = [
    {
      field: 'client_id',
      headerName: 'Client',
      flex: 1,
    },
    {
      field: 'amount',
      headerName: 'Amount',
      flex: 1,
    },
    {
      field: 'message',
      headerName: 'Message',
      flex: 1,
    },
    {
      field: 'daysLeftUntilDue',
      headerName: 'Days Left Until Due',
      flex: 1,
    },
    {
      field: 'overdue',
      headerName: 'Status',
      flex: 1,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
        };

        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            alignItems="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Link
              to={`/clients/${params.row.id}`}
              style={{ textDecoration: 'none', width: '100%' }}
            >
              <Button onClick={onRowClicked}>
                process
                {/* <Typography
                  color={colors.grey[100]}
                  sx={{ ml: '5px' }}
                ></Typography> */}
              </Button>
            </Link>
          </Box>
        );
      },
    },
  ];

  return (
    <Box margin={2}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6}>
          <Grid container spacing={1}>
            <Grid item xs={12} sm={6}>
              <NotificationCard />
            </Grid>
            <Grid item xs={12} sm={3}>
              {isLoading && <CircularProgress />}
              {isFetched && (
                <MetricsCard title="Users" content={getmetrics.users} />
              )}
            </Grid>
            <Grid item xs={12} sm={3}>
              {isLoading && <CircularProgress />}
              {isFetched && (
                <MetricsCard title="Employees" content={getmetrics.employees} />
              )}
            </Grid>
            <Grid item xs={12} sm={6}>
              <NavigationCard
                content="Permissons"
                url="/permissions"
                title="all permissions"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              {isLoading && <CircularProgress />}
              {isFetched && (
                <MetricsCard
                  title="Active Employees"
                  content={getmetrics.active_employees}
                />
              )}
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} sm={6}>
          <Grid container spacing={1}>
            <Grid item xs={12} sm={12}>
              {isLoading && <CircularProgress />}
              {isFetched && (
                <MetricsCard
                  title="Severance Liablity"
                  content={formatNumber(getmetrics.severance_liablity)}
                />
              )}
            </Grid>
            {/* <Grid item xs={12} sm={6}>
              {isLoading && <CircularProgress />}
              {isFetched && (
                <MetricsCard
                  title="leave Liablity Days"
                  content={formatNumber(getmetrics.leave_liablity_days)}
                />
              )}
            </Grid>
            <Grid item xs={12} sm={6}>
              {isLoading && <CircularProgress />}
              {isFetched && (
                <MetricsCard
                  title="leave Liablity money"
                  content={formatNumber(getmetrics.leave_liablity_money)}
                />
              )}
            </Grid> */}
          </Grid>
        </Grid>
      </Grid>

      <Grid container spacing={2} mt={2}>
        <Grid item xs={12}>
          <Header title="List of Payment FollowUp Record" heading="h5" />
          {isLoadingPayment && <CircularProgress />}
          {getThisMonthPayments && (
            <DataGrid
              rows={getThisMonthPayments}
              columns={columns}
              disableSelectionOnClick
              autoHeight
            />
          )}
        </Grid>
      </Grid>
    </Box>
  );
}

export default Dashboard;
