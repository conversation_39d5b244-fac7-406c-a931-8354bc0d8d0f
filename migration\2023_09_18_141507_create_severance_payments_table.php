<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('severance_payments', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('emp_basic_id');
            $table->date('start_date')->default(now());
            $table->date('termination_date')->default(now());
            $table->date('approval_date')->default(now());
            $table->float('basic_salary');
            $table->float('y_of_service');
            $table->float('total_severance_pay');
            $table->float('severance_tax');
            $table->float('severance_net');
            $table->text('termination_reason');
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('severance_payments');
    }
};
