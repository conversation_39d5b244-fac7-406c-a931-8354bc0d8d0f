import {
  Button,
  Container,
  Grid,
  Paper,
  TextField,
  useTheme,
  Box,
  MenuItem,
} from '@mui/material';
import {
  QueryClient,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';

import React from 'react';
import { useState } from 'react';
import { useParams } from 'react-router-dom';
import api from '../../api/config/axiosConfig';
import Header from '../../components/Header';
import { useStateContext } from '../../context/ContextProvider';
import { tokens } from '../../utils/theme';

function ClientForm() {
  let { id } = useParams();
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { errors, setErrors, setNotification, user } = useStateContext();
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    company_name: '',
    company_code: '',
    region: '',
    city: '',
    sub_city: '',
    street_address1: '',
    street_address2: '',
    kebele: '',
    house_no: '',
    tel: '',
    residence: '',
    start_date: '',
    end_date: '',
    sh_id: '',
    guard_no: '',
    leader_no: '',
    guard_salary: '',
    leader_salary: '',
    guard_lsalary: '',
    leader_lsalary: '',
    guard_transport: '',
    guard_transport_rate: '',
    rate_type: '',
    email: '',
    location: '',
    jv_number: '',
    total_working_hours: 1,
    tax_center: '',
    pension_center: '',
    client_additional: 0,
  });

  const regionList = [
    'Addis Ababa',
    'Afar',
    'Amhara',
    'Benishangul-Gumuz',
    'Dire Dawa',
    'Gambela',
    'Harari',
    'Oromia',
    'Sidama',
    'Somali',
    'South West Ethiopia Peoples',
    'Tigray',
    'Central Ethiopia',
    'South Ethiopia',
  ];
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const client = useQuery(
    ['client', id],
    () =>
      api.get(`client/${id}`).then(({ data }) => {
        setFormData(data);

        return data;
      }),
    {
      enabled: !!id,
      refetchOnWindowFocus: false,
      staleTime: 600000,
    }
  );

  const { data: tax_center, isLoading } = useQuery(
    ['tax_center'],
    async () =>
      await api.get('tax_center').then(({ data }) => {
        return data;
      })
  );

  const addClient = useMutation((formdata) => api.post('client', formdata), {
    onSuccess: () => {
      queryClient.invalidateQueries(['client']);
      setNotification('client added successfully');
      setFormData({
        first_name: '',
        last_name: '',
        company_name: '',
        company_code: '',
        region: '',
        city: '',
        sub_city: '',
        street_address1: '',
        street_address2: '',
        kebele: '',
        house_no: '',
        tel: '',
        residence: '',
        start_date: '',
        end_date: '',
        sh_id: '',
        guard_no: '',
        leader_no: '',
        guard_salary: '',
        leader_salary: '',
        guard_lsalary: '',
        leader_lsalary: '',
        guard_transport: '',
        guard_transport_rate: '',
        rate_type: '',
        email: '',
        location: '',
        jv_number: '',
        total_working_hours: 1,
        tax_center: '',
        pension_center: '',
        client_additional: 0,
      });
    },
    onError: (err) => {
      if (err.response && err.response.status === 422) {
        if (err.response.data.errors) {
          setErrors(err.response.data.errors);
        }
      }
    },
  });

  const updateClient = useMutation(
    (formdata) => api.put(`client/${formdata.id}`, {...formdata, user_id: user.id}),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['client']);
        setNotification('client Information updated successfully');
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );
  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    
    const payload = {
      first_name: data.get('first_name'),
      last_name: data.get('last_name'),
      company_name: data.get('company_name'),
      company_code: data.get('company_code'),
      region: data.get('region'),
      city: data.get('city'),
      sub_city: data.get('sub_city'),
      street_address1: data.get('street_address1'),
      street_address2: data.get('street_address2'),
      kebele: data.get('kebele'),
      house_no: data.get('house_no'),
      tel: data.get('tel'),
      residence: data.get('residence'),
      start_date: data.get('start_date'),
      end_date: data.get('end_date'),
      sh_id: data.get('sh_id'),
      guard_no: data.get('guard_no'),
      leader_no: data.get('leader_no'),
      guard_salary: data.get('guard_salary'),
      leader_salary: data.get('leader_salary'),
      guard_lsalary: data.get('guard_lsalary'),
      leader_lsalary: data.get('leader_lsalary'),
      guard_transport: data.get('guard_transport'),
      guard_transport_rate: data.get('guard_transport_rate'),
      rate_type: data.get('rate_type'),
      email: data.get('email'),
      location: data.get('location'),
      jv_number: data.get('jv_number'),
      total_working_hours: data.get('total_working_hours'),
      tax_center: data.get('tax_center'),
      pension_center: data.get('pension_center'),
      client_additional: data.get('client_additional'),
      user_id: user.id,
    };

    if (id) {
      updateClient.mutate(formData);
    } else {
      console.log(payload);
      addClient.mutate(payload);
    }
  };
  return (
    <Box m="10px">
      <Header
        title="Client Registration form"
        subtitle="form to register or update client information"
      />
      <Container component="main" maxWidth="xlg" sx={{ mb: 4 }}>
        <Paper
          variant="outlined"
          sx={{
            my: { xs: 3, md: 6 },
            p: { xs: 2, md: 3 },
            color: colors.grey[100],
            background: colors.primary[400],
          }}
        >
          <Box component="form" onSubmit={handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.first_name == ''}
                  margin="normal"
                  label="First Name"
                  name="first_name"
                  fullWidth
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.first_name}
                  helperText={errors.first_name ? errors.first_name[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.last_name == ''}
                  margin="normal"
                  label="Last Name"
                  name="last_name"
                  fullWidth
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.last_name}
                  helperText={errors.last_name ? errors.last_name[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.email == ''}
                  margin="normal"
                  label="email"
                  name="email"
                  fullWidth
                  variant="standard"
                  type="email"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.email}
                  helperText={errors.email ? errors.email[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.company_name == ''}
                  margin="normal"
                  label="Company Name"
                  name="company_name"
                  fullWidth
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.company_name}
                  helperText={
                    errors.company_name ? errors.company_name[0] : null
                  }
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.company_code == ''}
                  margin="normal"
                  label="Company Code"
                  name="company_code"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.company_code}
                  helperText={
                    errors.company_code ? errors.company_code[0] : null
                  }
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  select
                  required
                  error={!errors.region == ''}
                  margin="normal"
                  label="Region"
                  name="region"
                  fullWidth
                  variant="standard"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.region}
                  helperText={errors.region ? errors.region[0] : null}
                >
                  {regionList.map((region) => (
                    <MenuItem key={region} value={region}>
                      {region}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.residence == ''}
                  margin="normal"
                  label="Residence"
                  name="residence"
                  fullWidth
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.residence}
                  helperText={errors.residence ? errors.residence[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.start_date == ''}
                  margin="normal"
                  label="StartDate"
                  name="start_date"
                  fullWidth
                  variant="standard"
                  type="date"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.start_date}
                  helperText={errors.start_date ? errors.start_date[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.end_date == ''}
                  margin="normal"
                  label="End Date"
                  name="end_date"
                  fullWidth
                  variant="standard"
                  type="date"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.end_date}
                  helperText={errors.end_date ? errors.end_date[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.city == ''}
                  margin="normal"
                  label="City"
                  name="city"
                  fullWidth
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.city}
                  helperText={errors.city ? errors.city[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.sub_city == ''}
                  margin="normal"
                  label="Sub City"
                  name="sub_city"
                  fullWidth
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.sub_city}
                  helperText={errors.sub_city ? errors.sub_city[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.kebele == ''}
                  margin="normal"
                  label="Kebele"
                  name="kebele"
                  fullWidth
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.kebele}
                  helperText={errors.kebele ? errors.kebele[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.house_no == ''}
                  margin="normal"
                  label="House Number"
                  name="house_no"
                  fullWidth
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.house_no}
                  helperText={errors.house_no ? errors.house_no[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.location == ''}
                  margin="normal"
                  label="Location"
                  name="location"
                  fullWidth
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.location}
                  helperText={errors.location ? errors.location[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.street_address1 == ''}
                  margin="normal"
                  label="street_address1"
                  name="street_address1"
                  fullWidth
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.street_address1}
                  helperText={
                    errors.street_address1 ? errors.street_address1[0] : null
                  }
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.street_address2 == ''}
                  margin="normal"
                  label="Street Adress Number 2"
                  name="street_address2"
                  fullWidth
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.street_address2}
                  helperText={
                    errors.street_address2 ? errors.street_address2[0] : null
                  }
                />
              </Grid>

              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.tel == ''}
                  margin="normal"
                  label="Phone"
                  name="tel"
                  fullWidth
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.tel}
                  helperText={errors.tel ? errors.tel[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.sh_id == ''}
                  margin="normal"
                  label="Sh Id"
                  name="sh_id"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.sh_id}
                  helperText={errors.sh_id ? errors.sh_id[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.guard_no == ''}
                  margin="normal"
                  label="Guard No"
                  name="guard_no"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.guard_no}
                  helperText={errors.guard_no ? errors.guard_no[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.leader_no == ''}
                  margin="normal"
                  label="Leader No"
                  name="leader_no"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.leader_no}
                  helperText={errors.leader_no ? errors.leader_no[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.guard_salary == ''}
                  margin="normal"
                  label="Guard Salary"
                  name="guard_salary"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.guard_salary}
                  helperText={
                    errors.guard_salary ? errors.guard_salary[0] : null
                  }
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.guard_lsalary == ''}
                  margin="normal"
                  label="Guard Lsalary"
                  name="guard_lsalary"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.guard_lsalary}
                  helperText={
                    errors.guard_lsalary ? errors.guard_lsalary[0] : null
                  }
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.leader_lsalary == ''}
                  margin="normal"
                  label="Leader Lsalary"
                  name="leader_lsalary"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.leader_lsalary}
                  helperText={
                    errors.leader_lsalary ? errors.leader_lsalary[0] : null
                  }
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.leader_salary == ''}
                  margin="normal"
                  label="Leader Salary"
                  name="leader_salary"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.leader_salary}
                  helperText={
                    errors.leader_salary ? errors.leader_salary[0] : null
                  }
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.guard_transport == ''}
                  margin="normal"
                  label="Guard Transport"
                  name="guard_transport"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.guard_transport}
                  helperText={
                    errors.guard_transport ? errors.guard_transport[0] : null
                  }
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.guard_transport_rate == ''}
                  margin="normal"
                  label="Guard Transport Rate"
                  name="guard_transport_rate"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.guard_transport_rate}
                  helperText={
                    errors.guard_transport_rate
                      ? errors.guard_transport_rate[0]
                      : null
                  }
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.rate_type == ''}
                  margin="normal"
                  label="Rate Type"
                  name="rate_type"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.rate_type}
                  helperText={errors.rate_type ? errors.rate_type[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.jv_number == ''}
                  margin="normal"
                  label="Jv Number"
                  name="jv_number"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.jv_number}
                  helperText={errors.jv_number ? errors.jv_number[0] : null}
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.total_working_hours == ''}
                  margin="normal"
                  label="Working Hours"
                  name="total_working_hours"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.total_working_hours}
                  helperText={
                    errors.total_working_hours
                      ? errors.total_working_hours[0]
                      : null
                  }
                />
              </Grid>
              <Grid item xs={12} md={2} sm={4} style={{ marginTop: '16px' }}>
                <TextField
                  select
                  required
                  name="tax_center"
                  value={formData.tax_center}
                  onChange={handleChange}
                  fullWidth
                  label="Tax Center"
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                >
                  {isLoading ? (
                    <MenuItem value="">Loading</MenuItem>
                  ) : (
                    tax_center.map((center) => (
                      <MenuItem key={center.id} value={center.id}>
                        {center.tax_center_name}
                      </MenuItem>
                    ))
                  )}
                </TextField>
              </Grid>

              <Grid item xs={12} md={2} sm={4} style={{ marginTop: '16px' }}>
                <TextField
                  select
                  required
                  name="pension_center"
                  value={formData.pension_center}
                  onChange={handleChange}
                  fullWidth
                  label="Pension Center"
                  variant="standard"
                  type="text"
                  InputLabelProps={{
                    color: 'success',
                  }}
                >
                  {isLoading ? (
                    <MenuItem value="">Loading</MenuItem>
                  ) : (
                    tax_center.map((center) => (
                      <MenuItem key={center.id} value={center.id}>
                        {center.tax_center_name}
                      </MenuItem>
                    ))
                  )}
                </TextField>
              </Grid>

              <Grid item xs={12} md={2} sm={4}>
                <TextField
                  error={!errors.client_additional == ''}
                  margin="normal"
                  label="Client Additional"
                  name="client_additional"
                  fullWidth
                  variant="standard"
                  type="number"
                  InputLabelProps={{
                    color: 'success',
                  }}
                  onChange={handleChange}
                  value={formData.client_additional}
                  helperText={
                    errors.client_additional
                      ? errors.client_additional[0]
                      : null
                  }
                />
              </Grid>
            </Grid>
            <Button
              type="submit"
              variant="contained"
              sx={{ mt: 3, ml: 1 }}
              color="success"
            >
              submit
            </Button>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
}

export default ClientForm;
