import React from "react";
import Header from "../../../components/Header";
import {
  Button,
  Typography,
  useTheme,
  Box,
  CircularProgress,
} from "@mui/material";
import { useStateContext } from "../../../context/ContextProvider";
import { tokens } from "../../../utils/theme";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";
import { DataGrid } from "@mui/x-data-grid";

function DailyRateList({ dailyRate, selectedRow }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const {
    data: daily,
    isLoading,
    isFetched,
  } = useQuery(
    ["daily_rate", dailyRate.emp_basic_id],
    async () =>
      await api
        .get(`daily_rate/${dailyRate.emp_basic_id}`)
        .then(({ data }) => data),
    {
      enabled: !!dailyRate.emp_basic_id,
      staleTime: 60000,
    }
  );
  const columns = [
    {
      field: "id",
      headerName: "ID",
      flex: 0.2,
    },
    {
      field: "from_date",
      headerName: "From Date",
      flex: 0.5,
    },
    {
      field: "to_date",
      headerName: "end_date",
      flex: 0.8,
    },

    {
      field: "no_days",
      headerName: "No-days",
      flex: 0.5,
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 0.7,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          selectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "delete",
      headerName: "Delete",
      flex: 0.8,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deleteDailyRate = useMutation(
          (id) => api.delete(`daily_rate/${id}`),
          {
            onSuccess: () => {
              setNotification("daily rate record deleted successfully");
              queryClient.invalidateQueries({ queryKey: "daily_rate" });
            },
          }
        );
        const onDeleteDailyRate = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              "Are you sure you want to delete the selected  daily rate record?"
            )
          ) {
            return;
          }
          deleteDailyRate.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onDeleteDailyRate}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];
  return (
    <>
      <Header title="List Of Unpaid Daily Rate" heading="h5" />
      {isLoading && <CircularProgress />}
      {isFetched && (
        <DataGrid columns={columns} hideFooter autoHeight rows={daily} />
      )}
    </>
  );
}

export default DailyRateList;
