import {
  <PERSON>BackOutlined,
  DarkModeOutlined,
  LightModeOutlined,
  LogoutOutlined,
  NotificationsOutlined,
  SettingsOutlined,
} from "@mui/icons-material";
import {
  Box,
  IconButton,
  useTheme,
  Badge,
  Popover,
  List,
  Card,
  CardHeader,
  LinearProgress,
} from "@mui/material";
import React, { useContext } from "react";
import { ColorModeContext, tokens } from "../../utils/theme";
import { useStateContext } from "../../context/ContextProvider";
import api from "../../api/config/axiosConfig";
import { Link, useNavigate } from "react-router-dom";
import {
  useNotificationsCount,
  useUnreadNotifications,
} from "../../api/userApi/clinetHook";
import { useState } from "react";
import Messages from "../../components/Messages";

function TopBar() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const colorMode = useContext(ColorModeContext);

  const { setUser, setToken, user } = useStateContext();
  const { data: count, isFetched, isLoading } = useNotificationsCount();
  const { data: unreadNotifications } = useUnreadNotifications();

  const [notificationPopupOpen, setNotificationPopupOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  const handleNotificationIconClick = (event) => {
    setAnchorEl(event.currentTarget);
    setNotificationPopupOpen(!notificationPopupOpen);
  };
  const handleCloseNotificationPopup = () => {
    setNotificationPopupOpen(false);
  };
  const handleOnClick = (id) => {
    setNotificationPopupOpen(false);
    navigate(`/messages/detail/${id}`);
  };
  const handleOnClickPayment = (id) => {
    setNotificationPopupOpen(false);
    navigate(`/messages/detail/payment/${id}`);
  };
  const handleOnClickStore = (id) => {
    setNotificationPopupOpen(false);
    navigate(`/store/approval/${id}`);
  };
  //navigate
  const navigate = useNavigate();
  //logout
  const handleLogout = () => {
    api.delete("/logout").then(() => {
      setUser({});
      setToken(null);
    });
  };

  return (
    <Box display="flex" justifyContent="space-between" p={0.5}>
      <div>
        <IconButton onClick={() => navigate(-1)} sx={{ padding: 1 }}>
          <ArrowBackOutlined />
        </IconButton>
      </div>
      <div>
        <IconButton onClick={handleNotificationIconClick}>
          {isFetched && (
            <Badge badgeContent={count} max={99} color="error" showZero>
              <NotificationsOutlined />
            </Badge>
          )}
        </IconButton>
        <IconButton onClick={colorMode.toggleColorMode}>
          {theme.palette.mode === "dark" ? (
            <DarkModeOutlined />
          ) : (
            <LightModeOutlined />
          )}
        </IconButton>

        {user && user.role === "admin" && (
          <IconButton LinkComponent={Link} to="/settings">
            <SettingsOutlined />
          </IconButton>
        )}
        <IconButton onClick={handleLogout}>
          <LogoutOutlined />
        </IconButton>
      </div>
      <Popover
        open={notificationPopupOpen}
        anchorEl={anchorEl}
        onClose={handleCloseNotificationPopup}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        sx={{
          marginRight: "10px",
        }}
      >
        <Card
          sx={{
            border: `1px solid ${colors.primary[400]}`,
            cursor: "pointer",
          }}
        >
          <div
            style={{
              backgroundColor: `${colors.blueAccent[500]}`,
            }}
          >
            <CardHeader title="notifications" />
          </div>
          {isLoading && <LinearProgress />}
          {unreadNotifications &&
            unreadNotifications.map((notification, index) => {
              if (
                notification.type ==
                "App\\Notifications\\ContractFollowUpNotification"
              ) {
                return (
                  <Messages
                    companyName={notification.message.company_name}
                    createdAt={notification.created_at}
                    reminder={notification.message.contract_schedule}
                    onclick={() => handleOnClick(notification.id)}
                    key={index}
                    type='contract'
                  />
                );
              } else if (
                notification.type ==
                "App\\Notifications\\ItemRequestNotification"
              ) {
                return (
                  <Messages
                    companyName={notification.data.item_name}
                    createdAt={notification.created_at}
                    reminder={`${notification.data.quantity} requested by ${notification.data.requested_by}`}
                    onclick={() => handleOnClickStore(notification.id)}
                    key={index}
                    type="store"
                  />
                );
              }
              else {
                return (
                  <Messages
                    companyName={notification.message.company_name}
                    createdAt={notification.created_at}
                    reminder={notification.message.collection_schedule}
                    onclick={() => handleOnClickPayment(notification.id)}
                    key={index}
                    type="payment"
                  />
                );
              }
            })}
        </Card>
      </Popover>
    </Box>
  );
}

export default TopBar;
