import { Box, Grid } from "@mui/material";
import React from "react";
import { useState } from "react";
import Header from "../../components/Header";
import PayRollDate from "./PayRollDate";
import OpenModal from "../../components/OpenModal";
import SetDeduct from "./SetDeduct";
import SetProvidentFund from "./SetProvidentFund";
import SetExchangeRate from "./SetExchangeRate";
import SetPension from "./SetPension";
import LinkCard from "../../components/LinkCard";
import SetMedicalShare from "./SetMedicalShare";
import SetHoliday from "./SetHoliday";
function Settings() {
  const [isopen, setIsOpen] = useState(false);
  const [isDeduct, setIsDeduct] = useState(false);
  const [isProvident, setIsProvident] = useState(false);
  const [isExchange, setIsExchange] = useState(false);
  const [isPension, setIsPension] = useState(false);
  const [isMedical, setIsMedical] = useState(false);
  const [isHoliday, setIsHoliday] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [anchorElDeduct, setAnchorElDeduct] = useState(null);
  const [anchorElProvident, setAnchorElProvident] = useState(null);
  const [anchorElExchange, setAnchorElExchange] = useState(null);
  const [anchorElPension, setAnchorElPension] = useState(null);
  const [anchorElmedical, setAnchorElMedical] = useState(null);
  const [anchorElHoliday, setAnchorElHoliday] = useState(null);

  const handleOpen = (e) => {
    setIsOpen(true);
    setAnchorEl(e.currentTarget);
  };
  const handleDeduct = (e) => {
    setIsDeduct(true);
    setAnchorElDeduct(e.currentTarget);
  };

  const handleProvident = (e) => {
    setIsProvident(true);
    setAnchorElProvident(e.currentTarget);
  };
  const handleExchange = (e) => {
    setIsExchange(true);
    setAnchorElExchange(e.currentTarget);
  };
  const handlePension = (e) => {
    setIsPension(true);
    setAnchorElPension(e.currentTarget);
  };
  const handleMedical = (e) => {
    setIsMedical(true);
    setAnchorElMedical(e.currentTarget);
  };
  const handleHoliday = (e) => {
    setIsHoliday(true);
    setAnchorElHoliday(e.currentTarget);
  };

  const handleClose = () => {
    setIsOpen(false);
    setAnchorEl(null);
    setAnchorElDeduct(null);
    setIsDeduct(false);
    setIsProvident(false);
    setAnchorElProvident(null);
    setAnchorElExchange(null);
    setIsExchange(false);
    setIsPension(false);
    setIsMedical(false);
    setIsHoliday(false);
    setAnchorElPension(null);
    setAnchorElMedical(null);
    setAnchorElHoliday(null);
  };

  return (
    <Box m="10px">
      <Header
        title="Settings Page"
        subtitle="a place to changes system settings"
      />
      <Box maxWidth="85%" ml="50px">
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <OpenModal
              title="set payroll date"
              subtitle="set payroll day in here"
              handleOpen={handleOpen}
            />
            <PayRollDate
              isopen={isopen}
              anchorEl={anchorEl}
              handleClose={handleClose}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <OpenModal
              title="set deductible"
              subtitle="set employee deductible"
              handleOpen={handleDeduct}
            />
            <SetDeduct
              anchorEl={anchorElDeduct}
              handleClose={handleClose}
              isopen={isDeduct}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <OpenModal
              handleOpen={handleProvident}
              title="set provident"
              subtitle="set provident"
            />
            <SetProvidentFund
              anchorEl={anchorElProvident}
              isopen={isProvident}
              handleClose={handleClose}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <OpenModal
              handleOpen={handleExchange}
              title="set exchange"
              subtitle="set exchange"
            />
            <SetExchangeRate
              anchorEl={anchorElExchange}
              isopen={isExchange}
              handleClose={handleClose}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <OpenModal
              handleOpen={handlePension}
              title="set pension"
              subtitle="set pension"
            />
            <SetPension
              anchorEl={anchorElPension}
              isopen={isPension}
              handleClose={handleClose}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <OpenModal
              title="Medical Share"
              subtitle="set Medical Share"
              handleOpen={handleMedical}
            />
            <SetMedicalShare
              anchorEl={anchorElmedical}
              handleClose={handleClose}
              isopen={isMedical}
            />
          </Grid>

          
          {/* 
          <Grid item xs={12} sm={6} md={3}>
           <OpenModal
              title="Holiday"
              subtitle="set Holiday"
              handleOpen={handleHoliday}
            />
            <SetHoliday
              anchorEl={anchorElHoliday}
              handleClose={handleClose}
              isopen={isHoliday}
            />
          </Grid> 
          */}

          <Grid item xs={12} sm={6} md={3}>
            <LinkCard
              title="Terminate Employee"
              subtitle="Terminate Employee"
              to="/settings/terminate"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Pc Rate"
              subtitle="add,view edit and delete pc rate  types "
              to="/settings/pcrate-type"
            />
          </Grid>
        
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Tax Center"
              subtitle="view ,add ,edit and delete Tax Center here"
              to="/settings/tax-center"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Warehouse"
              subtitle="view ,add ,edit and delete Warehouse here"
              to="/settings/warehouse"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Category"
              subtitle="view ,add ,edit and delete Category here"
              to="/settings/category"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title=" register Users"
              subtitle="register users"
              to="/settings/users/new"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Users table"
              subtitle="view users table view ,edit ,delete"
              to="/settings/users"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="reset password"
              subtitle="reset lost password here"
              to="/settings/users/reset-password"
            />
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}

export default Settings;
