import { Box, Grid } from "@mui/material";
import React from "react";
import Header from "../../../components/Header";
import { useLocation } from "react-router-dom";
import { DataGrid } from "@mui/x-data-grid";
import { useEmployeeData } from "../../../api/userApi/clinetHook";

function LeaveSummaryLookup() {
  const location = useLocation();
  const {leave,formData} = location.state;
  const { employeedYear } = leave;

  console.log(leave, formData)

  const options = {
    weekday: "short",
    month: "short",
    day: "numeric",
    year: "numeric",
  };
  const date = new Date(employeedYear.date);
  const month = date.getMonth() + 1;
  const day = date.getDate();

  const rows14 = Object.entries(leave.leaveLookUp14).map(([date, value]) => ({
    id: date,
    date:new Date(date).toLocaleDateString('en-Us',options),
    value,
  }));

  const leaveFraction14 = [
    {
      id: "2019-09-05",
      date: new Date("2019, 09, 05").toLocaleDateString("en-US", options),
      value: leave.leaveFraction14.toFixed(3),
    },
  ];

  const new14 = [...rows14, ...leaveFraction14];

  const rows16 = Object.entries(leave.leaveLookUp16).map(([date, value]) => ({
    id: date,
    date: new Date(date, month - 1, day).toLocaleDateString("en-US", options),
    value,
  }));

  const leaveFraction16 = [
    {
      id: "2023-10-22",
      date: new Date().toLocaleDateString('en-US', options),
      value: leave.leaveFraction2.toFixed(3),
    },
  ];

  const new16 = [...rows16, ...leaveFraction16];
  const columns = [
    { field: "date", headerName: "Date", flex: 1 },
    { field: "value", headerName: "Value", flex: 1 },
  ];

  const { data: emp, isFetched } = useEmployeeData(formData.emp_basic_id);
  
  console.log(emp,new14)
  return (
    <Box m={2}>
      <Header
        title="Leave Comulative Lookup Table"
        subtitle={
          isFetched && `Employee Name: ${emp.first_name} ${emp.middle_name} ${emp.last_name}`
        }
      />
      <Grid container spacing={4}>
        <Grid item xs={12} sm={6}>
          <DataGrid rows={new14} columns={columns} autoHeight rowsPerPageOptions={[5,25,100]}   />
        </Grid>
        <Grid item xs={12} sm={6}>
          <DataGrid rows={new16} columns={columns} autoHeight rowsPerPageOptions={[5,25,100]}  />
        </Grid>
      </Grid>
    </Box>
  );
}

export default LeaveSummaryLookup;
