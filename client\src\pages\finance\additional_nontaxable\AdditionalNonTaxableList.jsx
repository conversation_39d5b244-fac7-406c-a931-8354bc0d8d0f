import React from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";
import Header from "../../../components/Header";
import { DataGrid } from "@mui/x-data-grid";
import {
  Button,
  Typography,
  useTheme,
  Box,
  CircularProgress,
} from "@mui/material";
import { tokens } from "../../../utils/theme";
import { useStateContext } from "../../../context/ContextProvider";

function AdditionalNonTaxableList({ nontaxable, selectedRow }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const { data: nontax, isLoading } = useQuery(
    ["nontxable", nontaxable.emp_basic_id],
    async () =>
      await api
        .get(`nontaxable/${nontaxable.emp_basic_id}`)
        .then(({ data }) => data.data),
    {
      enabled: !!nontaxable.emp_basic_id,
      staleTime: 60000,
      refetchOnWindowFocus: false,
    }
  );
  // 
  const queryClient = useQueryClient();
  const columns = [
    {
      field: "id",
      headerName: "ID",
      flex: 0.2,
    },
    {
      field: "transport_allowance",
      headerName: "Transport",
      flex: 0.5,
    },
    {
      field: "mobile_allowance",
      headerName: "Mobile",
      flex: 0.8,
    },
    {
      field: "desert_allowance",
      headerName: "Desert",
      flex: 0.5,
    },

    {
      field: "cleaning_allowance",
      headerName: "Cleaning",
      flex: 0.5,
    },
    {
      field: "medical_allowance",
      headerName: "Medical",
      flex: 0.5,
    },
    {
      field: "other_allowance",
      headerName: "Other",
      flex: 0.5,
    },
    {
      field: "effective_date",
      headerName: "Effective Date",
      flex: 0.5,
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 0.7,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          selectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "delete",
      headerName: "Delete",
      flex: 0.8,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deleteNontax = useMutation(
          (id) => api.delete(`nontaxable/${id}`),
          {
            onSuccess: () => {
              setNotification("Additional Non-Taxable record deleted successfully");
            },
            onSettled: () => {
              queryClient.invalidateQueries({ queryKey: "nontxable" });
            },
          }
        );
        const onDeleteNonTax = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              "Are you sure you want to delete the selected  nontaxable record?"
            )
          ) {
            return;
          }
          deleteNontax.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onDeleteNonTax}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];
  return (
    <>
      <Header heading="h5" title="List Of Unpaid Additional NonTaxable" />
      {isLoading ? (
        <CircularProgress />
      ) : (
        <DataGrid autoHeight hideFooter columns={columns} rows={nontax} />
      )}
    </>
  );
}

export default AdditionalNonTaxableList;
