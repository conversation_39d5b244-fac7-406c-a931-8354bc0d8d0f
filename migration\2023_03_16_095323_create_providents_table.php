<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('providents', function (Blueprint $table) {
            $table->id();
            $table->integer('provident_emp')->default(4);
            $table->integer('provident_sns')->default(10);
            $table->timestamps();
        });

        DB::table('providents')->insert([
            'provident_emp' => 4,
            'provident_sns' => 10,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('providents');
    }
};
