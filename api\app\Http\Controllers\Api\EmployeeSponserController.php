<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreEmployeeSponserRequest;
use App\Http\Requests\UpdateEmployeeSponserRequest;
use App\Models\EmployeeSponser;
use Illuminate\Http\Request;
use Response;

class EmployeeSponserController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
    return Response(EmployeeSponser::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $empSponserRequest)
    {
        $data=$empSponserRequest->all();
        $empSponser=EmployeeSponser::create([
            "emp_basic_id"=>$data["emp_basic_id"],
            "name"=>$data["name"],
            "age"=>$data["age"],
            "gender"=>$data["gender"],
            "company_name"=>$data["company_name"],
            "position"=>$data["position"],
            "city"=>$data["city"],
            "sub_city"=>$data["sub_city"],
            "kebele"=>$data["kebele"],
            "house_no"=>$data["house_no"],
            "id_no"=>$data["id_no"],
            "office_tel"=>$data["office_tel"],
            "mobile"=>$data["mobile"],
            "property_type"=>$data["property_type"],
            "position2"=>$data["position2"],
            "certificate_type"=>$data["certificate_type"],
            "effective_date"=>$data["effective_date"],
            "expire_date"=>$data["expire_date"],
            // "user_id"=>$request->user_id,
            "user_id"=>0,
        ]);

        
        return response(['empsponser'=>$empSponser],201);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EmployeeSponser  $employeeSponser
     * @return \Illuminate\Http\Response
     */
    public function show(EmployeeSponser $employeeSponser)
    {
        return response($employeeSponser);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\EmployeeSponser  $employeeSponser
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, EmployeeSponser $employeeSponser)
    {
        // $data=$request->validated();
        $data=$request->all();

        $employeeSponser->update([
            "name"=>$data["name"],
            "age"=>$data["age"],
            "gender"=>$data["gender"],
            "company_name"=>$data["company_name"],
            "position"=>$data["position"],
            "city"=>$data["city"],
            "sub_city"=>$data["sub_city"],
            "kebele"=>$data["kebele"],
            "house_no"=>$data["house_no"],
            "id_no"=>$data["id_no"],
            "office_tel"=>$data["office_tel"],
            "mobile"=>$data["mobile"],
            "property_type"=>$data["property_type"],
            "position2"=>$data["position2"],
            "certificate_type"=>$data["certificate_type"],
            "effective_date"=>$data["effective_date"],
            "expire_date"=>$data["expire_date"],
            "user_id"=>$request->user_id,
        ]);

        return response($employeeSponser);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EmployeeSponser  $employeeSponser
     * @return \Illuminate\Http\Response
     */
    public function destroy(EmployeeSponser $employeeSponser)
    {
        $employeeSponser->delete();

        return response('',204);
    }
}
