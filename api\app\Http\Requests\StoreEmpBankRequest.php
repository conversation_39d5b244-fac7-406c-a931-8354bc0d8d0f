<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Type\TrueType;

class StoreEmpBankRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'emp_basic_id'=>'required',
            'bank_account'=>'required|numeric',
            'bank_code'=>'required',
            'bank_branch'=>'required',
        ];
    }
}
