<?php

namespace App\Http\Controllers\Api\settings;

use App\Http\Controllers\Controller;
use App\Http\Resources\TerminationResource;
use App\Models\EmpBasic;
use App\Models\settings\Termination;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TerminationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {

        $table = DB::table('terminations')->select(
            'terminations.id',
            'terminations.termination_date',
            'terminations.reason',
            'terminations.ref_no',
            'clients.company_name as company_name',
            'emp_basics.first_name as first_name',
        )->join('emp_basics', 'emp_basics.id', '=', 'terminations.emp_basic_id')->join('clients', 'clients.id', '=', 'terminations.client_id')->orderByDesc('terminations.termination_date')->get();
        return response($table);
        // return TerminationResource::collection($table);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \App\Http\Resources\TerminationResource
     */
    public function store(Request $request)
    {
        $data = [];
        if ($request->input('emp_basic_id')) {
            EmpBasic::where('id', $request->input('emp_basic_id'))->update(['termination_status' => 1]);
            $data = Termination::create([
                'emp_basic_id' => $request->input('emp_basic_id'),
                'client_id' => $request->input('client_id'),
                'reason' => $request->input('reason'),
                'termination_date' => $request->input('termination_date'),
                'ref_no' => $request->input('ref_no'),
            ]);
        }


        return new TerminationResource($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\settings\Termination  $termination
     * @return \App\Http\Resources\TerminationResource
     */
    public function show(Termination $termination)
    {
        return new TerminationResource($termination);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\settings\Termination  $termination
     * @return \App\Http\Resources\TerminationResource
     */
    public function update(Request $request, Termination $termination)
    {
        $termination->update([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'client_id' => $request->input('client_id'),
            'reason' => $request->input('reason'),
            'termination_date' => $request->input('termination_date'),
            'ref_no' => $request->input('ref_no'),
        ]);

        return new TerminationResource($termination);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\settings\Termination  $termination
     * @return \Illuminate\Http\Response
     */
    public function destroy(Termination $termination)
    {
        $termination->delete();

        return response('', 204);
    }
}
