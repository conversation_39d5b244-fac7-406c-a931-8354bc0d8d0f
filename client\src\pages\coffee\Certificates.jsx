import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { getCertifications } from '../../api/coffee';
import { DataGrid } from '@mui/x-data-grid';
import { tokens } from '../../utils/theme';
import { useTheme } from '@mui/material/styles';

const Certificates = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['certifications'],
    queryFn: getCertifications,
  });

  const columns = [
    { field: 'id', headerName: 'ID', flex: 0.5 },
    { field: 'certificate_number', headerName: 'Certificate Number', flex: 1 },
    { field: 'certification_type', headerName: 'Type', flex: 1 },
    {
      field: 'coffee_batch',
      headerName: 'Batch Number',
      flex: 1,
      valueGetter: (params) => params.row.coffee_batch?.batch_number,
    },
    { field: 'issue_date', headerName: 'Issue Date', flex: 1 },
    { field: 'expiry_date', headerName: 'Expiry Date', flex: 1 },
  ];

  if (isLoading) {
    return <Typography>Loading...</Typography>;
  }

  if (isError) {
    return <Typography>Error: {error.message}</Typography>;
  }

  return (
    <Box m="20px">
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="h2" component="h1" gutterBottom>
          Certificates
        </Typography>
        <Button
          variant="contained"
          sx={{ backgroundColor: colors.greenAccent[600] }}
        >
          Add New Certificate
        </Button>
      </Box>
      <Box
        m="40px 0 0 0"
        height="75vh"
        sx={{
          '& .MuiDataGrid-root': {
            border: 'none',
          },
          '& .MuiDataGrid-cell': {
            borderBottom: 'none',
          },
          '& .name-column--cell': {
            color: colors.greenAccent[300],
          },
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: colors.blueAccent[700],
            borderBottom: 'none',
          },
          '& .MuiDataGrid-virtualScroller': {
            backgroundColor: colors.primary[400],
          },
          '& .MuiDataGrid-footerContainer': {
            borderTop: 'none',
            backgroundColor: colors.blueAccent[700],
          },
        }}
      >
        <DataGrid
          rows={data?.data || []}
          columns={columns}
          pageSize={10}
          rowsPerPageOptions={[10]}
          checkboxSelection
        />
      </Box>
    </Box>
  );
};

export default Certificates;
