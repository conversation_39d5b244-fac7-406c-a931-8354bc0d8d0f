import {
  Box,
  Container,
  Paper,
  Step,
  StepLabel,
  Stepper,
  Typography,
  useTheme,
} from "@mui/material";
import React, { useState } from "react";
import Header from "../../../components/Header";
import { useStateContext } from "../../../context/ContextProvider";
import { tokens } from "../../../utils/theme";
import BasicImployeeInfo from "./BasicImployeeInfo";
import EmployeeAllowance from "./EmployeeAllowance";
import EmployeeBankForm from "./EmployeeBankForm";
import EmployeeDetail from "./EmployeeDetail";

function RegisterEmployee() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const steps = [
    "Basic Information",
    "Employee Detail Information",
    "Employee Allowance",
    "EmployeeBank Information",
  ];
  
  const [activeStep, setActiveStep] = useState(0);
  const { setNotification } = useStateContext();

  // const [basic, setBasic] = useState({});
  
  const [clicked, setClicked] = useState(false);
  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <BasicImployeeInfo
            onNext={handleNext}
            clicked={clicked}
            // basic={basic}
          />
        );
      case 1:
        return (
          <EmployeeDetail
            onNext={handleNext}
            onBack={handleBack}
            clicked={clicked}
            // basic={basic}
          />
        );
      case 2:
        return (
          <EmployeeAllowance
            onNext={handleNext}
            onBack={handleBack}
            clicked={clicked}
            // basic={basic}
          />
        );
      case 3:
        return (
          <EmployeeBankForm
            onBack={handleBack}
            onNext={handleNext}
            clicked={clicked}
            // basic={basic}
          />
        );
      default:
      // return  throw new Error("Invalid step");
    }
  };
  const handleNext = () => {
    setClicked(false);
    setActiveStep(activeStep + 1);
  };
  const handleBack = () => {
    setActiveStep(activeStep - 1);
    setClicked(true);
  };

  return (
    <Box m="10px">
      <Header
        title="Register Employee"
        subtitle="welcome to employee registration page"
      />
      <Container component="main" maxWidth="lg">
        <Paper
          variant="outlined"
          sx={{
            // my: { xs: 3, md: 6 },
            p: { xs: 1, md: 1 },
            color: colors.grey[100],
            background: colors.primary[400],
          }}
        >
          <Typography component="h1" variant="h4" align="center">
            Employe registration Form
          </Typography>
          <Stepper activeStep={activeStep} sx={{ pt: 3, pb: 5 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
          {activeStep === steps.length ? (
            <>
              {/* here we will show some type of form preview */}
              <Typography>Registration form is completed</Typography>
              {setNotification("Employee Registered Successfully")}
              {/* <Navigate to='/dashboard'/> */}
            </>
          ) : (
            <>{getStepContent(activeStep)}</>
          )}
        </Paper>
      </Container>
    </Box>
  );
}

export default RegisterEmployee;
