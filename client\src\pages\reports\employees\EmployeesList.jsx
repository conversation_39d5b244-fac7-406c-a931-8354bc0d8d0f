import { Box } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { useLocation } from 'react-router-dom';
import api from '../../../api/config/axiosConfig';
import _CircularProgress from '../../../components/_CircularProgress';
import Header from '../../../components/Header';
import CustomToolBar from '../../../helper/CustomToolBar';

import { useState } from 'react';
import {
  employeeReportTaxMessage,
  incomeTaxMessage,
} from '../formattedMessage';
const EmployeesList = () => {
  const location = useLocation();
  const recivedData = location.state;
  const [companyName, setCompanyName] = useState('');
  const {
    data: response,
    isLoading,
    isFetched,
  } = useQuery(['employees_Report'], () =>
    api.get('/report/employees', { params: recivedData }).then(({ data }) => {
      if (data.length > 0) {
        setCompanyName(data[0].company_name);
        console.log(data);
      }
      return data;
    })
  );
  const columns = [
    {
      field: 'id',
      headerName: 'ID',
    },
    {
      field: 'tin_no',
      headerName: 'Tin Number',
      flex: 1,
    },
    {
      field: 'fullname',
      headerName: 'fullName',
      flex: 1.5,
    },
    {
      field: 'gender',
      headerName: 'Gender',
      flex: 1,
    },

    {
      field: 'start_date',
      headerName: 'Empoyeed Date',
      flex: 1,
    },
    {
      field: 'basic_salary',
      headerName: 'Basic_Salary',
      flex: 1,
    },
    {
      field: 'position',
      headerName: 'Position',
      flex: 1,
    },
    {
      field: 'company_name',
      headerName: 'Company',
      flex: 1.5,
    },
    {
      field: 'region',
      headerName: 'Region',
      flex: 1,
    },
    {
      field: 'city',
      headerName: 'City',
      flex: 1,
    },
    {
      field: 'sub_city',
      headerName: 'Sub City',
      flex: 1,
    },
    {
      field: 'kebele',
      headerName: 'Kebele',
      flex: 1,
    },
    {
      field: 'house_no',
      headerName: 'House No',
      flex: 1,
    },
    {
      field: 'tel',
      headerName: 'Contact Number',
      flex: 1,
    },
  ];
  return (
    <Box margin="10px">
      {recivedData && recivedData.clientId ? (
        <Header
          title={`${companyName} EMPLOYEES REPORT`}
          subtitle={employeeReportTaxMessage(
            recivedData.firstDate,
            recivedData.lastDate,
            recivedData.year,
            recivedData.gender,
            recivedData.region
          )}
        />
      ) : (
        <Header
          title="EMPLOYEES REPORT"
          subtitle={employeeReportTaxMessage(
            recivedData.firstDate,
            recivedData.lastDate,
            recivedData.year,
            recivedData.gender,
            recivedData.region
          )}
        />
      )}

      {isLoading && <_CircularProgress />}
      {isFetched && (
        <DataGrid
          columns={columns}
          rows={response}
          autoHeight
          components={{ Toolbar: CustomToolBar }}
        />
      )}
    </Box>
  );
};

export default EmployeesList;
