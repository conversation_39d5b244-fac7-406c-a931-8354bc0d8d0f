import React from 'react'
import { useUnreadNotifications } from '../../../api/userApi/clinetHook'
import { List } from '@mui/material'
import _CircularProgress from "../../../components/_CircularProgress";
import MessageItem from './MessageItem';
import PaymentFMessageItem from './PaymentFMessageItem';
const UnreadNotifications = () => {
  const {data:notifications,isLoading}=useUnreadNotifications()

  if (isLoading) {
    return <_CircularProgress />;
  }
  return (
    <List>
        {notifications &&
        notifications.map((notification, index) => {
          if (
            notification.type ===
            "App\\Notifications\\ContractFollowUpNotification"
          ) {
            return (
              <MessageItem
                key={index}
                createdAt={notification.created_at}
                readAt={notification.read_at}
                id={notification.id}
                companyName={notification.message.client_id}
                contractSchedule={notification.message.contract_schedule}
              />
            );
          } else if (
            notification.type ===
            "App\\Notifications\\PaymentFollowupNotification"
          ) {
            return (
              <PaymentFMessageItem
                key={index}
                createdAt={notification.created_at}
                readAt={notification.read_at}
                id={notification.id}
                companyName={notification.message.client_id}
                collectionSchedule={notification.message.collection_schedule}
              />
            );
          }
        })}
    </List>
  )
}

export default UnreadNotifications