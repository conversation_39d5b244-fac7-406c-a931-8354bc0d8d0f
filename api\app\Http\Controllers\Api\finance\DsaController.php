<?php

namespace App\Http\Controllers\Api\finance;

use App\Http\Controllers\Controller;
use App\Http\Resources\DsaResource;
use App\Models\EmpBasic;
use App\Models\finance\Dsa;
use App\Models\settings\ExchangeRate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DsaController extends Controller
{

    public $currency;
    public $basicSalary;
    public $exchangeRate;
    public $total;
    public function __construct()
    {
        $this->currency = 0;
        $this->exchangeRate = 0;
        $this->basicSalary = 0;
        $this->total = 0;
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index()
    {
        return DsaResource::collection(Dsa::all());
    }

    public function calculateTaxableDsa($basicSalary = '', $currency = '', $exchangeRate = '', $noOfdays = '', $rateAmount = '')
    {

        if ($currency == 1) {
            $basicSalary = $basicSalary * $exchangeRate;
        }
        if (($basicSalary * 4 / 100) < 500) {
            $this->total = $noOfdays * (($rateAmount * (800 / 35.891)) - 500);
        } else {
            $this->total = $noOfdays * (($rateAmount * (800 / 35.891)) - (($basicSalary * 4) / 100));
        }
    }

    public function calculateNonTaxDsa($basicSalary = '', $currency = '', $exchangeRate = '', $noOfdays = '')
    {
        if ($currency == 1) {
            $basicSalary = $basicSalary * $exchangeRate;
        }
        if (($basicSalary * 4 / 100) < 500) {
            $this->total = $noOfdays * 500;
        } else {
            $this->total = $noOfdays * (($basicSalary * 4) / 100);
        }
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \App\Http\Resources\DsaResource
     */
    public function store(Request $request)
    {
        $this->basicSalary = EmpBasic::where('id', $request->input('emp_basic_id'))->value('basic_salary');
        // $this->currency = EmpBasic::where('emp_basic_id', $request->input('emp_basic_id'))->value('currency');
        $exchangeRate = ExchangeRate::first();
        $this->exchangeRate = $exchangeRate->exchange_rate;
        if ($request->input('taxable') == 1) {
            $this->calculateTaxableDsa(
                basicSalary: $this->basicSalary,
                currency: $this->currency,
                exchangeRate: $this->exchangeRate,
                noOfdays: $request->input('no_days'),
                rateAmount: $request->input('rate_amount')
            );
        } else {
            $this->calculateNonTaxDsa(basicSalary: $this->basicSalary, currency: $this->currency, exchangeRate: $this->exchangeRate, noOfdays: $request->input('no_days'));
        }

        $dsa = Dsa::create([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'no_days' => $request->input('no_days'),
            'rate_amount' => $request->input('rate_amount'),
            'total' => $this->total,
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'taxable' => $request->input('taxable'),
            'user_id' => $request->input('user_id'),
        ]);
        return new DsaResource($dsa);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\finance\Dsa  $dsa
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function show($id)
    {
        $dsa=Dsa::where('emp_basic_id',$id)->where('paid',0)->get();
        return DsaResource::collection($dsa);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\finance\Dsa  $dsa
     * @return \App\Http\Resources\DsaResource
     */
    public function update(Request $request, Dsa $dsa)
    {
        $this->basicSalary = EmpBasic::where('id', $request->input('emp_basic_id'))->value('basic_salary');
        $exchangeRate = ExchangeRate::first();
        $this->exchangeRate = $exchangeRate->exchange_rate;
        if ($request->input('taxable') == 1) {
            $this->calculateTaxableDsa(
                basicSalary: $this->basicSalary,
                currency: $this->currency,
                exchangeRate: $this->exchangeRate,
                noOfdays: $request->input('no_days'),
                rateAmount: $request->input('rate_amount')
            );
        } else {
            $this->calculateNonTaxDsa(basicSalary: $this->basicSalary, currency: $this->currency, exchangeRate: $this->exchangeRate, noOfdays: $request->input('no_days'));
        }

        $dsa->update([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'no_days' => $request->input('no_days'),
            'rate_amount' => $request->input('rate_amount'),
            'total' => $this->total,
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'taxable' => $request->input('taxable'),
            'user_id' => $request->input('user_id'),
        ]);

        return new DsaResource($dsa);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\finance\Dsa  $dsa
     * @return \Illuminate\Http\Response
     */
    public function destroy(Dsa $dsa)
    {
        $dsa->delete();

        return response('', 204);
    }
}
