import React, { useState } from "react";
import {
  Button,
  Typography,
  useTheme,
  Box,
  CircularProgress,
} from "@mui/material";
import { tokens } from "../../../utils/theme";
import { useStateContext } from "../../../context/ContextProvider";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import Header from "../../../components/Header";
import { DataGrid } from "@mui/x-data-grid";
import { Link, useNavigate } from "react-router-dom";
import api from "../../../api/config/axiosConfig";
function ListOfEmployee({ clientId }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [selectedRows, setSelectedRows] = useState([]);
  const {setNotification}=useStateContext()
 const navigate=useNavigate()
  const {
    data: res,
    isLoading,
    isFetched,
  } = useQuery(
    ["list_in_payroll", clientId],
    () => api.get(`payroll_emp_list/${clientId}`).then(({ data }) => data.data),
    {
      enabled: !!clientId,
    }
  );
  const handleButtonClick = () => {
    if(selectedRows.length>0){
      navigate('/finance/payroll/calc',{
        state:{emp_id:selectedRows,clientId:clientId}
      })
    }else{
      setNotification('please select employee to prepare payroll')
    }
  };
  const columns = [
    {
      field: "id",
      headerName: "ID",
    },
    {
      field: "first_name",
      headerName: "First Name",
      flex: 1,
    },
    {
      field: "middle_name",
      headerName: "Middle Name",
      flex: 1,
    },
    {
      field: "last_name",
      headerName: "Last Name",
      flex: 1,
    },

    {
      field: "position",
      headerName: "Positions",
      flex: 2,
    },
  ];
  return (
    <>
      <Header title="List Of Employees" heading="h5" />
      {isLoading && <>loading..</>}
      {isFetched && (
        <><DataGrid columns={columns} hideFooter autoHeight rows={res}
          checkboxSelection
          onSelectionModelChange={(newSelection) => {
            setSelectedRows(newSelection);
          } } /><Button sx={{ display: 'block', marginLeft: 'auto', marginTop: '16px' }} variant="contained" color="success" onClick={handleButtonClick}>Prepare Payroll</Button></>
      )}
    </>
  );
}

export default ListOfEmployee;
