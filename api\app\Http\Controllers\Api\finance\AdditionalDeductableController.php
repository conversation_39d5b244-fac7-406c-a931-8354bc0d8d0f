<?php

namespace App\Http\Controllers\Api\finance;

use App\Http\Controllers\Controller;
use App\Http\Resources\AdditionalDeductableResource;
use App\Models\finance\AdditionalDeductable;
use Illuminate\Http\Request;

class AdditionalDeductableController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index()
    {
        return AdditionalDeductableResource::collection(AdditionalDeductable::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = AdditionalDeductable::create([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'deduct_date' => $request->input('deduct_date'),
            'rate' => $request->input('rate'),
            'duration' => $request->input('duration'),
            'amount' => $request->input('amount'),
            'reason' => $request->input('reason'),
            'user_id' => $request->input('user_id'),
        ]);

        return new AdditionalDeductableResource($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function show($id)
    {
        $addtionalDeduct= AdditionalDeductable::where('emp_basic_id',$id)->where('paid',0)->get();

        return  AdditionalDeductableResource::collection($addtionalDeduct);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $additionalDeduct = AdditionalDeductable::find($id);

        $additionalDeduct->update([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'deduct_date' => $request->input('deduct_date'),
            'rate' => $request->input('rate'),
            'duration' => $request->input('duration'),
            'amount' => $request->input('amount'),
            'reason' => $request->input('reason'),
            'user_id' => $request->input('user_id'),
        ]);

        return new AdditionalDeductableResource($additionalDeduct);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $delete = AdditionalDeductable::find($id);

        $delete->delete();
        return response('', 204);
    }
}
