<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Models\PhytosanitaryCertificate;
use Illuminate\Http\Request;

class PhytosanitaryCertificateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return PhytosanitaryCertificate::with('coffeeBatch')->latest()->paginate();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'coffee_batch_id' => 'required|exists:coffee_batches,id',
            'certificate_number' => 'required|string|unique:phytosanitary_certificates|max:255',
            'issue_date' => 'nullable|date',
            'expiry_date' => 'nullable|date',
            'inspector_name' => 'nullable|string|max:255',
        ]);

        return PhytosanitaryCertificate::create($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\PhytosanitaryCertificate  $phytosanitaryCertificate
     * @return \Illuminate\Http\Response
     */
    public function show(PhytosanitaryCertificate $phytosanitaryCertificate)
    {
        return $phytosanitaryCertificate->load('coffeeBatch');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\PhytosanitaryCertificate  $phytosanitaryCertificate
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, PhytosanitaryCertificate $phytosanitaryCertificate)
    {
        $data = $request->validate([
            'coffee_batch_id' => 'sometimes|required|exists:coffee_batches,id',
            'certificate_number' => 'sometimes|required|string|unique:phytosanitary_certificates,certificate_number,' . $phytosanitaryCertificate->id . '|max:255',
            'issue_date' => 'nullable|date',
            'expiry_date' => 'nullable|date',
            'inspector_name' => 'nullable|string|max:255',
        ]);

        $phytosanitaryCertificate->update($data);

        return $phytosanitaryCertificate;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\PhytosanitaryCertificate  $phytosanitaryCertificate
     * @return \Illuminate\Http\Response
     */
    public function destroy(PhytosanitaryCertificate $phytosanitaryCertificate)
    {
        $phytosanitaryCertificate->delete();

        return response()->noContent();
    }
}
