import { useState } from "react";
import RecommandationForm from "./RecommandationForm";
import RecommandationList from "./RecommandationList";

function Recomandation({ id }) {
  const [editData, setEditData] = useState({});
  const onSelectRow = (value) => {
    setEditData(value);
  };

  return (
    <div>
      <RecommandationForm data={editData} empId={id} />
      <RecommandationList id={id} selectedRow={onSelectRow}/>
    </div>
  );
}

export default Recomandation;
