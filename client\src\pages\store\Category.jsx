import { AddOutlined } from '@mui/icons-material';
import {
  Button,
  Typography,
  useTheme,
  Box,
  IconButton,
  Grid,
  TextField,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import api from '../../api/config/axiosConfig';
import Header from '../../components/Header';
import { useStateContext } from '../../context/ContextProvider';
import { tokens } from '../../utils/theme';
import _CircularProgress from '../../components/_CircularProgress';

function Category() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const queryClient = useQueryClient();
  const [clicked, setClicked] = useState(false);
  const [selectedRow, setSelectedRow] = useState({});
  const { setNotification, errors, setErrors } = useStateContext();

  const { data: categories, isLoading } = useQuery(
    ['categories'],
    async () =>
      await api.get('store/categories').then(({ data }) => {
        return data.data;
      })
  );

  const columns = [
    {
      field: 'id',
      headerName: 'Id',
    },
    {
      field: 'name',
      headerName: 'Category Name',
      flex: 1,
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 1,
    },
    {
      field: 'edit',
      headerName: 'Edit',
      flex: 1,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          setClicked(true);
          setSelectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: 'delete',
      headerName: 'Delete',
      flex: 1,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deleteCategoryMutation = useMutation(
          (id) => api.delete(`store/categories/${id}`),
          {
            onSuccess: () => {
              setNotification('Category Deleted Successfully ');
            },
            onSettled: () => {
              queryClient.invalidateQueries(['categories']);
            },
          }
        );
        const deleteCategory = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              'Are you sure you want to delete the selected category?'
            )
          ) {
            return;
          }
          deleteCategoryMutation.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deleteCategory}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];

  const handleClick = (e) => {
    e.preventDefault();
    setClicked(true);
  };

  const handleChange = (e) => {
    setSelectedRow({
      ...selectedRow,
      [e.target.name]: e.target.value,
    });
  };

  const postCategory = useMutation(
    (data) => api.post('store/categories', data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['categories']);
        setNotification('Category created successfully');
        setClicked(false);
      },
      onError: (err) => {
        setClicked(true);
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );

  const onSubmit = (e) => {
    e.preventDefault();
    const payload = {
      name: selectedRow.name,
      description: selectedRow.description,
    };
    if (selectedRow.id) {
      api
        .put(`store/categories/${selectedRow.id}`, payload)
        .then(() => {
          setNotification('Category updated successfully');
          queryClient.invalidateQueries(['categories']);
          setClicked(false);
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
          setClicked(true);
        });
    } else {
      postCategory.mutate(payload);
    }
  };

  return (
    <Box m="10px">
      <Header title="Category Table" subtitle="List of categories" />
      <Box display="flex" justifyContent="center" alignItems="center">
        <IconButton
          sx={{
            boxShadow: '2px 2px 4px rgba(0,0,0,0.25)',
            background: colors.primary[400],
          }}
          onClick={handleClick}
        >
          <AddOutlined color={colors.grey[100]} />
        </IconButton>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={clicked ? 9 : 12}>
          <Box
            m="10px 0 0 0"
            height="67vh"
            sx={{
              '& .MuiDataGrid-root': {
                border: 'none',
              },
              '& .MuiDataGrid-cell': {
                borderBottom: 'none',
              },
              '& .name-column--cell': {
                color: colors.greenAccent[300],
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: colors.blueAccent[700],
                borderBottom: 'none',
              },
              '& .MuiDataGrid-virtualScroller': {
                backgroundColor: colors.primary[400],
              },
              '& .MuiDataGrid-footerContainer': {
                borderTop: 'none',
                backgroundColor: colors.blueAccent[700],
              },
              '& .MuiCheckBox-root': {
                color: `${colors.greenAccent[200]}!important`,
              },
              '& .MuiDataGrid-toolbarContainer .MuiButton-text': {
                color: `${colors.grey[100]}!important`,
              },
            }}
          >
            {isLoading ? (
              <_CircularProgress />
            ) : (
              <DataGrid columns={columns} rows={categories} />
            )}
          </Box>
        </Grid>

        {clicked ? (
          <Grid item xs={12} sm={3} component="form" onSubmit={onSubmit}>
            <Typography variant="h5" color={colors.grey[100]} align="center">
              {selectedRow.id ? 'Edit Category' : 'Add Category'}
            </Typography>
            <TextField
              error={!errors.name == ''}
              id="name"
              margin="normal"
              fullWidth
              label="Category Name"
              name="name"
              InputLabelProps={{
                color: 'success',
              }}
              value={selectedRow.name || ''}
              onChange={handleChange}
              helperText={
                errors.name ? errors.name[0] : null
              }
            />
            <TextField
              error={!errors.description == ''}
              id="description"
              margin="normal"
              fullWidth
              label="Description"
              name="description"
              InputLabelProps={{
                color: 'success',
              }}
              value={selectedRow.description || ''}
              onChange={handleChange}
              helperText={
                errors.description ? errors.description[0] : null
              }
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 2, py: 2 }}
              color="success"
              size="small"
            >
              submit
            </Button>

            <Button
              onClick={() => {
                setClicked(false);
                setSelectedRow({});
              }}
              fullWidth
              variant="contained"
              sx={{ mt: 2, py: 2 }}
              color="error"
              size="small"
            >
              cancel
            </Button>
          </Grid>
        ) : undefined}
      </Grid>
    </Box>
  );
}

export default Category;
