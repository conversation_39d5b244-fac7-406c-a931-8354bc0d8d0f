<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateEmpBasicRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'first_name' => 'required|string|max:255',
            'middle_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'amharic_name' => 'string|max:255',
            'company_id' => 'string|max:255',
            'tin_no' => 'required',
            'pension_id' => 'required',
            'file_no' => 'required|numeric',
            'start_date' => 'date',
            'gender' => 'string|max:255',
            'date_of_birth' => 'date',
            'position' => 'string',
            'basic_salary' => 'numeric',
            'rate_type' => 'required',
            'initial_salary' => 'numeric',
            'insurance' => 'required',
            'pension' => 'required',
            'provident' => 'required',
        ];
    }
}
