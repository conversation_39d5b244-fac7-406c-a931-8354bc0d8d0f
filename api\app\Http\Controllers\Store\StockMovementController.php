<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Models\StockMovement;
use App\Services\InventoryService;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class StockMovementController extends Controller
{
    public function __construct(private InventoryService $inventory) {}

    public function index()
    {
        return StockMovement::with(['warehouse','toWarehouse','coffeeBatch','item'])->latest()->paginate();
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'movement_type'   => ['required', Rule::in(['inbound','outbound','transfer'])],
            'quantity'        => 'required|numeric|min:0.001',
            'unit'            => 'nullable|string',
            'warehouse_id'    => 'required|exists:warehouses,id',
            'to_warehouse_id' => 'nullable|exists:warehouses,id',
            'coffee_batch_id' => 'nullable|exists:coffee_batches,id',
            'item_id'         => 'nullable|exists:items,id',
            'reference'       => 'nullable|string|max:190',
        ]);

        // Enforce xor selection at controller level:
        if (($data['coffee_batch_id'] ?? null) && ($data['item_id'] ?? null)) {
            return response()->json(['message' => 'Provide either coffee_batch_id or item_id'], 422);
        }
        if (!($data['coffee_batch_id'] ?? null) && !($data['item_id'] ?? null)) {
            return response()->json(['message' => 'coffee_batch_id or item_id is required'], 422);
        }

        $movement = $this->inventory->recordMovement($data);

        // If transfer, auto-create corresponding inbound at destination
        if ($movement->movement_type === 'transfer') {
            if (empty($data['to_warehouse_id'])) {
                return response()->json(['message' => 'to_warehouse_id required for transfer'], 422);
            }
            $inbound = $this->inventory->recordMovement([
                'movement_type'   => 'inbound',
                'quantity'        => $movement->quantity,
                'unit'            => $movement->unit,
                'warehouse_id'    => $movement->to_warehouse_id,
                'to_warehouse_id' => null,
                'coffee_batch_id' => $movement->coffee_batch_id,
                'item_id'         => $movement->item_id,
                'reference'       => 'AUTO-IN-' . ($movement->reference ?? $movement->id),
            ]);
        }

        return $movement->load(['warehouse','toWarehouse','coffeeBatch','item']);
    }

    public function destroy(StockMovement $stockMovement)
    {
        $this->inventory->deleteMovement($stockMovement);
        return response()->noContent();
    }
}
