import { Box, Button, Typography, useTheme } from "@mui/material";
import React from "react";
import { tokens } from "../utils/theme";

const EditRenderCell = ({ onClick, title }) => {
    const theme = useTheme();
    const colors = tokens(theme.palette.mode);
  return (
    <Box
      width="60%"
      m="0 auto"
      p="30x"
      display="flex"
      justifyContent="center"
      backgroundColor={colors.greenAccent[700]}
      borderRadius="4px"
    >
      <Button onClick={onClick}>
        <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
          {title}
        </Typography>
      </Button>
    </Box>
  );
};

export default EditRenderCell;
