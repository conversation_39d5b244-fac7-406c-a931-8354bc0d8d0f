import { TextField, MenuItem, Box } from '@mui/material';
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import api from '../api/config/axiosConfig';

const TaxCenterDropDown = ({ onSelectedCenter }) => {
  const [selectedCenter, setSelectedCenter] = useState('');

  const { data: response, isLoading } = useQuery(
    ['comp'],
    async () =>
      await api.get('tax_center').then(({ data }) => {
        console.log(data)
        return data;
      })
  );

  // if (!isLoading) {
  //     console.log(response);
  // }

  const handleCenterChange = (e) => {
    //console.log('selected center', e.target.value);
    const centerValue = e.target.value;
    setSelectedCenter(centerValue);
    onSelectedCenter({
      value: centerValue,
      clicked: true,
    });
    //console.log('onSelectedCenter ',onSelectedCenter);
  };

  if (isLoading) {
    return "loading ...."
  }

  return (
    <Box>
      <TextField
        select
        name="tax_center"
        value={selectedCenter}
        onChange={handleCenterChange}
        fullWidth
        label="Select Tax Center"
        // variant='standard'
        margin="normal"
        type="text"
        InputLabelProps={{
          color: 'success',
        }}
      >
        {response.map((center) => (
          <MenuItem key={center.id} value={center.id}>
            {center.tax_center_name}
          </MenuItem>
        ))}
      </TextField>
    </Box>
  );
};

export default TaxCenterDropDown;
