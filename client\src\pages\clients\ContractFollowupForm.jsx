import {
  Box,
  Button,
  Grid,
  LinearProgress,
  MenuItem,
  TextField,
} from '@mui/material';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import api from '../../api/config/axiosConfig';
import { useStateContext } from '../../context/ContextProvider';
import {
  collectionschedule,
  scheduleReminder,
} from '../../helper/scheduleHelper';

function ContractFollowupForm({ clientId, editData, setEditData }) {
  const { setNotification, errors, setErrors, user } = useStateContext();
  const [uploadProgress, setUploadProgress] = useState(0);
  const queryClient = useQueryClient();

  const [contract, setContract] = useState({
    contract_schedule: '',
    start_date: new Date().toISOString().slice(0, 10),
    end_date: new Date().toISOString().slice(0, 10),
    contract_document: '',
    reminder: '',
    message: '',
    amount: '',
    file_url: '',
  });

  useEffect(() => {
    if (editData) {
      setContract((prevContract) => ({
        ...prevContract,
        ...editData,
        contract_document: null,
        file_url: editData.contract_document,
      }));
    }
  }, [editData]);
  const handleChange = (e) => {
    setContract({
      ...contract,
      [e.target.name]: e.target.value,
    });
  };

  const createContract = useMutation(
    (payload) =>
      api.post('contract-follow-up', payload, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progress) => {
          const percentage = Math.round(
            (progress.loaded * 100) / progress.total
          );
          setUploadProgress(percentage);
        },
      }),
    {
      onSuccess: () => {
        setNotification('Contract Follow Up created successfully');
        queryClient.invalidateQueries('ContractByClient');
        setErrors({});
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
            setUploadProgress(0);
          }
        }
      },
    }
  );
  const handleSubmit = async (e) => {
    e.preventDefault();

    const data = new FormData(e.currentTarget);

    if (contract.contract_document instanceof File) {
      console.log('file', contract.contract_document);
      data.append('contract_document', contract.contract_document);
    } else if (contract.file_url) {
      data.append('contract_document', contract.file_url);
    }

    const payload = {
      client_id: clientId,
      contract_schedule: data.get('contract_schedule'),
      start_date: data.get('start_date'),
      end_date: data.get('end_date'),
      contract_document: data.get('contract_document'),
      reminder: data.get('reminder'),
      message: data.get('message'),
      amount: data.get('amount'),
      user_id: user.id,
    };

    if (editData && editData.id) {
      console.log(payload);
      api.put(`contract-follow-up/${editData.id}`, payload).then(() => {
        setNotification('Contract Follow Up record updated successfully');
        queryClient.invalidateQueries('ContractByClient');
        setEditData({});
        setContract({
          contract_schedule: '',
          start_date: new Date().toISOString().slice(0, 10),
          end_date: new Date().toISOString().slice(0, 10),
          contract_document: '',
          reminder: '',
          message: '',
          amount: '',
        });
      });
    } else {
      console.log(payload);
      createContract.mutate(payload);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} encType="multipart/form-data">
      <Grid container spacing={1}>
        <Grid item xs={12} sm={3}>
          <TextField
            error={errors.contract_document}
            margin="normal"
            fullWidth
            id="contract_document"
            name="contract_document"
            label="Attach contract Document"
            type="file"
            inputProps={{ accept: '.doc,.pdf,image/*' }}
            variant="standard"
            InputLabelProps={{
              color: 'success',
            }}
            value={contract.contract_document}
            size="small"
            onChange={handleChange}
            helperText={errors.contract_document && errors.contract_document[0]}
          />
          <LinearProgress
            value={uploadProgress}
            variant="determinate"
            color="success"
          />
        </Grid>

        <Grid item xs={12} sm={3}>
          <TextField
            error={errors.start_date}
            margin="normal"
            label="Starting Date"
            name="start_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={contract.start_date}
            helperText={errors.start_date && errors.start_date[0]}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            error={errors.end_date}
            margin="normal"
            label="End Date"
            name="end_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={contract.end_date || new Date().toISOString().slice(0, 10)}
            helperText={errors.end_date && errors.end_date[0]}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            error={errors.contract_schedule}
            margin="normal"
            label="Contract Schedule"
            name="contract_schedule"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={
              contract.contract_schedule
                ? contract.contract_schedule
                : 'monthly'
            }
            helperText={errors.contract_schedule && errors.contract_schedule[0]}
          >
            {collectionschedule.map((schedule) => (
              <MenuItem key={schedule.value} value={schedule.value}>
                {schedule.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            error={errors.reminder}
            margin="normal"
            label="Schedule Reminder"
            name="reminder"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={contract.reminder ? contract.reminder : -5}
            helperText={errors.reminder && errors.reminder[0]}
          >
            {scheduleReminder.map((schedule) => (
              <MenuItem key={schedule.value} value={schedule.value}>
                {schedule.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            error={errors.amount}
            margin="normal"
            label="collection Amount"
            name="amount"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={contract.amount}
            helperText={errors.amount && errors.amount[0]}
          />
        </Grid>
        <Grid item xs={12} sm={5}>
          <TextField
            margin="normal"
            label="Message"
            name="message"
            fullWidth
            multiline
            rows={2}
            variant="outlined"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={contract.message}
          />
        </Grid>
        {editData && editData.contract_document && (
          <Grid item xs={12} sm={5}>
            <a
              href={`http://localhost:8000/${editData.contract_document}`}
              target="_blank"
              rel="noreferrer"
            >
              <Button variant="outlined" color="success">
                View Document
              </Button>
            </a>
          </Grid>
        )}
      </Grid>
      <span style={{ float: 'inline-start' }}>
        <Button type="submit" variant="contained" color="success">
          {editData && editData.id ? <>update</> : <>create</>}
        </Button>
      </span>
    </Box>
  );
}

export default ContractFollowupForm;
