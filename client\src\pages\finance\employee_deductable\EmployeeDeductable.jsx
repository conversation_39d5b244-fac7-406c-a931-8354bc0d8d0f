import React from "react";
import { Box, Grid, Paper, useTheme } from "@mui/material";
import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { tokens } from "../../../utils/theme";
import api from "../../../api/config/axiosConfig";
import Header from "../../../components/Header";
import CeSearchComponent from "../../../components/CeSearchComponent";
import SearchBar from "../../../components/SearchBar";
import EmployeeSelecter from "../../../components/EmployeeSelecter";
import EmployeeDeductableForm from "./EmployeeDeductableForm";
import EmployeeDeductableList from "./EmployeeDeductableList";
function EmployeeDeductable() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [clicked, setClicked] = useState(false);
  const [editData, setEditData] = useState({});
  const [formData, setFormData] = useState({});
  const queryClient = useQueryClient();
  const { data: client, isFetched } = useQuery(
    ["client"],
    async () => await api.get("client").then(({ data }) => data.data),
    {
      refetchOnWindowFocus: false,
      staleTime: 60000,
    }
  );

  const onSelectedRow = (value) => {
    setEditData(value);
  };
  const handleClietSelect = ({value}) => {
    setFormData({
      ...formData,
      client_id: value.id,
    });
  };

  const { data: assignedEmployee, isLoading } = useQuery(
    ["assignedEmployee", formData.client_id],
    async () =>
      await api.get(`assign/client/${formData.client_id}`).then(({ data }) => {
        return data.data;
      }),
    {
      staleTime: 60000,
      enabled: !!formData.client_id,
    }
  );

  const handleRefetch = () => {
    queryClient.invalidateQueries({ queryKey: ["assignedEmployee"] });
  };
  const handleEmployeeSelect = ({ value, clicked }) => {
    setFormData({
      ...formData,
      emp_basic_id: value.id,
    });
    setClicked(clicked);
  };
  return (
    <Box m="5px">
      <Header
        title="set Employee Deductable "
        subtitle="add employee deductable here"
      />
      <Box>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}>
            <Paper
              sx={{
                padding: '1rem',
                color: colors.grey[100],
                background: colors.primary[400],
              }}
            >
              <CeSearchComponent
                ClientSearch={
                  <SearchBar
                    client={client}
                    onClientSelect={handleClietSelect}
                  />
                }
                EmployeeSelecter={
                  <EmployeeSelecter
                    assignedEmployee={assignedEmployee}
                    isFetched={isFetched}
                    onEmployeeSelect={handleEmployeeSelect}
                  />
                }
              />
            </Paper>
          </Grid>
          <Grid item xs={12} sm={8}>
            {clicked && (
              <Box>
                <Paper
                  sx={{
                    padding: '1rem',
                    color: colors.grey[100],
                    background: colors.primary[400],
                  }}
                >
                  <EmployeeDeductableForm
                    formData={formData}
                    editData={editData}
                    handleRefetch={handleRefetch}
                    setEditData={setEditData}
                  />
                </Paper>
                <Paper
                  sx={{
                    marginTop: '30px',
                    padding: '1rem',
                    color: colors.grey[100],
                    background: colors.primary[400],
                  }}
                >
                  <EmployeeDeductableList
                    deductable={formData}
                    selectedRow={onSelectedRow}
                  />
                </Paper>
              </Box>
            )}
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}

export default EmployeeDeductable;
