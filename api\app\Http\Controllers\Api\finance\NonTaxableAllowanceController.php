<?php

namespace App\Http\Controllers\Api\finance;

use App\Http\Controllers\Controller;
use App\Http\Resources\NonTaxableAllowanceResource;
use App\Models\finance\NonTaxableAllowance;
use Illuminate\Http\Request;

class NonTaxableAllowanceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index()
    {
        return  NonTaxableAllowanceResource::collection(NonTaxableAllowance::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \App\Http\Resources\NonTaxableAllowanceResource
     */
    public function store(Request $request)
    {
        $data = NonTaxableAllowance::create([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'transport_allowance' => $request->input('transport_allowance'),
            'desert_allowance' => $request->input('desert_allowance'),
            'mobile_allowance' => $request->input('mobile_allowance'),
            'position_allowance' => $request->input('position_allowance'),
            'cleaning_allowance' => $request->input('cleaning_allowance'),
            'other_allowance' => $request->input('other_allowance'),
            'medical_allowance' => $request->input('medical_allowance'),
            'effective_date' => $request->input('effective_date'),

            'user_id' => $request->input('user_id'),

        ]);

        return new NonTaxableAllowanceResource($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\finance\NonTaxableAllowance  $nonTaxableAllowance
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function show($id)
    {
        $nonTaxable = NonTaxableAllowance::where('emp_basic_id', '=', $id)->where('paid', '=', 0)
            ->get();
        return NonTaxableAllowanceResource::collection($nonTaxable);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\finance\NonTaxableAllowance  $nonTaxableAllowance
     * @return \App\Http\Resources\NonTaxableAllowanceResource
     */
    public function update(Request $request, NonTaxableAllowance $nonTaxableAllowance)
    {
        $nonTaxableAllowance->update([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'transport_allowance' => $request->input('transport_allowance'),
            'desert_allowance' => $request->input('desert_allowance'),
            'mobile_allowance' => $request->input('mobile_allowance'),
            'position_allowance' => $request->input('position_allowance'),
            'cleaning_allowance' => $request->input('cleaning_allowance'),
            'other_allowance' => $request->input('other_allowance'),
            'medical_allowance' => $request->input('medical_allowance'),
            'effective_date' => $request->input('effective_date'),
            'user_id' => $request->input('user_id'),
        ]);

        return new  NonTaxableAllowanceResource($nonTaxableAllowance);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\finance\NonTaxableAllowance  $nonTaxableAllowance
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $nonTaxableAllowance=NonTaxableAllowance::find($id);
        $nonTaxableAllowance->delete();

        return response('', 204);
    }
}
