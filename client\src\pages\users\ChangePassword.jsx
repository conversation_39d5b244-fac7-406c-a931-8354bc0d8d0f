import React, { useState } from "react";
import Header from "../../components/Header";
import {
  <PERSON><PERSON>,
  Box,
  Button,
  Container,
  Grid,
  IconButton,
  InputAdornment,
  Paper,
  TextField,
} from "@mui/material";
import { useStateContext } from "../../context/ContextProvider";
import api from "../../api/config/axiosConfig";
import { Visibility, VisibilityOff } from "@mui/icons-material";

export const PasswordField=({label,name,value,onChange})=>{
  const [showPassword, setShowPassword] = useState(false);
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  return (
    <TextField
      label={label}
      fullWidth
      name={name}
      value={value}
      onChange={onChange}
      size="small"
      type={showPassword ? "text" : "password"}
      InputProps={{
        endAdornment: (
          <InputAdornment position="end">
            <IconButton
              aria-label="toggle password visibility"
              onClick={togglePasswordVisibility}
            >
              {showPassword ? <VisibilityOff /> : <Visibility />}
            </IconButton>
          </InputAdornment>
        ),
      }}
    />
  );
}
const ChangePassword = () => {
  const { setErrors, errors, setNotification } = useStateContext();
  const [formData, setFormData] = useState({
    current_password: "",
    password: "",
    password_confirmation: "",
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    api
      .post("change-password", formData)
      .then(() => {
        setErrors({});
        setNotification("Password is Reseted successfully");
      })
      .catch((err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          } else {
            setErrors({
              message: [err.response.data.message],
            });
          }
        }
      });
      setErrors({});
  };
  return (
    <Container m="20px">
      <Header
        heading="h3"
        title="Change Password"
        subtitle="change your password here"
      />
      <Box component="form" onSubmit={handleSubmit}>
        {errors && (
          <Paper
            sx={{
              marginY: 3,
              width: "400px",
            }}
          >
            {Object.keys(errors).map((key) => (
              <Alert variant="filled" severity="error" key={key} sx={{mb:1}}>
                {errors[key][0]}
              </Alert>
            ))}
          </Paper>
        )}
        <Grid container spacing={1}>
          <Grid item xs={12} sm={3}>
          <PasswordField
              label="Enter Your Old Password"
              name="current_password"
              value={formData.current_password}
              onChange={handleChange}
            />

          </Grid>
          <Grid item xs={12} sm={3}>
          <PasswordField
              label="Enter Your New Password"
              name="password"
              value={formData.password}
              onChange={handleChange}
            />
           
          </Grid>
          <Grid item xs={12} sm={3}>
            <PasswordField
              label="Confirm Your New Password"
              name="password_confirmation"
              value={formData.password_confirmation}
              onChange={handleChange}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <Button type="submit" variant="contained" color="success" fullWidth>
              change Password
            </Button>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default ChangePassword;
