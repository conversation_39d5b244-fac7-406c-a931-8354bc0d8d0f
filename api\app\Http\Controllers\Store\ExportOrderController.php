<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Models\{ExportOrder, CoffeeBatch};
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ExportOrderController extends Controller
{
    public function index()
    {
        return ExportOrder::with(['customer','coffeeBatches','shipment','invoices'])->latest()->paginate();
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'order_number'   => 'required|unique:export_orders',
            'customer_id'    => 'required|exists:customers,id',
            'incoterm'       => 'nullable|string|max:10',
            'shipping_date'  => 'nullable|date',
            'delivery_port'  => 'nullable|string|max:120',
            'status'         => 'nullable|in:draft,confirmed,processing,shipped,delivered,cancelled',
        ]);
        $data += ['total_weight_kg' => 0, 'price_per_kg' => 0, 'total_price' => 0];

        return ExportOrder::create($data);
    }

    public function allocateBatch(Request $request, ExportOrder $exportOrder)
    {
        $payload = $request->validate([
            'coffee_batch_id'     => 'required|exists:coffee_batches,id',
            'allocated_weight_kg' => 'required|numeric|min:0.001'
        ]);

        return DB::transaction(function () use ($exportOrder, $payload) {
            /** @var CoffeeBatch $batch */
            $batch = CoffeeBatch::lockForUpdate()->findOrFail($payload['coffee_batch_id']);
            if ($batch->quantity_kg < $payload['allocated_weight_kg']) {
                return response()->json(['message' => 'Insufficient batch quantity'], 422);
            }

            // Attach or update pivot
            $existing = $exportOrder->coffeeBatches()
                ->where('coffee_batch_id', $batch->id)
                ->first();

            if ($existing) {
                $exportOrder->coffeeBatches()->updateExistingPivot($batch->id, [
                    'allocated_weight_kg' => DB::raw('allocated_weight_kg + ' . (float)$payload['allocated_weight_kg'])
                ]);
            } else {
                $exportOrder->coffeeBatches()->attach($batch->id, [
                    'allocated_weight_kg' => $payload['allocated_weight_kg']
                ]);
            }

            // Reserve at business layer (optional): set status allocated
            $batch->status = 'allocated';
            $batch->save();

            // Recalculate order totals (optional: set price here or via endpoint)
            $total = $exportOrder->coffeeBatches()->sum('allocated_weight_kg');
            $exportOrder->total_weight_kg = $total;
            $exportOrder->total_price = $exportOrder->price_per_kg * $total;
            $exportOrder->save();

            return $exportOrder->load('coffeeBatches');
        });
    }

    public function ship(ExportOrder $exportOrder)
    {
        $exportOrder->update(['status' => 'shipped']);
        return $exportOrder;
    }

    public function deliver(ExportOrder $exportOrder)
    {
        $exportOrder->update(['status' => 'delivered']);
        return $exportOrder;
    }
}
