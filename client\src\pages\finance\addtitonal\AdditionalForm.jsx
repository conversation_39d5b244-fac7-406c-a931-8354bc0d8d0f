import { Box, Button, Grid, MenuItem, TextField } from '@mui/material';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import api from '../../../api/config/axiosConfig';
import { useStateContext } from '../../../context/ContextProvider';
import { isTaxable } from '../dsa/DsaForm';

const rateType = [
  {
    label: 'Days',
    value: 'days',
  },
  {
    label: 'Hours',
    value: 'hours',
  },
];
function AdditionalForm({ formData, editedData, setEditData }) {
  const queryClient = useQueryClient();
  const [additional, setAdditional] = useState({
    effective_date: '',
    end_date: '',
    rate_type: '',
    minutes: 0,
    salary: '',
    working_time: '',
    taxable: '',
    reason: '',
  });

  useEffect(() => {
    if (editedData) {
      setAdditional(editedData);
    }
  }, [editedData]);

  const { setNotification, user } = useStateContext();

  const handleChange = (e) => {
    setAdditional({
      ...additional,
      [e.target.name]: e.target.value,
    });
  };

  const createAdditional = useMutation((data) => api.post('additional', data), {
    onSuccess: () => {
      setNotification('addititonal inserted successfully');
      queryClient.invalidateQueries({ queryKey: 'additional' });
    },
  });
  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);

    const remark = data.get('reason') ? data.get('reason') : 'default reason';

    const payload = {
      emp_basic_id: formData.emp_basic_id,
      client_id: formData.client_id,
      effective_date: data.get('effective_date'),
      end_date: data.get('end_date'),
      rate_type: data.get('rate_type'),
      minutes: data.get('minutes'),
      working_time: data.get('working_time'),
      salary: data.get('salary'),
      taxable: data.get('taxable'),
      reason: remark,
      user_id: user.id,
    };

    if (editedData && editedData.id) {
      api.put(`additional/${editedData.id}`, payload).then(() => {
        setNotification('additional recorded updated successfully');
        setEditData({});
        queryClient.invalidateQueries({ queryKey: 'additional' });
      });
    } else {
      createAdditional.mutate(payload);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Effective Date"
            name="effective_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={
              additional.effective_date || new Date().toISOString().slice(0, 10)
            }
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Ending Date"
            name="end_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={additional.end_date || new Date().toISOString().slice(0, 10)}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Hours or Days "
            name="working_time"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={additional.working_time}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Rate Type"
            name="rate_type"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={additional.rate_type ? additional.rate_type : 'days'}
          >
            {rateType.map((rate) => (
              <MenuItem key={rate.value} value={rate.value}>
                {rate.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Minutes"
            name="minutes"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={additional.minutes}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="salary"
            name="salary"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={additional.salary}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Taxable/non-Taxable"
            name="taxable"
            fullWidth
            select
            variant="standard"
            type="number"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={additional.taxable ? additional.taxable : ''}
          >
            {isTaxable.map((value) => (
              <MenuItem key={value.value} value={value.value}>
                {value.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Reason"
            name="reason"
            multiline
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={additional.reason}
          />
        </Grid>
      </Grid>
      <span style={{ float: 'inline-start' }}>
        <Button type="submit" variant="contained" color="success">
          {editedData.id ? <>update</> : <>create</>}
        </Button>
      </span>
    </Box>
  );
}

export default AdditionalForm;
