<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HireRequisition extends Model
{
    use HasFactory;

    protected $table = 'hire_requisitions';

    protected $fillable = [
        'requisition_date',
        'department',
        'position',
        'salary_level',
        'salary_range_from',
        'salary_range_to',
        'approved',
        'approved_by',
    ];

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }
}