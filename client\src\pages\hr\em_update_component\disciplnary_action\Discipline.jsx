import React from "react";
import { useState, useEffect } from "react";
import { Box, Button, TextField, Grid, Container } from "@mui/material";
import api from "../../../../api/config/axiosConfig";
import { useStateContext } from "../../../../context/ContextProvider";
import { useMutation, useQuery } from "@tanstack/react-query";
import DisciplineList from "./DisciplineList";
import DisciplineForm from "./DisciplineForm";
function Discipline({ id }) {
  const [editData, setEditData] = useState({});
  const onSelectRow=(value)=>{
    setEditData(value);
  }

  return (
<Container>
<DisciplineForm data={editData} empId={id}/>
    <DisciplineList id={id} selectedRow={onSelectRow} isFetched={true}/>
</Container>
  );
}

export default Discipline;
