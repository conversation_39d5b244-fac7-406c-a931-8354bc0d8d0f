import { createContext, useContext, useState } from "react";

const StateContext = createContext({
  user: null,
  token: null,
  errors: null,
  notification: null,
  setUser: () => {},
  setToken: () => {},
  setErrors: () => {},
  setNotification: () => {},
  empBasic: null,
  setEmpBasic: () => {},
  empdetail: null,
  setEmpDetail: () => {},
  empAllowance: null,
  setEmpAllowance: () => {},
});

export const ContextProvider = ({ children }) => {
  const [user, setUser] = useState({});
  const [errors, setErrors] = useState({});
  const [token, _setToken] = useState(localStorage.getItem("ACCESS_TOKEN"));
  const [notification, _setNotification] = useState();

  const [empBasic,setEmpBasic]=useState()
  const [empdetail,setEmpDetail]=useState()
  const [empAllowance,setEmpAllowance]=useState()

  const setNotification = (message) => {
    _setNotification(message);
    setTimeout(() => {
      setNotification("");
    }, 5000);
  };
  const setToken = (token) => {
    _setToken(token);
    if (token) {
      localStorage.setItem("ACCESS_TOKEN", token);
    } else {
      localStorage.removeItem("ACCESS_TOKEN");
    }
  };  
  return (
    <StateContext.Provider
      value={{
        user,
        token,
        empBasic,
        empdetail,
        empAllowance,
        setUser,
        setToken,
        errors,
        setErrors,
        setNotification,
        notification,
        setEmpBasic,
        setEmpDetail,
        setEmpAllowance,
      }}
    >
      {children}
    </StateContext.Provider>
  );
};

export const useStateContext = () => useContext(StateContext);
