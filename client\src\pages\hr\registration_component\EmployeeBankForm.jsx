import { <PERSON>po<PERSON>, <PERSON>, Grid, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import React from "react";
import { useState, useEffect } from "react";
import api from "../../../api/config/axiosConfig";
import { useStateContext } from "../../../context/ContextProvider";


function EmployeeBankForm({ onNext, onBack }) {
  const {
    errors,
    setErrors,
    empBasic,
    setEmpBasic,
    setEmpAllowance,
    setEmpDetail,
    user,
  } = useStateContext();
  const [formData, setFormData] = useState({
    bank_account: "",
    bank_code: "",
    bank_branch: "",
    user_id: "",
  });

      useEffect(() => {
        setFormData({
          ...formData,
          user_id: user.id,
        });
      }, [user]);
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    const payload = {
      emp_basic_id: empBasic,
      bank_account: data.get("bank_account"),
      bank_code: data.get("bank_code"),
      bank_branch: data.get("bank_branch"),
      user_id: user.id,
    };
    api
      .post("emp_banks", payload)
      .then(() => {
        api.post("emp_status", {
          emp_basic_id: empBasic,
          certificate_medical: 1,
          marital_status: 0,
          driving_license: 0,
          criminal_record: 0,
          living_certificate: 1,
          labor_union: 0,
          credit_association: 0,
          sport_association: 0,
          social_support: 0,
          finggure_print: 1,
          holiday: 0,
          eng_lang: "good",
          user_id: user.id,
        });
        onNext();
        setEmpAllowance(null);
        setEmpBasic(null);
        setEmpDetail(null);
      })
      .catch((err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      });
  };
  return (
    <Box>
      <Typography>Employee Bank Info Form </Typography>
      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.bank_code == ""}
              margin="normal"
              label="Bank Name"
              name="bank_code"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: "success",
              }}
              onChange={handleChange}
              value={formData.bank_code}
              helperText={errors.bank_code ? errors.bank_code[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.bank_branch == ""}
              margin="normal"
              label="Bank Branch"
              name="bank_branch"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: "success",
              }}
              onChange={handleChange}
              value={formData.bank_branch}
              helperText={errors.bank_branch ? errors.bank_branch[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.bank_account == ""}
              margin="normal"
              label="Bank Account"
              name="bank_account"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: "success",
              }}
              onChange={handleChange}
              value={formData.bank_account}
              helperText={errors.bank_account ? errors.bank_account[0] : null}
            />
          </Grid>
        </Grid>
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <Button onClick={onBack} sx={{ mt: 3, ml: 1 }} variant="contained">
            Back
          </Button>
          <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
            Submit
          </Button>
        </Box>
      </Box>
    </Box>
  );
}

export default EmployeeBankForm;
