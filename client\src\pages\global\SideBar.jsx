import {
  DashboardRounded,
  HomeOutlined,
  LogoutOutlined,
  MenuOutlined,
  MoneyOutlined,
  OpenInBrowser,
  PeopleAltOutlined,
  PeopleOutlined,
  PeopleOutlineOutlined,
  PeopleOutlineRounded,
  PersonAddAlt1Outlined,
  SettingsOutlined,
} from '@mui/icons-material';
import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  Typography,
  useTheme,
} from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { Menu, MenuItem, ProSidebar, SubMenu } from 'react-pro-sidebar';
import 'react-pro-sidebar/dist/css/styles.css';
import { Link } from 'react-router-dom';
import api from '../../api/config/axiosConfig';
import userProfile from '../../assets/edomias_log.png';
import _CircularProgress from '../../components/_CircularProgress';
import { useStateContext } from '../../context/ContextProvider';
import { tokens } from '../../utils/theme';
const Item = ({ title, to, icon, selected, setSelected }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  return (
    <MenuItem
      active={selected === title}
      style={{ color: colors.grey[100] }}
      onClick={() => setSelected(title)}
      icon={icon}
    >
      <Typography>{title}</Typography>
      <Link to={to} />
    </MenuItem>
  );
};

const LogoutItem = ({ title, icon, selected, handleLogout }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  return (
    <MenuItem
      active={selected === title}
      style={{ color: colors.grey[100] }}
      onClick={handleLogout}
      icon={icon}
    >
      {title}
    </MenuItem>
  );
};

function SideBar() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [selected, setSelected] = useState('Dashboard');
  const [ext, setExt] = useState('');
  const { setToken, setUser, user } = useStateContext();
  const [img, setImg] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    api.get('/user').then(({ data }) => {
      setUser(data.user);
      setExt(data.ext);
      setImg(data.user.image);
      setIsLoading(false);
    });
  }, []);
  const handleLogout = (e) => {
    e.preventDefault();
    api.delete('/logout').then(() => {
      setUser({});
      setToken(null);
    });
  };

  return (
    <Box
      sx={{
        '& .pro-sidebar-inner': {
          background: `${colors.primary[400]} !important`,
        },
        '& .pro-icon-wrapper': {
          backgroundColor: 'transparent !important',
        },
        '& .pro-inner-item': {
          padding: '5px 35px 5px 20px  !important',
        },
        '& .pro-inner-item:hover': {
          color: '#868dfb !important',
        },
        '& .pro-menu-item.active': {
          color: '#6870fa !important',
        },
      }}
    >
      <ProSidebar
        collapsed={isCollapsed}
        style={{ height: '100vh', overflowY: 'scroll' }}
      >
        <Menu iconShape="square">
          {/* logo and menu Item */}
          <MenuItem
            onClick={() => setIsCollapsed(!isCollapsed)}
            icon={isCollapsed ? <MenuOutlined /> : undefined}
            style={{
              margin: '10px 0 20px 0',
              color: colors.grey[100],
            }}
          >
            {!isCollapsed && (
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
                ml="15px"
              >
                <Typography variant="h3" color={colors.grey['100']}>
                  {user.role}
                </Typography>
                <IconButton onClick={() => setIsCollapsed(!isCollapsed)}>
                  <MenuOutlined />
                </IconButton>
              </Box>
            )}
          </MenuItem>
          {/* user */}
          {isLoading ? (
            <_CircularProgress />
          ) : (
            !isCollapsed && (
              <Box mb="25px">
                <Box display="flex" justifyContent="center" alignItems="center">
                  <img
                    alt="user-profile"
                    width="100px"
                    height="100px"
                    // src={userProfile}
                    // src={`data:image/jpeg;base64,${user.image}`}
                    src={img ? `data:image/${ext};base64,${img}` : userProfile}
                    style={{ cursor: 'pointer', borderRadius: '50%' }}
                  />
                </Box>
                <Box textAlign="center">
                  <Typography
                    variant="h2"
                    color={colors.grey[100]}
                    fontWeight="bold"
                    margin="10px 0 0 0"
                  >
                    {`${user.firstName} ${user.lastName}`}
                  </Typography>
                  <Button
                    variant="text"
                    LinkComponent={Link}
                    to="/user/change-password"
                  >
                    <Typography
                      variant="button"
                      color={colors.greenAccent[500]}
                    >
                      change Your password here
                    </Typography>
                  </Button>
                </Box>
              </Box>
            )
          )}
          {/* menu items */}
          <Box paddingLeft={isCollapsed ? undefined : '10%'}>
            <Item
              title="Dashboard"
              to="/dashboard"
              icon={<HomeOutlined />}
              selected={selected}
              setSelected={setSelected}
            />
            {(user.role == 'admin' || user.role == 'hr') && (
              <>
                <Typography
                  variant="h6"
                  color={colors.grey[300]}
                  sx={{ m: '15px 0 5px 20px ' }}
                >
                  HR
                </Typography>
                <Item
                  title="Human Resources"
                  to="/employees"
                  icon={<PeopleAltOutlined />}
                  selected={selected}
                  setSelected={setSelected}
                />
              </>
            )}
            {(user.role == 'admin' || user.role == 'hr') && (
              <>
                <Typography
                  variant="h6"
                  color={colors.grey[300]}
                  sx={{ m: '15px 0 5px 20px ' }}
                >
                  Client
                </Typography>
                <Item
                  title="Clients"
                  to="/clients"
                  icon={<DashboardRounded />}
                  selected={selected}
                  setSelected={setSelected}
                />
              </>
            )}
            {(user.role == 'finance' || user.role == 'admin') && (
              <>
                <Typography
                  variant="h6"
                  color={colors.grey[300]}
                  sx={{ m: '15px 0 5px 20px ' }}
                >
                  Finance
                </Typography>
                <Item
                  title="FINANCE"
                  to="/finance"
                  icon={<MoneyOutlined />}
                  selected={selected}
                  setSelected={setSelected}
                />
              </>
            )}
            {(user.role == 'admin' || user.role == 'storekeeper') && (
              <>
                <Typography
                  variant="h6"
                  color={colors.grey[300]}
                  sx={{ m: '15px 0 5px 20px ' }}
                >
                  Store
                </Typography>
                <Item
                  title="Store"
                  to="/store"
                  icon={<SettingsOutlined />}
                  selected={selected}
                  setSelected={setSelected}
                />
              <Item
                  title="Coffee export"
                  to="/coffeepage"
                  icon={<SettingsOutlined />}
                  selected={selected}
                  setSelected={setSelected}
                />
              </>
            )}
            {/* {(user.role == "operation" ||
              user.role == "admin" )&& (
                <>
                  <Typography
                    variant="h6"
                    color={colors.grey[300]}
                    sx={{ m: "15px 0 5px 20px " }}
                  >
                    Operation
                  </Typography>
                  <Item
                    title="Operation"
                    to="/operations"
                    icon={<OpenInBrowser />}
                    selected={selected}
                    setSelected={setSelected}
                  />
                </>
              )} */}
            <Typography
              variant="h6"
              color={colors.grey[300]}
              sx={{ m: '15px 0 5px 20px ' }}
            >
              Reports
            </Typography>
            <Item
              title="Report"
              to="/reports"
              icon={<MoneyOutlined />}
              selected={selected}
              setSelected={setSelected}
            />
            {user.role == 'admin' && (
              <>
                <Typography
                  variant="h6"
                  color={colors.grey[300]}
                  sx={{ m: '15px 0 5px 20px ' }}
                >
                  Settings
                </Typography>
                <Item
                  title="Settings"
                  to="/settings"
                  icon={<SettingsOutlined />}
                  selected={selected}
                  setSelected={setSelected}
                />
              </>
            )}

            <LogoutItem
              handleLogout={handleLogout}
              icon={<LogoutOutlined />}
              selected={selected}
              title="Logout"
            />
          </Box>
        </Menu>
      </ProSidebar>
    </Box>
  );
}

export default SideBar;
