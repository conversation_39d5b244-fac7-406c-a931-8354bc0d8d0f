import {
  Card,
  useTheme,
  CardContent,
  Typography,
  Box,
  Grid,
  Divider,
  CircularProgress,
} from "@mui/material";
import React from "react";
import { tokens } from "../../../utils/theme";
import { useNotificationsCount } from "../../../api/userApi/clinetHook";
import { useNavigate } from "react-router-dom";
const NotificationCard = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { data: count, isLoading, isFetched } = useNotificationsCount();
  const navigate = useNavigate();
  
  const handleClick = () => {
    navigate("/all_notifications");
  };

  return (
    <Card
      variant="elevation"
      style={{
        color: colors.grey,
        background: colors.primary[400],
        padding: 5,
        cursor: "pointer",
      }}
      onClick={handleClick}
    >
      <CardContent>
        <Box display="flex flexDirection:column">
          <Typography variant="body2" color={colors.grey[100]}>
            {isLoading && <CircularProgress />}
            {isFetched && (
              <span
                style={{
                  color: `${colors.redAccent[400]}`,
                }}
              >
                unread {count}
              </span>
            )}
          </Typography>
          <Typography variant="h3" color={colors.blueAccent[500]}>
            notifications
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default NotificationCard;
