<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\EmpEmergencyContact;
use App\Http\Requests\StoreEmpEmergencyContactRequest;
use App\Http\Requests\UpdateEmpEmergencyContactRequest;
use Illuminate\Http\Request;

class EmpEmergencyContactController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(EmpEmergencyContact::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreEmpEmergencyContactRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // $data = $request->validated();
        $data = $request->all();
        $res = EmpEmergencyContact::create([
            'emp_basic_id' => $data['emp_basic_id'],
            'name' => $data['name'],
            'relationship' => $data['relationship'],
            'contact' => $data['contact'],
            'mobile' => $data['mobile'],
            'user_id' => $request->user_id,
        ]);

        return response($res);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EmpEmergencyContact  $empEmergencyContact
     * @return \Illuminate\Http\Response
     */
    public function show(EmpEmergencyContact $empEmergencyContact)
    {
        return response($empEmergencyContact);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateEmpEmergencyContactRequest  $request
     * @param  \App\Models\EmpEmergencyContact  $empEmergencyContact
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, EmpEmergencyContact $empEmergencyContact)
    {
        // $data = $request->validated();
        $data = $request->all();
        $empEmergencyContact->update([
            'emp_basic_id' => $data['emp_basic_id'],
            'name' => $data['name'],
            'relationship' => $data['relationship'],
            'contact' => $data['contact'],
            'mobile' => $data['mobile'],
            'user_id' => $request->user_id,
        ]);

        return response($empEmergencyContact);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EmpEmergencyContact  $empEmergencyContact
     * @return \Illuminate\Http\Response
     */
    public function destroy(EmpEmergencyContact $empEmergencyContact)
    {
        $empEmergencyContact->delete();
        return response('', 204);
    }
}
