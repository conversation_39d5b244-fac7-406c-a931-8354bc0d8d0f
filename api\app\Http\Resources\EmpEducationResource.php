<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class EmpEducationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // return parent::toArray($request);

        return [
                'id'=>$this->id,
                'emp_basic_id'=>$this->emp_basic_id,
                'institution'=>$this->institution,
                'level'=>$this->level,
                'grade'=>$this->grade,
                'certefication'=>$this->certefication,
                'start_date'=>$this->start_date,
                'end_date'=>$this->end_date,
        ];
    }
}
