<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('payment_follow_ups', function (Blueprint $table) {
            //2025_02_13_064814_add_status_to_payment_follow_ups_table.php
            $table->string('status')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('payment_follow_ups', function (Blueprint $table) {
            //
            $table->string('status')->nullable();
        });
    }
};
