import React from "react";
import { Box, Button, TextField, Grid } from "@mui/material";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { useStateContext } from "../../../../context/ContextProvider";
import { useState } from "react";
import api from "../../../../api/config/axiosConfig";
const RecommandationForm = ({ data, empId }) => {
  const { errors, setErrors, setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    emp_basic_id: "",
    award_type: "",
    award_reason: "",
    from_date: "",
    ref_no: "",
    rate: "",
  });

  if (data) {
    useEffect(() => {
      setFormData(data);
    }, [data]);
  }
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  const addRecomaindation = useMutation(
    (payload) => api.post("award", payload),
    {
      onSuccess: () => {
        setNotification("successfuly created");
        queryClient.invalidateQueries({ queryKey: "recommandation" });
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );
  const updateRecomandation = useMutation(
    (formdata) => api.put(`award/${formdata.id}`, formdata),
    {
      onSuccess: () => {
        setNotification("updated successfully");
        queryClient.invalidateQueries({ queryKey: "recommandation" });
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );
  const handleSubmit = (e) => {
    e.preventDefault();
    const form = new FormData(e.currentTarget);
    if (data && data.id) {
      updateRecomandation.mutate(formData);
    } else {
      const payload = {
        emp_basic_id: empId,
        award_type: form.get("award_type"),
        award_reason: form.get("award_reason"),
        from_date: form.get("from_date"),
        ref_no: form.get("ref_no"),
        rate: form.get("rate"),
      };

      addRecomaindation.mutate(payload);
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.award_type == ""}
            margin="normal"
            label="Award Type"
            name="award_type"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.award_type}
            helperText={errors.award_type ? errors.award_type[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.award_reason == ""}
            margin="normal"
            label="Award_Reason"
            name="award_reason"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.award_reason}
            helperText={errors.award_reason ? errors.award_reason[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.from_date == ""}
            margin="normal"
            label="From Date"
            name="from_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.from_date}
            helperText={errors.from_date ? errors.from_date[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.ref_no == ""}
            margin="normal"
            label="Reference Number"
            name="ref_no"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.ref_no}
            helperText={errors.ref_no ? errors.ref_no[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.rate == ""}
            margin="normal"
            label="Rate"
            name="rate"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.rate}
            helperText={errors.rate ? errors.rate[0] : null}
          />
        </Grid>
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
          {data && data.id ? <div>update</div> : <div>create</div>}
        </Button>
      </Box>
    </Box>
  );
};

export default RecommandationForm;
