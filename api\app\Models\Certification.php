<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Certification extends Model
{
    use HasFactory;

    protected $fillable = [
        'coffee_batch_id',
        'certificate_number',
        'certification_type',
        'issue_date',
        'expiry_date',
        'certifying_body',
    ];

    public function coffeeBatch()
    {
        return $this->belongsTo(CoffeeBatch::class);
    }
}
