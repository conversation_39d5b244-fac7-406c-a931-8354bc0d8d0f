import React from 'react';
import { Box, Grid } from '@mui/material';
import Header from '../../components/Header';
import LinkCard from '../../components/LinkCard';

const CoffeeExportPage = () => {
  return (
    <Box m="10px">
      <Header
        title="Coffee Export"
        subtitle="Manage coffee export operations"
      />
      <Box maxWidth="85%" ml="50px">
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Coffee Batches"
              subtitle="Register/manage coffee batches"
              to="/coffeepage/batches"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Farmers / Purchases"
              subtitle="Record purchases from farmers"
              to="/coffeepage/farmers"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Export Orders"
              subtitle="Manage orders from international buyers"
              to="/coffeepage/export-orders"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Shipments"
              subtitle="Assign containers, vessels, tracking, etc."
              to="/coffeepage/shipments"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Certificates"
              subtitle="Phytosanitary, ICO, Quality, etc."
              to="/coffeepage/certificates"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Stock Movements"
              subtitle="Coffee stock transfers between warehouses"
              to="/coffeepage/stock-movements"
            />
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default CoffeeExportPage;
