import { useMutation, useQueryClient } from "@tanstack/react-query";
import React, { useEffect, useState } from "react";
import { useStateContext } from "../../../context/ContextProvider";
import api from "../../../api/config/axiosConfig";
import {
  Box,
  Grid,
  TextField,
  MenuItem,
  Button,
  Checkbox,
  FormControlLabel,
  Paper,
  useTheme,
} from "@mui/material";
import { useLocation, useParams } from "react-router-dom";
import { tokens } from "../../../utils/theme";
import Header from "../../../components/Header";
import { useEmployeeData } from "../../../api/userApi/clinetHook";
const reasons = [
  {
    label: "Annual leave",
    value: "Annual leave",
  },
  {
    label: "Sick leave",
    value: "Sick leave",
  },
  {
    label: "Morning leave",
    value: "Morning leave",
  },
  {
    label: "Exam leave",
    value: "Exam leave",
  },
  {
    label: "Social leave",
    value: "Social leave",
  },
  {
    label: "Weeding leave",
    value: "Weeding leave",
  },
  {
    label: "Maternity leave",
    value: "Maternity leave",
  },
  {
    label: "Leave with out pay ",
    value: "Leave with out pay ",
  },
  {
    label: "Leave From expired",
    value: "Leave From expired",
  },
];
function LeaveForm() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  let { id } = useParams();
  const { setNotification, user } = useStateContext();
  const location = useLocation();
  const [checked, setChecked] = useState(false);
  const [formData, setFormData] = useState(location.state);
  const [leave, setLeave] = useState({
    from_date: "",
    to_date: "",
    reg_date: "",
    reason: "",
    leave_taken: "",
    from_expired: "",
  });

  useEffect(() => {
    if (id) {
      api.get(`leaveId/${id}`).then(({ data }) => {
        setLeave(data);
        setFormData({
          emp_basic_id: data.emp_basic_id,
          client_id: data.client_id,
        });
      });
    }
  }, [id]);
  const handleChange = (e) => {
    setLeave({
      ...leave,
      [e.target.name]: e.target.value,
    });
  };
  const createLeave = useMutation((data) => api.post("leave", data), {
    onSuccess: () => {
      setNotification("Employee Leave is Inserted successfully");
    },
  });
  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    const payload = {
      emp_basic_id: formData.emp_basic_id,
      client_id: formData.client_id,
      user_id: user.id,
      from_date: data.get("from_date"),
      to_date: data.get("to_date"),
      reg_date: data.get("reg_date"),
      leave_taken: data.get("leave_taken"),
      from_expired: data.get("from_expired"),
      reason: data.get("reason"),
    };
    if (id) {
      api.put(`leave/${id}`, payload).then(() => {
        setNotification("leave record updated successfully");
      });
    } else {
      createLeave.mutate(payload);
    }
  };
  const {data:emp,isFetched}=useEmployeeData(formData.emp_basic_id)
  return (
    <Box margin="2%">
      <Header
        title="Apply Employee Leave"
        heading="h4"
        subtitle={
          isFetched && `Employee Name: ${emp.first_name} ${emp.middle_name} ${emp.last_name}`
        }
      />
      <Paper
        sx={{
          maxWidth: "70%",
          padding: "1rem",
          color: colors.grey[100],
          background: colors.primary[400],
          display: "flex",
          justifyContent: "center",
        }}
      >
        <Box component="form" onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={3}>
              <TextField
                margin="normal"
                label="From Date"
                name="from_date"
                fullWidth
                variant="standard"
                type="date"
                InputLabelProps={{
                  color: "success",
                }}
                onChange={handleChange}
                value={leave.from_date || new Date().toISOString().slice(0, 10)}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                margin="normal"
                label="To Date"
                name="to_date"
                fullWidth
                variant="standard"
                type="date"
                InputLabelProps={{
                  color: "success",
                }}
                onChange={handleChange}
                value={leave.to_date || new Date().toISOString().slice(0, 10)}
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <TextField
                margin="normal"
                label="Take Leave"
                name="leave_taken"
                fullWidth
                variant="standard"
                type="number"
                InputLabelProps={{
                  color: "success",
                }}
                onChange={handleChange}
                value={leave.leave_taken ? leave.leave_taken : ""}
                disabled={checked}
              />
            </Grid>
            <Grid item xs={12} sm={0.1}>
              <TextField
                InputProps={{
                  startAdornment: (
                    <FormControlLabel
                      control={
                        <Checkbox
                          sx={{
                            p: 0,
                            m: 0,
                          }}
                          checked={checked}
                          onChange={(e) => {
                            setChecked(e.target.checked);
                          }}
                        />
                      }
                      label="TakefromExpired"
                    />
                  ),
                }}
                margin="normal"
                label="From Expired"
                name="checkbox"
                fullWidth
                variant="standard"
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                margin="normal"
                label="From Expired"
                name="from_expired"
                fullWidth
                variant="standard"
                type="number"
                InputLabelProps={{
                  color: "success",
                }}
                onChange={handleChange}
                value={leave.from_expired ?? ""}
                disabled={!checked}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                margin="normal"
                label="Reasons"
                name="reason"
                fullWidth
                select
                variant="standard"
                type="text"
                InputLabelProps={{
                  color: "success",
                }}
                onChange={handleChange}
                value={leave.reason ? leave.reason : ""}
              >
                {reasons.map((reason) => (
                  <MenuItem key={reason.value} value={reason.value}>
                    {reason.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                margin="normal"
                label="Registration Date"
                name="reg_date"
                fullWidth
                variant="standard"
                type="date"
                InputLabelProps={{
                  color: "success",
                }}
                onChange={handleChange}
                value={leave.reg_date || new Date().toISOString().slice(0, 10)}
              />
            </Grid>
          </Grid>
          <span style={{ float: "inline-start" }}>
            <Button type="submit" variant="contained" color="success">
              {id ? <>update</> : <>add</>}
              
            </Button>
          </span>
        </Box>
      </Paper>
    </Box>
  );
}

export default LeaveForm;
