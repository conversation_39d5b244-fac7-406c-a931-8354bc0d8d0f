import { TextField, MenuItem, Box } from '@mui/material';
import React, { useState } from 'react';

const YearDropDown = ({ onSelectedYear }) => {
  const [selectedYear, setSelectedYear] = useState('');

  const years = [];

  for (let year = 2000; year <= 2040; year++) {
    years.push({
      label: String(year),
      value: `${year}-01-01`,
    });
  }

  const handleYearChange = (e) => {
    const yearValue = e.target.value;
    setSelectedYear(yearValue);
    onSelectedYear(yearValue);
  };

  return (
    <Box>
      <TextField
        select
        name="year-dropdown"
        value={selectedYear}
        onChange={handleYearChange}
        fullWidth
        label="Select Year"
        // variant='standard'
        margin='normal'
        type='text'
        InputLabelProps={{
          color: "success",
        }}
      >
        {years.map((year) => (
          <MenuItem key={year.value} value={year.value}>
            {year.label}
          </MenuItem>
        ))}
      </TextField>
    </Box>
  );
};

export default YearDropDown;
