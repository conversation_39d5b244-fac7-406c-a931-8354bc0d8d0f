<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Education;
use App\Http\Requests\StoreEducationRequest;
use App\Http\Requests\UpdateEducationRequest;
use App\Http\Resources\EmpEducationResource;
use Illuminate\Http\Request;

class EmpEducationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index()
    {
        return EmpEducationResource::collection(Education::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreEducationRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // $data = $request->validated();
        $data = $request->all();

        $education = Education::create([
            'emp_basic_id' => $data['emp_basic_id'],
            'institution' => $data['institution'],
            'level' => $data['level'],
            'grade' => $data['grade'],
            'certefication' => $data['certefication'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'user_id' => $request->user_id,
        ]);

        return response(new EmpEducationResource($education));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Education  $education
     * @return \Illuminate\Http\Response
     */
    public function show(Education $education)
    {
        return response(new EmpEducationResource($education));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateEducationRequest  $request
     * @param  \App\Models\Education  $education
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // $data = $request->validated();
         $education=Education::find($id);
        $data = $request->all();
        $education->update([
            'emp_basic_id' => $data['emp_basic_id'],
            'institution' => $data['institution'],
            'level' => $data['level'],
            'grade' => $data['grade'],
            'certefication' => $data['certefication'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'user_id' => $request->user_id,
        ]);

        return response(new EmpEducationResource($education));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Education  $education
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $education=Education::find($id);
        $education->delete();

        return response('', 204);
    }
}
