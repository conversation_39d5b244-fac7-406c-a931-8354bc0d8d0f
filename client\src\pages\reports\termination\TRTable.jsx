import { Box } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useQuery } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import api from '../../../api/config/axiosConfig';
import _CircularProgress from '../../../components/_CircularProgress';
import Header from '../../../components/Header';
import CustomToolBar from '../../../helper/CustomToolBar';
import { formatedMessageTermination } from '../formattedMessage';
const TRTable = () => {
  const location = useLocation();
  const TR = location.state;
  const [companyName, setCompanyName] = useState('');
  const columns = [
    {
      field: 'id',
      headerName: 'ID',
    },
    {
      field: 'first_name',
      headerName: 'First Name',
      flex: 1,
    },
    {
      field: 'middle_name',
      headerName: 'Middle Name',
      flex: 1,
    },
    {
      field: 'last_name',
      headerName: 'Last Name',
      flex: 1,
    },
    {
      field: 'start_date',
      headerName: 'Start Date',
      flex: 1,
    },
    {
      field: 'termination_date',
      headerName: 'Termination Date',
      flex: 1,
    },
    {
      field: 'reason',
      headerName: 'Reason',
      flex: 1,
    },
    {
      field: 'ref_no',
      headerName: 'Reference No',
      flex: 1,
    },
  ];
  const {
    data: response,
    isLoading,
    isFetched,
  } = useQuery(['terminationReport'], () =>
    api.get('/termination/report', { params: TR }).then(({ data }) => {
      if (data.length > 0) {
        setCompanyName(data[0].company_name);
      }
      return data;
    })
  );
  return (
    <Box margin="10px">
      {TR && TR.clientId ? (
        <Header
          title={`${companyName} Terminated Employee REPORT `}
          subtitle={formatedMessageTermination(
            TR.firstDate,
            TR.lastDate,
            TR.year
          )}
        />
      ) : (
        <Header
          title="Terminated Employee REPORT"
          subtitle={formatedMessageTermination(
            TR.firstDate,
            TR.lastDate,
            TR.year
          )}
        />
      )}
      {isLoading && <_CircularProgress />}
      {isFetched && (
        <DataGrid
          autoHeight
          hideFooter
          columns={columns}
          rows={response}
          components={{ Toolbar: CustomToolBar }}
        />
      )}
    </Box>
  );
};

export default TRTable;
