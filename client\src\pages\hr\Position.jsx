import { AddOutlined } from "@mui/icons-material";
import {
  <PERSON>ton,
  IconButton,
  Typography,
  useTheme,
  Grid,
  TextField,
  LinearProgress,
} from "@mui/material";
import { Box } from "@mui/system";
import { DataGrid } from "@mui/x-data-grid";
import {
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import React, { useEffect, useState } from "react";
import api from "../../api/config/axiosConfig";
import Header from "../../components/Header";
import { useStateContext } from "../../context/ContextProvider";
import { tokens } from "../../utils/theme";
import _LinearProgress from "../../components/_LinearProgress";
import { usePositionData } from "../../api/userApi/clinetHook";
function Position() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const queryClient = useQueryClient();
  const [clicked, setClicked] = useState(false);
  const [selectedRow, setSelectedRow] = useState({});
  const { setNotification, errors, setErrors } = useStateContext();

  // const {
  //   data: positions,
  //   isLoading,
  //   error,
  // } = useQuery(
  //   ["positions"],
  //   () => api.get("positions").then(async ({ data }) => await data.data),
  //   {
  //     refetchOnWindowFocus: false,
  //     staleTime: 60000,
  //   }
  // );

  const {
    data:positions,
    isLoading,
  }=usePositionData()
  const columns = [
    {
      field: "id",
      headerName: "ID",
    },
    {
      field: "position_name",
      headerName: "Position Name",
      flex: 1,
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 1,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          setClicked(true);
          setSelectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "delete",
      headerName: "Delete",
      flex: 1,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deletePosition = (e) => {
          e.stopPropagation();
          if (!window.confirm("Are you sure you want to delete the selected Position?")) {
            return;
          }
          // delete api in here
          api
            .delete(`positions/${params.id}`)
            .then(() => {
              setNotification("postions deleted successfully")})
            .catch((err) => {
              
            });
        };

        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deletePosition}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];
  const addPostion = useMutation(
    (formdata) => api.post(`positions`, formdata),
    {
      onSuccess: () => {
        setNotification("new Position is created");
        setClicked(false);
        queryClient.invalidateQueries(["positions"]);
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
        setClicked(true);
      },
    }
  );

  const handleChange = (e) => {
    setSelectedRow({
      ...selectedRow,
      [e.target.name]: e.target.value,
    });
  };
  const handleClick = (e) => {
    e.preventDefault();
    setClicked(true);
  };
  const onSubmit = (e) => {
    e.preventDefault();
    const payload = {
      position_name: selectedRow.position_name,
    };
    if (selectedRow.id) {
      api
        .put(`positions/${selectedRow.id}`, payload)
        .then(() => {
          setNotification("selected positions updated successfully");
          setClicked(false);
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
          setClicked(true);
        });
    } else {
      addPostion.mutate(payload);
    }
    setErrors({});
  };
  return (
    <Box m="10px">
      <Header title="Positions table" subtitle="List Of positions " />
      <Box display="flex" justifyContent="center" alignItems="center">
        <IconButton
          sx={{
            boxShadow: "2px 2px 4px rgba(0,0,0,0.25)",
            background: colors.primary[400],
          }}
          onClick={handleClick}
        >
          <AddOutlined color={colors.grey[100]} />
        </IconButton>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={clicked ? 9 : 12}>
          <Box
            m="10px 0 0 0"
            height="67vh"
            sx={{
              "& .MuiDataGrid-root": {
                border: "none",
              },
              "& .MuiDataGrid-cell": {
                borderBottom: "none",
              },
              "& .name-column--cell": {
                color: colors.greenAccent[300],
              },
              "& .MuiDataGrid-columnHeaders": {
                backgroundColor: colors.blueAccent[700],
                borderBottom: "none",
              },
              "& .MuiDataGrid-virtualScroller": {
                backgroundColor: colors.primary[400],
              },
              "& .MuiDataGrid-footerContainer": {
                borderTop: "none",
                backgroundColor: colors.blueAccent[700],
              },
              "& .MuiCheckBox-root": {
                color: `${colors.greenAccent[200]}!important`,
              },
              "& .MuiDataGrid-toolbarContainer .MuiButton-text": {
                color: `${colors.grey[100]}!important`,
              },
            }}
          >
            {isLoading ? (
              <_LinearProgress />
            ) : (
              <DataGrid rows={positions} columns={columns} />
            )}
            {/* <DataGrid rows={positions} columns={columns} /> */}
          </Box>
        </Grid>
        {clicked ? (
          <Grid item xs={12} sm={3} component="form" onSubmit={onSubmit}>
            <TextField
              error={!errors.position_name == ""}
              id="position_name"
              margin="normal"
              fullWidth
              label="Position"
              name="position_name"
              autoComplete="position"
              InputLabelProps={{
                color: "success",
              }}
              value={selectedRow.position_name || ""}
              onChange={handleChange}
              helperText={errors.position_name ? errors.position_name[0] : null}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 2, py: 2 }}
              color="success"
              size="small"
            >
              submit
            </Button>
          </Grid>
        ) : undefined}
      </Grid>
    </Box>
  );
}

export default Position;
