<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Models\Invoice;
use Illuminate\Http\Request;

class InvoiceController extends Controller
{
    public function index() { return Invoice::with('exportOrder.customer')->latest()->paginate(); }

    public function store(Request $request)
    {
        $data = $request->validate([
            'invoice_number'  => 'required|unique:invoices',
            'export_order_id' => 'required|exists:export_orders,id',
            'amount'          => 'required|numeric|min:0',
            'currency'        => 'nullable|string|max:8',
            'issue_date'      => 'nullable|date',
            'due_date'        => 'nullable|date',
            'paid_status'     => 'nullable|in:unpaid,partial,paid',
        ]);
        return Invoice::create($data);
    }

    public function update(Request $request, Invoice $invoice)
    {
        $invoice->update($request->only(['amount','currency','issue_date','due_date','paid_status']));
        return $invoice->load('exportOrder.customer');
    }
}
