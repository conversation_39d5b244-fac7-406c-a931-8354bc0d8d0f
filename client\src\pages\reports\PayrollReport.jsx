import React from "react";
import { Box, Grid } from "@mui/material";
import LinkCard from "../../components/LinkCard";
const PayrollReport = () => {
  return (
    <Box margin="10px">
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="Bank Payroll Report"
            subtitle="payroll report for a bank"
            to="/reports/payroll/bankpayrolist"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="IncomTax Report"
            subtitle="filter income tax report with dates and tax centers "
            to="/reports/payroll/income_tax_list"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="Pension Tax Report"
            subtitle="filter Pension tax report with dates and tax centers"
            to="/reports/payroll/pension_tax_list"
          />
        </Grid>
        {/* <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="Prvident tax report"
            subtitle="filter provident tax report with dates and tax centers"
            to="/reports/payroll/provident_tax_list"
          />
        </Grid> */}

        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="General Payroll Report"
            subtitle="filter General Payroll Report"
            to="/reports/payroll/General_payroll"
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default PayrollReport;
