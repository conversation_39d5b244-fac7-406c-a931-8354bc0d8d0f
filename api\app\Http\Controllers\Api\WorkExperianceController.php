<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\WorkExperiance;
use App\Http\Requests\StoreWorkExperianceRequest;
use App\Http\Requests\UpdateWorkExperianceRequest;
use Illuminate\Http\Request;

class WorkExperianceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(WorkExperiance::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreWorkExperianceRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // $data = $request->validated();
        $data = $request->all();

        $workExperiance = WorkExperiance::create([
            'emp_basic_id' => $data['emp_basic_id'],
            'organization' => $data['organization'],
            'position' => $data['position'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'user_id' => $request->user_id,
        ]);
        return response($workExperiance);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\WorkExperiance  $workExperiance
     * @return \Illuminate\Http\Response
     */
    public function show(WorkExperiance $workExperiance)
    {
        return response($workExperiance);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateWorkExperianceRequest  $request
     * @param  \App\Models\WorkExperiance  $workExperiance
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // $data = $request->validated();
        $data = $request->all();
        $workExperiance = WorkExperiance::find($id);
        $workExperiance->update([
            'emp_basic_id' => $data['emp_basic_id'],
            'organization' => $data['organization'],
            'position' => $data['position'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'user_id' => $request->user_id,
        ]);
        return response($workExperiance);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\WorkExperiance  $workExperiance
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $workExperiance = WorkExperiance::find($id);
        $workExperiance->delete();

        return response('', 204);
    }
}
