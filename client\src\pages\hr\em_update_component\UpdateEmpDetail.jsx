import {
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Typography,
  Box,
  Button,
  useTheme,
} from '@mui/material';
import React from 'react';
import { useState } from 'react';
import api from '../../../api/config/axiosConfig';
import { useStateContext } from '../../../context/ContextProvider';
import { tokens } from '../../../utils/theme';
import { useEffect } from 'react';

function UpdateEmpDetail({ id }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { errors, setErrors, setNotification, user } = useStateContext();
  const [formData, setFormData] = useState({
    emp_basic_id: '',
    height: '',
    weight: '',
    address: '',
    city: '',
    sub_city: '',
    kebele: '',
    house_no: '',
    country: '',
    phone: '',
    photo_url: '',
    user_id: user.id,
  });

  useEffect(() => {
    setFormData({
      ...formData,
      user_id: user.id,
    });
  }, [user]);

  if (id) {
    useEffect(() => {
      api
        .get(`emp_basics/${id}?all=true`, { params: { all: id } })
        .then(({ data }) => {
          setFormData(data.data.emp_detail);
          console.log(data.data);
        })
        .catch((err) => {
          console.error('from use effect', err.message);
        });
    }, [id]);
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  const handleSubmit = (e) => {
    e.preventDefault();

    //
    api
      .put(`emp_details/${formData.id}`, formData)
      .then(() => {
        setNotification('updated Successfully');
      })
      .catch((err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      });
  };
  return (
    <Box>
      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              margin="normal"
              label="Height"
              name="height"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.height}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              margin="normal"
              label="Weight"
              name="weight"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.weight}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.address == ''}
              margin="normal"
              label="Address"
              name="address"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.address}
              helperText={errors.address ? errors.address[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.city == ''}
              margin="normal"
              label="City"
              name="city"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.city}
              helperText={errors.city ? errors.city[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.sub_city == ''}
              margin="normal"
              label="Sub City"
              name="sub_city"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.sub_city}
              helperText={errors.sub_city ? errors.sub_city[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.kebele == ''}
              margin="normal"
              label="Kebele"
              name="kebele"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.kebele}
              helperText={errors.kebele ? errors.kebele[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.house_no == ''}
              margin="normal"
              label="House Number"
              name="house_no"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.house_no}
              helperText={errors.house_no ? errors.house_no[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.country == ''}
              margin="normal"
              label="Country"
              name="country"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.country}
              helperText={errors.country ? errors.country[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.phone == ''}
              margin="normal"
              label="Phone"
              name="phone"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.phone}
              helperText={errors.phone ? errors.phone[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              margin="normal"
              label="Image"
              name="photo_url"
              fullWidth
              variant="standard"
              type="file"
              inputProps={{ accept: 'image/*' }}
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={''}
            />
          </Grid>
        </Grid>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
            Update
          </Button>
        </Box>
      </Box>
    </Box>
  );
}

export default UpdateEmpDetail;
