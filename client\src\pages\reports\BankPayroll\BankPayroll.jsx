import React from 'react';
import { useState } from 'react';
import FilterComponent from '../../../components/FilterComponent';
import { Box, Button, Grid } from '@mui/material';
import { useNavigate } from 'react-router-dom';
const BankPayroll = () => {
  const [firstDate, setFirstDate] = useState('');
  const [lastDate, setLastDate] = useState('');
  const [clientId, setclientId] = useState('');
  const [tax_center, setTaxCenter] = useState('');
  const [year, setYear] = useState('');
  const [company, setCompany] = useState('');
  const [current, setCurrent] = useState('');
  const [region, setRegion] = useState('');
  const [bankCode, setBankCode] = useState('');

  const navigate = useNavigate();

  const passFromDate = (fromDate) => {
    setFirstDate(fromDate);
  };
  const passProjectId = (projectId) => {
    setclientId(projectId);
  };
  const passToDate = (toDate) => {
    setLastDate(toDate);
  };
  const fullYear = (year) => {
    setYear(year);
  };
  const passTaxCenter = (taxCenter) => {
    setTaxCenter(taxCenter);
  };

  const passCompany = (company) => {
    setCompany(company);
    console.log(company);
  };

  const passCurrent = (cu) => {
    setCurrent(cu);
    //console.log(current);
  };

  const passRegion = (region) => {
    console.log(region);
    setRegion(region);
  };

  const passBank = (bankCode) => {
    setBankCode(bankCode);
  }

  const handleButtonClick = () => {
    if (
      firstDate ||
      lastDate ||
      clientId ||
      year ||
      tax_center ||
      company ||
      current ||
      region ||
      bankCode
    ) {
      navigate('/reports/payroll/bankpayrollist/report', {
        state: {
          firstDate: firstDate,
          lastDate: lastDate,
          clientId: clientId,
          year: year,
          taxCenter: tax_center,
          company: company,
          current: current,
          region: region,
          bankCode: bankCode,
          status: 0,
        },
      });
    } else {
      navigate('/reports/payroll/bankpayrollist/report', {
        state: {
          status: 1,
        },
      });
    }
  };

  return (
    <Grid margin="10px" container spacing={2}>
      <Grid xs={12} sm={10}>
        <FilterComponent
          passFromDate={passFromDate}
          passProjectId={passProjectId}
          passToDate={passToDate}
          passYear={fullYear}
          passTaxCenter={passTaxCenter}
          passCompany={passCompany}
          passCurrent={passCurrent}
          passRegion={passRegion}
          passBank={passBank}
        />
      </Grid>
      <Grid item xs={12} sm={2}>
        <Button
          sx={{
            marginLeft: 'auto',
            width: '80%',
            height: '80%',
          }}
          variant="contained"
          color="success"
          onClick={handleButtonClick}
        >
          filter
        </Button>
      </Grid>
    </Grid>
  );
};

export default BankPayroll;
