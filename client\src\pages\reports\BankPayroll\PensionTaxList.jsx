import { useQuery } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import api from '../../../api/config/axiosConfig';
import { Box } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import _CircularProgress from '../../../components/_CircularProgress';
import CustomToolBar from '../../../helper/CustomToolBar';
import Header from '../../../components/Header';
import { pensionTaxMessage } from '../formattedMessage';
import CustomFooterTable from './CustomFooterTable';
import {formatedMessage}from "../formattedMessage"

const PensionTaxList = () => {
  const location = useLocation();
  const [region, setRegion] = React.useState('');


  const { data: response, isLoading } = useQuery(
    ['pensiontax', location.state],
    async () =>
      await api
        .get('pensiontax', { params: location.state })
        .then(({ data }) => {
          console.log(data);
          if (data.length > 0) {
            //setCompanyName(data[0].company_name);
            setRegion(location.state.region);
          }
          return data;
        })
  );

  const columns = React.useMemo(
    () => [
      { field: 'id', headerName: 'ID' },
      { field: 'fullname', headerName: 'fullName', flex: 1.5 },
      { field: 'tin_no', headerName: 'Tin Number', flex: 1 },
      { field: 'start_date', headerName: 'Employed Date', flex: 1 },
      { field: 'basic_salary', headerName: 'Basic_Salary', flex: 1 },
      { field: 'pension_emp', headerName: 'employee pension Tax', flex: 1 },
      { field: 'pension_comp', headerName: 'company pension Tax', flex: 1 },
      { field: 'pension_total', headerName: 'Total pension Tax', flex: 1 },
      { field: 'tax_center_name', headerName: 'Tax Center', flex: 1 },
      { field: 'company_name', headerName: 'Company', flex: 1 },
      { field: 'company_code', headerName: 'Company Code', flex: 1 },
    ],
    []
  );

  return (
    <Box margin="10px">
      {location.state.region ? (
        <Header
          title={`${region} PENSION TAX REPORT `}
          subtitle={formatedMessage(
            location.state.firstDate,
            location.state.lastDate,
            location.state.year
          )}
        />
      ) : (
        <Header
          title="PENSION TAX REPORT"
          subtitle={incomeTaxMessage(
            location.state.firstDate,
            location.state.lastDate
          )}
        />
      )}

      {isLoading ? (
        <_CircularProgress />
      ) : (
        <DataGrid
          columns={columns}
          rows={response}
          autoHeight
          rowsPerPageOptions={[5, 25, 100]}
          pagination
          components={{
            Toolbar: CustomToolBar,
            Footer: () => (
              <CustomFooterTable columns={columns} rows={response} />
            ),
          }}
        />
      )}
    </Box>
  );
};

export default PensionTaxList;
