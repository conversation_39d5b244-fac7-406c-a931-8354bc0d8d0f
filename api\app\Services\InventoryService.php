<?php

namespace App\Services;

use App\Models\{Item, CoffeeBatch, StockMovement};
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

class InventoryService
{
    public function recordMovement(array $data): StockMovement
    {
        // Invariants
        if (!empty($data['coffee_batch_id']) && !empty($data['item_id'])) {
            throw new InvalidArgumentException('Provide either coffee_batch_id or item_id, not both.');
        }
        if (empty($data['coffee_batch_id']) && empty($data['item_id'])) {
            throw new InvalidArgumentException('Provide coffee_batch_id or item_id.');
        }

        return DB::transaction(function () use ($data) {
            /** @var StockMovement $movement */
            $movement = StockMovement::create($data);

            if ($movement->coffee_batch_id) {
                $batch = CoffeeBatch::lockForUpdate()->findOrFail($movement->coffee_batch_id);
                $this->applyToCoffeeBatch($batch, $movement);
                $batch->save();
            } else {
                $item = Item::lockForUpdate()->findOrFail($movement->item_id);
                $this->applyToItem($item, $movement);
                $item->save();
            }

            return $movement->refresh();
        });
    }

    public function deleteMovement(StockMovement $movement): void
    {
        DB::transaction(function () use ($movement) {
            if ($movement->coffee_batch_id) {
                $batch = CoffeeBatch::lockForUpdate()->findOrFail($movement->coffee_batch_id);
                $this->reverseFromCoffeeBatch($batch, $movement);
                $batch->save();
            } else if ($movement->item_id) {
                $item = Item::lockForUpdate()->findOrFail($movement->item_id);
                $this->reverseFromItem($item, $movement);
                $item->save();
            }
            $movement->delete();
        });
    }

    protected function applyToCoffeeBatch(CoffeeBatch $batch, StockMovement $m): void
    {
        if ($m->movement_type === 'inbound') {
            $batch->quantity_kg += $m->quantity;
        } elseif ($m->movement_type === 'outbound') {
            if ($batch->quantity_kg < $m->quantity) {
                throw new InvalidArgumentException('Insufficient batch quantity.');
            }
            $batch->quantity_kg -= $m->quantity;
        } elseif ($m->movement_type === 'transfer') {
            if ($batch->quantity_kg < $m->quantity) {
                throw new InvalidArgumentException('Insufficient batch quantity for transfer.');
            }
            $batch->quantity_kg -= $m->quantity;
            // Create inbound at destination? Prefer explicit second movement via controller.
        }
    }

    protected function applyToItem(Item $item, StockMovement $m): void
    {
        if ($m->movement_type === 'inbound') {
            $item->current_qty += $m->quantity;
        } elseif ($m->movement_type === 'outbound') {
            if ($item->current_qty < $m->quantity) {
                throw new InvalidArgumentException('Insufficient item quantity.');
            }
            $item->current_qty -= $m->quantity;
        } elseif ($m->movement_type === 'transfer') {
            if ($item->current_qty < $m->quantity) {
                throw new InvalidArgumentException('Insufficient item quantity for transfer.');
            }
            $item->current_qty -= $m->quantity;
        }
    }

    protected function reverseFromCoffeeBatch(CoffeeBatch $batch, StockMovement $m): void
    {
        if ($m->movement_type === 'inbound') {
            $batch->quantity_kg -= $m->quantity;
        } elseif ($m->movement_type === 'outbound') {
            $batch->quantity_kg += $m->quantity;
        } elseif ($m->movement_type === 'transfer') {
            $batch->quantity_kg += $m->quantity;
        }
    }

    protected function reverseFromItem(Item $item, StockMovement $m): void
    {
        if ($m->movement_type === 'inbound') {
            $item->current_qty -= $m->quantity;
        } elseif ($m->movement_type === 'outbound') {
            $item->current_qty += $m->quantity;
        } elseif ($m->movement_type === 'transfer') {
            $item->current_qty += $m->quantity;
        }
    }
}
