import { Grid, Paper, useTheme } from "@mui/material";
import { tokens } from "../utils/theme";

const RightSide = ({ components, selectedLinkIndex, id = "" }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const Component = components[selectedLinkIndex];
  return (
    <Grid item xs={8}>
      <Paper
        sx={{
          padding: "1rem",
          color: colors.grey[100],
          background: colors.primary[400],
        }}
      >
        <Component id={id} />
      </Paper>
    </Grid>
  );
};
export default RightSide;
