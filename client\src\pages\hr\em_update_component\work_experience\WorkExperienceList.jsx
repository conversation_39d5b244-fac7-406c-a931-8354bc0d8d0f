import React from "react";
import EditRenderCell from "../../../../components/EditRenderCell";
import DeleteRender<PERSON>ell from "../../../../components/DeleteRenderCell";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { CircularProgress } from "@mui/material";
import Header from "../../../../components/Header";
import { DataGrid } from "@mui/x-data-grid";
import api from "../../../../api/config/axiosConfig";
import { useStateContext } from "../../../../context/ContextProvider";

const WorkExperienceList = ({ id, selectedRow }) => {
    const queryClient=useQueryClient()
    const {setNotification}=useStateContext()

  const {
    data: work,
    isLoading,
    isFetched,
  } = useQuery(["work_experiance", id], () =>
    api
      .get(`emp_basics/${id}`, { params: { all: "fetch_work_experiance" } })
      .then(({ data }) => {
        if (data.data.emp_work_experiance) {
          return data.data.emp_work_experiance;
        }
      }),
      {
        enabled:!!id,
        staleTime: 600000,
        refetchOnWindowFocus: false,
      }
  );
  const deleteRow = useMutation(
    (id) => api.delete(`emp_work_experiance/${id}`),
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: "work_experiance" });
        setNotification("Work Experience  record is deleted Successfully");
      },
    }
  );
  const handleDelete = (id) => {
    if (
      !window.confirm("Are you sure you want to delete the selected  record?")
    ) {
      return;
    }
    deleteRow.mutate(id);
  };
  const columns = [
    {
      field: "id",
      headerName: "Id",
    },
    {
      field: "organization",
      headerName: "Organization",
    },
    {
      field: "position",
      headerName: "Position",
    },
    {
      field: "start_date",
      headerName: "startDate",
    },
    {
      field: "end_date",
      headerName: "endDate",
    },
    {
      field: "edit",
      headerName: "Edit",
      filterable: false,
      sortable: false,
      hide: false,
      renderCell: (params) => (
        <EditRenderCell
          onClick={(e) => {
            e.stopPropagation();
            selectedRow(params.row);
          }}
          title="Edit"
        />
      ),
    },

    {
      field: "delete",
      headerName: "Delete",
      filterable: false,
      sortable: false,
      hide: false,
      renderCell: (params) => (
        <DeleteRenderCell handleDelete={() => handleDelete(params.row.id)} />
      ),
    },
  ];
  return (
    <div style={{ marginTop: "15px" }}>
      <Header title="List of Work Experience" heading="h5" />
      {isLoading && <CircularProgress />}
      {isFetched && (
        <DataGrid columns={columns} rows={work} hideFooter autoHeight />
      )}
    </div>
  );
};

export default WorkExperienceList;
