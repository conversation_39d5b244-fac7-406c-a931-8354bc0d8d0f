<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coffee_batches', function (Blueprint $table) {
            $table->id();
            $table->string('batch_number')->unique();
            $table->date('harvest_date')->nullable();
            $table->string('coffee_type');
            $table->string('grade');
            $table->string('processing_method');
            $table->decimal('quantity_kg');
            $table->enum('status', ['in_stock', 'allocated', 'shipped', 'sold']);
            $table->foreignId('warehouse_id')->nullable()->constrained('warehouses');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coffee_batches');
    }
};
