import { Grid, <PERSON>Item, ListItemText, useTheme, Card ,ListItemButton} from "@mui/material";
import { tokens } from "../utils/theme";

const LeftSide = ({ links, handleLinkClick ,activeLink,m}) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  return (
    <Grid item xs={4}>
      <Grid container spacing={0.7}>
        {links.map((link, index) => (
          <Grid key={index} item xs={12} md={m?m:6}>
            <Card 
              style={{
                color: colors.grey[100],
                background: colors.primary[400],
              }}
            >
              <ListItemButton onClick={() => handleLinkClick(index)}sx={{background:(activeLink===index?colors.blueAccent[500]:'')}}>
                <ListItemText primary={link} />
              </ListItemButton>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Grid>
  );
};

export default LeftSide;
