<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Models\CoffeeBatch;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class CoffeeBatchController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return CoffeeBatch::with('warehouse')->latest()->paginate();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'batch_number' => 'required|string|unique:coffee_batches|max:255',
            'harvest_date' => 'nullable|date',
            'coffee_type' => 'required|string|max:255',
            'grade' => 'required|string|max:255',
            'processing_method' => 'required|string|max:255',
            'quantity_kg' => 'required|numeric',
            'status' => ['required', Rule::in(['in_stock', 'allocated', 'shipped', 'sold'])],
            'warehouse_id' => 'nullable|exists:warehouses,id',
        ]);

        return CoffeeBatch::create($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\CoffeeBatch  $coffeeBatch
     * @return \Illuminate\Http\Response
     */
    public function show(CoffeeBatch $coffeeBatch)
    {
        return $coffeeBatch->load('warehouse', 'qualityInspections', 'certifications', 'purchasesFromFarmers', 'phytosanitaryCertificates', 'stockMovements', 'exportOrders');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\CoffeeBatch  $coffeeBatch
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, CoffeeBatch $coffeeBatch)
    {
        $data = $request->validate([
            'batch_number' => 'sometimes|required|string|unique:coffee_batches,batch_number,' . $coffeeBatch->id . '|max:255',
            'harvest_date' => 'nullable|date',
            'coffee_type' => 'sometimes|required|string|max:255',
            'grade' => 'sometimes|required|string|max:255',
            'processing_method' => 'sometimes|required|string|max:255',
            'quantity_kg' => 'sometimes|required|numeric',
            'status' => ['sometimes', 'required', Rule::in(['in_stock', 'allocated', 'shipped', 'sold'])],
            'warehouse_id' => 'nullable|exists:warehouses,id',
        ]);

        $coffeeBatch->update($data);

        return $coffeeBatch;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\CoffeeBatch  $coffeeBatch
     * @return \Illuminate\Http\Response
     */
    public function destroy(CoffeeBatch $coffeeBatch)
    {
        $coffeeBatch->delete();

        return response()->noContent();
    }
}
