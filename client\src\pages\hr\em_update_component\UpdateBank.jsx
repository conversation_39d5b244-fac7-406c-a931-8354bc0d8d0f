import { <PERSON><PERSON><PERSON>, <PERSON>, Grid, <PERSON><PERSON><PERSON>, Button } from "@mui/material";
import React from "react";
import { useEffect } from "react";
import { useState } from "react";
import api from "../../../api/config/axiosConfig";
import { useStateContext } from "../../../context/ContextProvider";

function UpdateBank({ id }) {
  const { errors, setErrors, setNotification, user } = useStateContext();
  const [formData, setFormData] = useState({
    id:'',
    emp_basic_id:id,
    bank_account: "",
    bank_code: "",
    bank_branch: "",
    user_id: user.id,
  });

    useEffect(() => {
      setFormData({
        ...formData,
        user_id: user.id,
      });
    }, [user]);
  
  if (id) {
    useEffect(() => {
      api
        .get(`emp_basics/${id}?all=true`, { params: { all: id } })
        .then(({ data }) => {
          if (data.data.emp_bank) {
            setFormData(data.data.emp_bank);
          }
        });
    }, [id]);
  }
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    if(formData.id){
      api
      .put(`emp_banks/${formData.id}`, formData)
      .then(() => {
        setNotification("Bank Information Updated successfully");
      })
      .catch((err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      });
    }else{
      api
      .post('emp_banks', formData)
      .then(() => {
        setNotification("Bank Information Updated successfully");
      })
      .catch((err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      });
    }
   
  };
  
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.bank_account == ""}
            margin="normal"
            label="Bank Account"
            name="bank_account"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.bank_account}
            helperText={errors.bank_account ? errors.bank_account[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.bank_branch == ""}
            margin="normal"
            label="Bank Branch"
            name="bank_branch"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.bank_branch}
            helperText={errors.bank_branch ? errors.bank_branch[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.bank_code == ""}
            margin="normal"
            label="Bank Code"
            name="bank_code"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.bank_code}
            helperText={errors.bank_code ? errors.bank_code[0] : null}
          />
        </Grid>
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
          Submit
        </Button>
      </Box>
    </Box>
  );
}

export default UpdateBank;
