<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentFollowupNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(public $payment_id, public $client_id, public $daysLeftUntilDue, public $start_date, public $end_date, public $collection_schedule, public $message, public $last_schedule, public $overdue, public $triggering_date)
    {

        $this->payment_id = $payment_id;
        $this->client_id = $client_id;
        $this->start_date = $start_date;
        $this->end_date = $end_date;
        $this->collection_schedule = $collection_schedule;
        $this->message = $message;
        $this->overdue = $overdue;
        $this->triggering_date = $triggering_date;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->line('The introduction to the notification.')
            ->action('Notification Action', url('/'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
    public function toDatabase($notifiable)
    {
        return [
            'message' => [
                'contract_id' => $this->payment_id,
                'client_id' => $this->client_id,
                'daysLeftUntilDue' => $this->daysLeftUntilDue,
                'start_date' => $this->start_date,
                'end_date' => $this->end_date,
                'collection_schedule' => $this->collection_schedule,
                'message' => $this->message,
                'overdue' => $this->overdue,
                'trigering_date' => $this->triggering_date,
                'type' => 'payment'
            ]
        ];
    }
}
