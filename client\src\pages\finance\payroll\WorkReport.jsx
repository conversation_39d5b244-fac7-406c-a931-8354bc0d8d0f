import React from 'react'
import {
    Box,
    TextField,
  } from "@mui/material";
  import Header from "../../../components/Header";
function WorkReport() {
  return (
    <Box>
    <Header title="work report for this month" heading="h6" />

    <TextField
      label="Total Work Duration"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Absence Duration"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Overtime Duration"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Holidy Duration"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Additional"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="PC-RATE Work Done"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Additional PC-RATE Work Done"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Additional Non-Taxable"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
  </Box>
  )
}

export default WorkReport