export const routes = [
  {
    role: 'hr',
    children: [
      {
        path: 'reports',
        element: 'Report',
      },
      {
        path: 'dashboard',
        element: 'Dashboard',
      },
      {
        path: '/user/change-password',
        element: 'change password',
      },
      {
        path: 'employees',
        element: 'HR',
      },
      {
        path: 'employees/edit',
        element: 'Search Employee',
      },
      {
        path: 'employees/:id',
        element: 'update Employee',
      },
      {
        path: 'employees/new',
        element: 'RegisterEmployee',
      },
      {
        path: 'employees/position',
        element: 'Position',
      },
      {
        path: '/employees/assign',
        element: 'Assign',
      },
      {
        path: '/employees/transfer',
        element: 'Transfer',
      },
      {
        path: '/employees/transfer/:id',
        element: 'updateTransfer',
      },
      {
        path: '/employees/leave',
        element: 'LeaveSearch',
      },
      {
        path: '/employees/leaves',
        element: 'Leave',
      },
      {
        path: '/employees/add-leaves',
        element: 'LeaveForm',
      },
      {
        path: '/employees/add-leaves/:id',
        element: 'updateLeave',
      },
      {
        path: '/employees/leave-detail',
        element: 'LeaveTakenDetail',
      },
      {
        path: '/employees/leave-detail-two',
        element: 'LeaveTakenTwoYears',
      },
      {
        path: '/employees/leave-summary',
        element: 'LeaveSummary',
      },
      {
        path: '/employees/leave-summary-lookup',
        element: 'LeaveSummaryLookup',
      },
      {
        path: 'clients',
        element: 'Project',
      },
      {
        path: 'clients/list',
        element: 'Client',
      },
      {
        path: 'clients/deduct',
        element: 'ClientDeduct',
      },
      {
        path: 'clients/new',
        element: 'ClientForm',
      },
      {
        path: 'clients/:id',
        element: 'updateClient',
      },
      {
        path: '/clients/show/:id',
        element: 'ShowClient',
      },
      {
        path: '/clients/payment-follow-up',
        element: 'paymentFollowup',
      },
      {
        path: '/clients/contract-follow-up',
        element: 'contractFollowup',
      },
    ],
  },
  {
    role: 'client',
    children: [
      {
        path: 'clients',
        element: 'Project',
      },
      {
        path: '/user/change-password',
        element: 'change password',
      },
      {
        path: 'clients/list',
        element: 'Client',
      },
      {
        path: 'clients/deduct',
        element: 'ClientDeduct',
      },
      {
        path: 'clients/new',
        element: 'ClientForm',
      },
      {
        path: 'clients/:id',
        element: 'updateClient',
      },
      {
        path: '/clients/show/:id',
        element: 'ShowClient',
      },
      {
        path: '/clients/payment-follow-up',
        element: 'paymentFollowup',
      },
      {
        path: '/clients/contract-follow-up',
        element: 'contractFollowup',
      },
    ],
  },
  {
    role: 'finance',
    children: [
      {
        path: 'dashboard',
        element: 'Dashboard',
      },
      {
        path: '/user/change-password',
        element: 'change password',
      },
      {
        path: 'reports',
        element: 'Report',
      },
      {
        path: 'finance',
        element: 'Finance',
      },
      {
        path: 'finance/draft',
        element: 'DraftPayroll',
      },
      {
        path: 'finance/overtime',
        element: 'overtime',
      },
      {
        path: 'finance/absence',
        element: 'Absence',
      },
      {
        path: 'finance/additional',
        element: 'Additional ',
      },
      {
        path: 'finance/pcrate',
        element: ' Pcrate',
      },
      {
        path: 'finance/additionalpcrate',
        element: ' AdditionalPcrate ',
      },
      {
        path: 'finance/dsa',
        element: 'Dsa',
      },
      {
        path: 'finance/nontaxable',
        element: 'AdditionalNonTaxable',
      },
      {
        path: 'finance/setdeductable',
        element: 'EmployeeDeductable',
      },
      {
        path: 'finance/add-deductable',
        element: 'AddDeductable',
      },
      {
        path: 'finance/dailyrate',
        element: 'DailyRate',
      },
      {
        path: '/finance/payroll',
        element: 'Payroll',
      },
      {
        path: '/finance/slip',
        element: 'PaymentSlip',
      },
      {
        path: '/finance/slip/list',
        element: 'PaymentSlipList',
      },
      {
        path: '/finance/payroll/calc',
        element: 'CalculatePayroll',
      },
      {
        path: '/finance/sevrenace',
        element: 'PaySeverance',
      },
    ],
  },
  {
    role: 'operation',
    children: [
      {
        path: 'dashboard',
        element: 'Dashboard',
      },
      {
        path: '/user/change-password',
        element: 'change password',
      },
      {
        path: 'reports',
        element: 'Report',
      },
      {
        path: 'operations',
        element: 'Operation',
      },
    ],
  },

  {
    role: 'admin',
    children: [
      {
        path: 'dashboard',
        element: 'Dashboard',
      },
      // {
      //   path: '/activity',
      //   element: 'ActivityLogPage',
      // },
      {
        path: '/user/change-password',
        element: 'change password',
      },
      {
        path: 'reports',
        element: 'Report',
      },
      {
        path: 'settings',
        element: 'Settings',
      },
      {
        path: 'settings/tax-center',
        element: 'TaxCenter ',
      },
      {
        path: 'settings/terminate',
        element: 'Terminate',
      },
      {
        path: 'settings/pcrate_type',
        element: 'PcrateType',
      },
      {
        path: 'settings/users',
        element: 'Users',
      },
      {
        path: 'settings/users/new',
        element: 'Register New User',
      },
      {
        path: 'settings/users/:id',
        element: 'Update User',
      },
      {
        path: 'employees',
        element: 'HR',
      },
      {
        path: 'employees/edit',
        element: 'Search Employee',
      },
      {
        path: 'employees/:id',
        element: 'update Employee',
      },
      {
        path: 'employees/new',
        element: 'RegisterEmployee',
      },
      {
        path: 'employees/position',
        element: 'Position',
      },
      {
        path: '/employees/assign',
        element: 'Assign',
      },
      {
        path: '/employees/transfer',
        element: 'Transfer',
      },
      {
        path: '/employees/transfer/:id',
        element: 'updateTransfer',
      },
      {
        path: '/employees/leave',
        element: 'LeaveSearch',
      },
      {
        path: '/employees/leaves',
        element: 'Leave',
      },
      {
        path: '/employees/add-leaves',
        element: 'LeaveForm',
      },
      {
        path: '/employees/add-leaves/:id',
        element: 'updateLeave',
      },
      {
        path: '/employees/leave-detail',
        element: 'LeaveTakenDetail',
      },
      {
        path: '/employees/leave-detail-two',
        element: 'LeaveTakenTwoYears',
      },
      {
        path: '/employees/leave-summary',
        element: 'LeaveSummary',
      },
      {
        path: '/employees/leave-summary-lookup',
        element: 'LeaveSummaryLookup',
      },
      {
        path: 'clients',
        element: 'Project',
      },
      {
        path: 'clients/list',
        element: 'Client',
      },
      {
        path: 'clients/deduct',
        element: 'ClientDeduct',
      },
      {
        path: 'clients/new',
        element: 'ClientForm',
      },
      {
        path: 'clients/:id',
        element: 'updateClient',
      },
      {
        path: '/clients/show/:id',
        element: 'ShowClient',
      },
      {
        path: 'operations',
        element: 'Operation',
      },
      {
        path: 'finance',
        element: 'Finance',
      },
      {
        path: 'finance/draft',
        element: 'DraftPayroll',
      },
      {
        path: 'finance/overtime',
        element: 'overtime',
      },
      {
        path: 'finance/absence',
        element: 'Absence',
      },
      {
        path: 'finance/additional',
        element: 'Additional ',
      },
      {
        path: 'finance/pcrate',
        element: ' Pcrate',
      },
      {
        path: 'finance/additionalpcrate',
        element: ' AdditionalPcrate ',
      },
      {
        path: 'finance/dsa',
        element: 'Dsa',
      },
      {
        path: 'finance/nontaxable',
        element: 'AdditionalNonTaxable',
      },
      {
        path: 'finance/setdeductable',
        element: 'EmployeeDeductable',
      },
      {
        path: 'finance/add-deductable',
        element: 'AddDeductable',
      },
      {
        path: '/finance/sevrenace',
        element: 'PaySeverance',
      },
      {
        path: 'finance/dailyrate',
        element: 'DailyRate',
      },
      {
        path: '/finance/payroll',
        element: 'Payroll',
      },
      {
        path: '/finance/slip',
        element: 'PaymentSlip',
      },
      {
        path: '/finance/slip/list',
        element: 'PaymentSlipList',
      },
      {
        path: '/finance/payroll/calc',
        element: 'CalculatePayroll',
      },
      {
        path: '/clients/payment-follow-up',
        element: 'paymentFollowup',
      },
      {
        path: '/clients/contract-follow-up',
        element: 'contractFollowup',
      },
      {
        path: '/settings/users/reset-password',
        element: 'resetPassword',
      },
      {
        path: 'store',
        element: 'Store',
      },
      {
        path: 'settings/category',
        element: 'Category',
      },
      {
        path: 'store/items',
        element: 'Items',
      },
      {
        path: 'store/stock',
        element: 'Stock',
      },
      {
        path: 'store/requests',
        element: 'Requests',
      },
      {
        path: 'store/approval',
        element: 'Approval',
      },
      {
        path: 'coffeepage',
        element: 'CoffeeExportPage',
      },
      {
        path: 'coffeepage/batches',
        element: 'CoffeeBatches',
      },
      {
        path: 'coffeepage/farmers',
        element: 'Farmers',
      },
      {
        path: 'coffeepage/export-orders',
        element: 'ExportOrders',
      },
      {
        path: 'coffeepage/shipments',
        element: 'Shipments',
      },
      {
        path: 'coffeepage/certificates',
        element: 'Certificates',
      },
      {
        path: 'coffeepage/stock-movements',
        element: 'StockMovements',
      },
      {
        path: 'settings/warehouse',
        element: 'Warehouse',
      },
    ],
  },
  {
    role: 'storekeeper',
    children: [
      {
        path: 'dashboard',
        element: 'Dashboard',
      },
      {
        path: '/user/change-password',
        element: 'change password',
      },
      {
        path: 'store',
        element: 'Store',
      },
      {
        path: 'settings/category',
        element: 'Category',
      },
      {
        path: 'store/items',
        element: 'Items',
      },
      {
        path: 'store/stock',
        element: 'Stock',
      },
      {
        path: 'store/requests',
        element: 'Requests',
      },
      {
        path: 'store/approval',
        element: 'Approval',
      },
      {
        path: 'coffeepage',
        element: 'CoffeeExportPage',
      },
      {
        path: 'coffeepage/batches',
        element: 'CoffeeBatches',
      },
      {
        path: 'coffeepage/farmers',
        element: 'Farmers',
      },
      {
        path: 'coffeepage/export-orders',
        element: 'ExportOrders',
      },
      {
        path: 'coffeepage/shipments',
        element: 'Shipments',
      },
      {
        path: 'coffeepage/certificates',
        element: 'Certificates',
      },
      {
        path: 'coffeepage/stock-movements',
        element: 'StockMovements',
      },
      {
        path: 'settings/warehouse',
        element: 'Warehouse',
      },
    ],
  },

  {
    path: 'unauthorizedpage',
    element: 'UnauthorizedPage',
  },
];
