import { Card, useTheme ,MenuItem,TextField, Box} from "@mui/material";
import React from "react";
import { tokens } from "../utils/theme";
import { useState } from "react";
import { useUsersData } from "../api/userApi/clinetHook";
import Minisearch from "minisearch";
const SearchUser = ({ onUserSelect }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [selected, setSelected] = useState(0);
  const [userId, setUserId] = useState("");
  const [results, setResults] = useState([]);
  const { data: users, isFetched } = useUsersData();
  const miniSearch = new Minisearch({
    fields: ["firstName","lastName"],
    storeFields: ["id", "firstName","lastName"],
  });
  if (users) {
    miniSearch.addAll(users);
  }
  const handleChange = (e) => {
    setUserId(e.target.value);
    const res = miniSearch.search(userId,{fuzzy:0.3});
    setResults(res);
  };
  const handleUserSelect = (value) => {
    onUserSelect({ value: value, clicked: true });
    setResults([])
  };
  return (
    <Box>
      <TextField
        label="Search Users"
        name="user_id"
        fullWidth
        variant="outlined"
        type="search"
        InputLabelProps={{
          color: "success",
        }}
        onChange={handleChange}
        value={userId}
        size="small"
      />
      <Card>
        {results.map((value) => (
          <MenuItem
            sx={{
              background: selected === value.id ? colors.blueAccent[500] : "",
            }}
            key={value.id}
            value={value.id}
            onClick={() => {
              handleUserSelect(value);
              setSelected(value.id);
              setUserId(value.firstName);
            }}
          >
            {`${value.firstName} ${value.lastName}`}
          </MenuItem>
        ))}
      </Card>
    </Box>
  );
};

export default SearchUser;
