import { Box, Grid, Paper, useTheme } from "@mui/material";
import React, { useState } from "react";
import Header from "../../components/Header";
import SearchBar from "../../components/SearchBar";
import SearchByCompName from "../../components/SearchByCompName";

import { tokens } from "../../utils/theme";
import PaymentFollowupForm from "./PaymentFollowupForm";
import PaymentFollowupList from "./PaymentFollowupList";

function PaymentFollowup() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [clicked, setClicked] = useState(false);
  const [clientId, setClientId] = useState("");
  const [editData, setEditData] = useState({});

  const onSelectedRow = (value) => {
    console.log(value);
    setEditData(value);
  };

  const handleClientSelect = ({ value, clicked }) => {
    //console.log(value.company_code);
    setClientId(value.company_code);
    setClicked(clicked);
  };

  return (
    <Box m={1}>
      <Header
        title="Payment Followup"
        subtitle="here you can create a payment followup reminder"
      />
      <Grid container spacing={2}>
        <Grid item xs={12} sm={4}>
          <Paper
            sx={{
              padding: '1rem',
              color: colors.grey[100],
              background: colors.primary[400],
            }}
          >
            {/* <SearchBar onClientSelect={handleClientSelect} /> */}
            <SearchByCompName onClientSelect={handleClientSelect} />
          </Paper>
        </Grid>
        <Grid item xs={12} sm={8}>
          {clicked && (
            <Box>
              <Paper
                sx={{
                  padding: '1rem',
                  color: colors.grey[100],
                  background: colors.primary[400],
                }}
              >
                <PaymentFollowupForm clientId={clientId} editData={editData} setEditData={setEditData}/>
              </Paper>

              <Paper
                sx={{
                  padding: '1rem',
                  color: colors.grey[100],
                  background: colors.primary[400],
                  marginTop: '30px',
                }}
              >
                <PaymentFollowupList
                  clientId={clientId}
                  selectedRow={onSelectedRow}
                />
              </Paper>
            </Box>
          )}
        </Grid>
      </Grid>
    </Box>
  );
}

export default PaymentFollowup;
