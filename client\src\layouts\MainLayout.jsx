import {
  CssB<PERSON>line,
  ThemeProvider,
} from "@mui/material";
import React from "react";
import { Navigate, Outlet, } from "react-router-dom";
import Toast from "../components/Toast";
import { useStateContext } from "../context/ContextProvider";
import SideBar from "../pages/global/SideBar";
import TopBar from "../pages/global/TopBar";
import { ColorModeContext, useMode } from "../utils/theme";
import { Suspense } from 'react';
import Loading from '../components/Loading';

function MainLayout() {
  const [theme, colorMode] = useMode();
  const { token, notification } = useStateContext();
  if (!token) {
    return <Navigate to="/login" />;
  }
  return (
    <ColorModeContext.Provider value={colorMode}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <div className="app">
          <SideBar />
          <main className="content">
            <TopBar />
         
              {notification && <Toast message={notification} />}
              <Suspense fallback={<Loading />}>
                <Outlet />
              </Suspense>
          </main>
        </div>
      </ThemeProvider>
    </ColorModeContext.Provider>
  );
}

export default MainLayout;
