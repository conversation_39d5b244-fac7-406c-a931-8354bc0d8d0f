import { Box, Grid } from "@mui/material";
import React from "react";
import LinkCard from "../../components/LinkCard";
import Header from "../../components/Header";

function Report() {
  return (
    <Box margin="10px">
      <Header title="All Reports" subtitle="all reports are found in here " />
      <Grid container spacing={2}>
      <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="Employee Report"
            subtitle="All employees report"
            to="/reports/employee"
          />
        </Grid>
      <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="Payroll Report"
            subtitle="All Payroll related reports"
            to="/reports/payroll"
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <LinkCard
            title="Severance Report"
            subtitle="all severance related reports"
            to="/reports/severance"
          />
        </Grid>
       
        <Grid item xs={12} sm={4}>
          <LinkCard
            title="Leave Report"
            subtitle="All leave related reports"
            to="/reports/leave"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="Termination Report"
            subtitle="filter Termination report using Dates and Sights  "
            to="/reports/TerminationReport"
          />
        </Grid>
      </Grid>
    </Box>
  );
}

export default Report;
