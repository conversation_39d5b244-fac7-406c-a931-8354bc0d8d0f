<?php

namespace App\Models;

use App\Models\finance\OverTime;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Assign extends Model
{
    use HasFactory;
    protected $guarded = [];

    public function empBasic()
    {
        return $this->belongsTo(EmpBasic::class);
    }

    public function client(){
        return $this->belongsTo(Client::class);
    } 
}
