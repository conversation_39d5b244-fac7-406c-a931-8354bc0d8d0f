import { Container } from "@mui/material";
import WorkExeperienceForm from "./WorkExeperienceForm";
import { useState } from "react";
import WorkExperienceList from "./WorkExperienceList";

function WorkExperiance({ id }) {
  const [editData, setEditData] = useState({});
  const onSelectRow = (value) => {
    setEditData(value);
  };

  return (
    <Container>
      <WorkExeperienceForm empId={id} data={editData} />
      <WorkExperienceList id={id} selectedRow={onSelectRow}/>
    </Container>
  );
}

export default WorkExperiance;
