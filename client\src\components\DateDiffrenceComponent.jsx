import { Box, TextField } from "@mui/material";
import React, { useState } from "react";

const DateDiffrenceComponent = ({ labelOne, labelTwo, onDateChange }) => {
  const [fromDate, setFromDate] = useState(new Date().toISOString().slice(0, 10));
  const [toDate, setToDate] = useState(new Date().toISOString().slice(0, 10));

  const handleFromDateChange = (event) => {
    setFromDate(event.target.value);
    onDateChange(event.target.value, toDate);
  };
  const handleToDateChange = (event) => {
    setToDate(event.target.value);
    onDateChange(fromDate,event.target.value);
  }
  return (
    <Box display='flex'>
      <TextField label={labelOne} name="from_date" type="date" 
        value={fromDate }
        onChange={handleFromDateChange}
        InputLabelProps={{
              color: "success",
            }}
            margin="normal"  
      />
      <TextField label={labelTwo} name="from_date" type="date" 
        value={toDate }
        onChange={handleToDateChange}
        margin="normal"
        sx={{marginLeft:"5px"}}
      />
    </Box>
  );
};

export default DateDiffrenceComponent;
