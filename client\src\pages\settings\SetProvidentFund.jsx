import { <PERSON>, TextField, useTheme, Button } from "@mui/material";
import api from "../../api/config/axiosConfig";
import React, { useState } from "react";
import { tokens } from "../../utils/theme";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import CustomPopOver from "../../components/CustomPopOver";
import { useStateContext } from "../../context/ContextProvider";

function SetProvidentFund({ isopen, anchorEl, handleClose }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const { data: pro } = useQuery(
    ["provident"],
    () =>
      api.get("provident").then(({ data }) => {
        setProvident(data);
        return data;
      }),
    {
      staleTime: 600000,
    }
  );
  const [provident, setProvident] = useState({
    provident_emp: "",
    provident_sns: "",
  });
const createProvident=useMutation((data)=>api.post('provident',data),{
  onSuccess:()=>{
    queryClient.invalidateQueries(["provident"]);
        setNotification("provident created successfully");
  }
})
  const handleChange = (e) => {
    setProvident({
      ...provident,
      [e.target.name]: e.target.value,
    });
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if(pro.id){

      api.put(`provident/${pro.id}`, provident).then(() => {
        queryClient.invalidateQueries(["provident"]);
        setNotification("provident updated successfully");
        handleClose();
      });
    }else{
      createProvident.mutate(provident)
         handleClose();
    }
  };
  return (
    <CustomPopOver
      isopen={isopen}
      anchorEl={anchorEl}
      handleClose={handleClose}
    >
      <Box
        component="form"
        onSubmit={handleSubmit}
        sx={{
          background: colors.primary[400],
          color: colors.grey[100],
          padding: 2,
        }}
      >
        <TextField
          label="Set Provident Employee"
          name="provident_emp"
          onChange={handleChange}
          value={provident.provident_emp}
          type="number"
          InputLabelProps={{
            color: "success",
          }}
        />
        <TextField
          label="Set Peovident Sns"
          name="provident_sns"
          onChange={handleChange}
          value={provident.provident_sns}
          type="number"
          InputLabelProps={{
            color: "success",
          }}
          sx={{ml:1}}
        />
        <Box sx={{ pt: 1, display: "flex", justifyContent: "space-between" }}>
          <Button onClick={handleClose} sx={{ color: colors.grey[100] }}>
            cancel
          </Button>
          <Button type="submit" sx={{ color: colors.grey[100] }}>
            submit
          </Button>
        </Box>
      </Box>
    </CustomPopOver>
  );
}

export default SetProvidentFund;
