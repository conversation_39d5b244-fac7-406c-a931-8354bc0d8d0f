import { Box, Grid, Paper, useTheme } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import Header from "../../../components/Header";
import { tokens } from "../../../utils/theme";
import api from "../../../api/config/axiosConfig";

import SearchBar from "../../../components/SearchBar";
import ListOfEmployee from "./ListOfEmployee";
function Payroll() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [clicked, setClicked] = useState(false);
  const [clientId, setClientId] = useState("");

 
  const handleClientSelect = ({ value, clicked }) => {
    setClientId(value.id);
    setClicked(clicked);
  };
  return (
    <Box m="20px">
      <Header
        title="Payroll"
        subtitle="select client and employee to calculate payroll"
      />
      <>
        
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}>
            <Paper
              sx={{
                padding: "1rem",
                color: colors.grey[100],
                background: colors.primary[400],
              }}
            >
              <SearchBar  onClientSelect={handleClientSelect} />
            </Paper>
          </Grid>

          <Grid item xs={12} sm={8}>
            {clicked && (
              <Paper
                sx={{
                  padding: "1rem",
                  color: colors.grey[100],
                  background: colors.primary[400],
                }}
              >
                <ListOfEmployee clientId={clientId} />
              </Paper>
            )}
          </Grid>
          
        </Grid>
      </>
    </Box>
  );
}

export default Payroll;
