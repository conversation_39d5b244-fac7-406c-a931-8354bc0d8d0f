<?php

namespace App\Http\Controllers\Api\helper;

use App\Models\AnnualLeave;
use DateInterval;
use Illuminate\Support\Facades\Date;

class LeaveHelper
{

    public function getTotalLeave14($employedYear, $effectiveDate)
    {
        $sumOfLeave = 0;
        $pattern = [];
        $interval = $employedYear->diff($effectiveDate);
        for ($i = 0; $i < $interval->y; $i++) {
            $year = $employedYear->add(new DateInterval('P1Y'))->format('Y-m-d');
            $pattern[$year] = 14 + $i;
            $sumOfLeave += $i + 14;
        }
        $index = end($pattern);
        return [
            'totalLeave' => $sumOfLeave,
            'monthInterval' => $interval->m,
            'dayInterval' => $interval->d,
            'yearIndex' => $interval->y,
            'pattern' => $pattern,
            'days' => $interval->days,
            'index' => $index
        ];
    }

    public function getTotalLeave16($effectiveDate, $currentYear, $leaveIndex = 16)
    {
        $interval = $effectiveDate->diff($currentYear);
        $pattern = [];
        $isReapted = false;
        for ($i = 0; $i < $interval->y; $i++) {
            $year = $effectiveDate->add(new DateInterval('P1Y'))->format('Y');
            $pattern[$year] = floor($i / 2) + $leaveIndex;
        }
        $sum = array_sum($pattern);
        $lastTwo = end($pattern) + prev($pattern);

        if (end($pattern) === prev($pattern)) {
            $isReapted = true;
        }
        return [
            'totalLeave' => $sum,
            'monthInterval' => $interval->m,
            'dayInterval' => $interval->d,
            'yearIndex' => $interval->y,
            'days' => $interval->days,
            'lastTwo' => $lastTwo,
            'pattern' => $pattern,
            'isReapted' => $isReapted,
        ];
    }
    public function isLeapYear($year)
    {
        $date = date('Y', $year->getTimestamp());
        $is_leap_year = date('L', strtotime("$date-01-01"));
        return $is_leap_year;
    }

    public function annualLeave($id)
    {
        $annualLeave = AnnualLeave::where('emp_basic_id', $id)->get();
        $other = 0;
        $fromExpired = 0;
        foreach ($annualLeave as $leave) {
            $other += $leave->leave_taken;
            $fromExpired += $leave->from_expired;
        }
        $totalLeave = $other + $fromExpired;

        return [
            'totalLeave' => $totalLeave,
        ];
    }

    public function leaveTakenTwoYears($id, $expiredYear)
    {

        $leaveTakens = AnnualLeave::where('emp_basic_id', $id)->where('reason', 'Annual leave')->where('from_expired', null)->where('reg_date', '>=', $expiredYear)->get();
        $leaves = 0;
        foreach ($leaveTakens as $taken) {
            $leaves += $taken->leave_taken;
        }

        return [
            'leaveTakenIntwoYears' => $leaves
        ];
    }
}
