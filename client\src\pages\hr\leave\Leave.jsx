import { Box, Grid, Alert } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import api from '../../../api/config/axiosConfig';
import { useEmployeeData } from '../../../api/userApi/clinetHook';
import Header from '../../../components/Header';
import OnClickCard from '../../../components/OnClickCard';

function Leave() {
  const location = useLocation();
  const formData = location.state;
  const navigate = useNavigate();
  const { data: emp, isFetched } = useEmployeeData(formData.emp_basic_id);

  const { data: leave } = useQuery(
    ['leave', formData.emp_basic_id],
    () => api.get(`leave/${formData.emp_basic_id}`).then(({ data }) => data),
    {
      staleTime: 600000,
      refetchOnWindowFocus: false,
      enabled: !!formData.emp_basic_id,
    }
  );
  const onClickAddLeave = () => {
    navigate('/employees/add-leaves', {
      state: formData,
    });
  };
  const onClickLeaveSummary = () => {
    navigate('/employees/leave-summary', {
      state: { leave: leave, formData: formData },
    });
  };
  const onclickeLookup = () => {
    navigate('/employees/leave-summary-lookup', {
      state: { leave, formData },
    });
  };

  return (
    <Box mx="40px">
      <Header
        title="Employee Leave Page"
        subtitle={
          isFetched &&
          `FullName: ${emp.first_name} ${emp.middle_name} ${emp.last_name}`
        }
      />

      {leave?.intervalFromEmpToCurrentDate?.days < 182 && (
        <Alert variant="filled" severity="error" sx={{ marginBottom: 2 }}>
          This Employee has worked less than 182 days. 
          <br />
          has worked for {leave?.intervalFromEmpToCurrentDate?.days} days
          </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} sm={4}>
          <OnClickCard
            onClick={onClickAddLeave}
            title="Apply Leave"
            content="give a leave to employees here"
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <OnClickCard
            title="Leave Taken Detail"
            content="You can see the Leave Taken Detail untile now  here"
            onClick={() => {
              navigate('/employees/leave-detail', {
                state: formData,
              });
            }}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <OnClickCard
            title="Leave Taken In Two Years"
            content="You can see Total Leave taken In Two Years here"
            onClick={() => {
              navigate('/employees/leave-detail-two', {
                state: formData,
              });
            }}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <OnClickCard
            title="Employee Leave  Summary"
            content="You can watch Employee Leave Summary Here..."
            onClick={onClickLeaveSummary}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <OnClickCard
            title="Leave Lookup Summary"
            content="Leave Lookup Summary"
            onClick={onclickeLookup}
          />
        </Grid>
      </Grid>
    </Box>
  );
}

export default Leave;
