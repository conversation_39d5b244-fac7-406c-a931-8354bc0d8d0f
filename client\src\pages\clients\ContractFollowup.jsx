import { Box, Grid, Paper, useTheme } from '@mui/material';
import React, { useState } from 'react';
import Header from '../../components/Header';
import SearchBar from '../../components/SearchBar';
import { tokens } from '../../utils/theme';
import ContractFollowupForm from './ContractFollowupForm';
import ContractFollowupList from './ContractFollowupList';

function ContractFollowup() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [clicked, setClicked] = useState(false);
  const [clientId, setClientId] = useState('');
  const [editData, setEditData] = useState({});

  const onSelectedRow = (value) => {
    console.log(value);
    setEditData(value);
  };
  const handleClientSelect = ({ value, clicked }) => {
    setClientId(value.id);
    setClicked(clicked);
  };
  return (
    <Box m={1}>
      <Header
        title="Contract Followup"
        subtitle="create and update your contract here"
      />
      <Grid container spacing={1}>
        <Grid item xs={12} sm={4}>
          <Paper
            sx={{
              padding: '1rem',
              color: colors.grey[100],
              background: colors.primary[400],
            }}
          >
            <SearchBar onClientSelect={handleClientSelect} />
          </Paper>
        </Grid>
        <Grid item xs={12} sm={8}>
          {clicked && (
            <Box>
              <Paper
                sx={{
                  padding: '1rem',
                  color: colors.grey[100],
                  background: colors.primary[400],
                }}
              >
                <ContractFollowupForm
                  clientId={clientId}
                  editData={editData}
                  setEditData={setEditData}
                />
              </Paper>
              <Paper
                sx={{
                  padding: '1rem',
                  color: colors.grey[100],
                  background: colors.primary[400],
                }}
              >
                <ContractFollowupList
                  clientId={clientId}
                  selectedRow={onSelectedRow}
                />
              </Paper>
            </Box>
          )}
        </Grid>
      </Grid>
    </Box>
  );
}

export default ContractFollowup;
