<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sponsored_employees', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('emp_basic_id');
            $table->foreign('emp_basic_id')->references('id')->on('emp_basics')->onDelete('cascade');
            $table->string('name')->nullable();
            $table->string('company_name')->nullable();
            $table->string('phone')->nullable();
            $table->string('office_phone')->nullable();
            $table->date('effective_date')->format('Y-m-d')->nullable();
            $table->date('expire_date')->format('Y-m-d')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sponsored_employees');
    }
};
