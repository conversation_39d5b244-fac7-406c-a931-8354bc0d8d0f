import React from "react";
import { CardContent, Typography, useTheme } from "@mui/material";
import { tokens } from "../utils/theme";

const Messages = ({ companyName, createdAt, reminder ,onclick,type}) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  return (
    <CardContent
    onClick={onclick}
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        marginBottom: 1,
        borderRadius: "5px",
        borderColor: "ButtonShadow",
        backgroundColor: colors.primary[400],
      }}
    >
      <Typography variant="body1" color={colors.blueAccent[500]}>
        {companyName}
      </Typography>
      {type=='contract' &&(
        <Typography variant="body1" color={colors.grey[100]} marginLeft={0.5}>
        {`Contract Followup ${reminder} reminder`}
      </Typography>
      )}
      {type=='payment' &&(
        <Typography variant="body1" color={colors.grey[100]} marginLeft={0.5}>
        {`Payment Followup  ${reminder} reminder`}
      </Typography>
      )}
      {type=='store' &&(
        <Typography variant="body1" color={colors.grey[100]} marginLeft={0.5}>
        {`Store Request  ${reminder}`}
      </Typography>
      )}
      <Typography  marginLeft='10px' variant="caption" color={colors.grey[100]}>
        {createdAt}
      </Typography>
    </CardContent>
  );
};

export default Messages;
