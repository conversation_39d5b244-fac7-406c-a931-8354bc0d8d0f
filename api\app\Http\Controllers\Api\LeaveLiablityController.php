<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\helper\LeaveHelper;
use App\Http\Controllers\Controller;
use App\Models\EmpBasic;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LeaveLiablityController extends Controller
{

    public  $twoYears;

    public function __construct()
    {

        $this->twoYears = date('Y-m-d', strtotime('-2 years'));
    }
    public function salaryRate($basicSalary)
    {
        return $basicSalary / 30;
    }
    public function liablityFunc()
    {
        $sumLeave = 0;
        $sumLeaveInMoney = 0;
        $count = 0;
        $employee = EmpBasic::where('termination_status', 0)->get()->map(function ($data) use (&$sumLeave, &$sumLeaveInMoney, &$count) {
            $annualLeave = new AnnualLeaveController();
            $salaryRate = $this->salaryRate($data->basic_salary);
            $extractLeave = $annualLeave->calculation($data->id, true);
            $entitledLeave = $extractLeave['availableLeaveEntitled'];
            $actualLeaveLeft = $extractLeave['availableLeave'];
            $expiredLeave = $extractLeave['expiredLeave'];
            $totalLeaveLiable = $actualLeaveLeft + $expiredLeave;
            $leaveLeftInMony = $salaryRate * $totalLeaveLiable;
            $sumLeave += $totalLeaveLiable;
            $sumLeaveInMoney += $leaveLeftInMony;
            $fullName = $data['first_name'] . " " . $data['middle_name'] . " " . $data['last_name'];
            $salary = $data['basic_salary'];
            $employeedDate = $data['start_date'];
            $count++;
            return [
                'id' => $count,
                'total_liablity' => round($totalLeaveLiable, 2),
                'leave_liable_in_money' => round($leaveLeftInMony, 2),
            ];
        });
        $totals_leave = $employee->sum('total_liablity');
        $totals_money = $employee->sum('leave_liable_in_money');
        return [
            'totals_leave' => round($totals_leave, 2),
            'totals_money' => round($totals_money, 2),
        ];
    }
    public function liablity()
    {
        $sumLeave = 0;
        $sumLeaveInMoney = 0;
        $count = 0;
        $employee = EmpBasic::where('termination_status', 0)->get()->map(function ($data) use (&$sumLeave, &$sumLeaveInMoney, &$count) {
            $annualLeave = new AnnualLeaveController();
            $salaryRate = $this->salaryRate($data->basic_salary);
            $extractLeave = $annualLeave->calculation($data->id, true);
            $entitledLeave = $extractLeave['availableLeaveEntitled'];
            $actualLeaveLeft = $extractLeave['availableLeave'];
            $expiredLeave = $extractLeave['expiredLeave'];
            $totalLeaveLiable = $actualLeaveLeft + $expiredLeave;
            $leaveLeftInMony = $salaryRate * $totalLeaveLiable;
            $sumLeave += $totalLeaveLiable;
            $sumLeaveInMoney += $leaveLeftInMony;
            $fullName = $data['first_name'] . " " . $data['middle_name'] . " " . $data['last_name'];
            $salary = $data['basic_salary'];
            $employeedDate = $data['start_date'];
            $count++;
            return [
                'id' => $count,
                'full_name' => $fullName,
                'salary' => round($salary, 2),
                'employeed_date' => $employeedDate,
                'entitled_leave' => round($entitledLeave, 2),
                'actual_leave_left' => round($actualLeaveLeft, 2),
                'expired_leave' => round($expiredLeave, 2),
                'total_liablity' => round($totalLeaveLiable, 2),
                'leave_liable_in_money' => round($leaveLeftInMony, 2),
            ];
        });
        $totals_leave = $employee->sum('total_liablity');
        $totals_money = $employee->sum('leave_liable_in_money');
        return response([
            'leaves' => $employee,
            'totals_leave' => round($totals_leave, 2),
            'totals_money' => round($totals_money, 2),
        ]);
    }
}
