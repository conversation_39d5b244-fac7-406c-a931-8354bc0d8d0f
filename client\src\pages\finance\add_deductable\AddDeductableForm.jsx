import React, { useState, useEffect } from "react";
import { Grid, Box, TextField, MenuItem, Button } from "@mui/material";
import { useStateContext } from "../../../context/ContextProvider";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";

const rateType = [
  {
    label: "Days",
    value: "days",
  },
  {
    label: "Hours",
    value: "hours",
  },
];

function AddDeductableForm({ formData, editData, setEditData }) {
  const queryClient = useQueryClient();
  const { setNotification, user } = useStateContext();

  const [addDeduct, setAddDeduct] = useState({
    deduct_date: '',
    rate: '',
    amount: '',
    duration: '',
    reason: '',
  });

  if (editData) {
    useEffect(() => {
      setAddDeduct(editData);
    }, [editData]);
  }

  const handleChange = (e) => {
    setAddDeduct({
      ...addDeduct,
      [e.target.name]: e.target.value,
    });
  };

  const createAddDeduct = useMutation(
    (data) => api.post('additional_deductable', data),
    {
      onSuccess: () => {
        setNotification('additional deductable inserted successfully');
        queryClient.invalidateQueries({ queryKey: 'additional_deductable' });
      },
    }
  );

  const handleSubmit = (e) => {
    e.preventDefault();

    const data = new FormData(e.currentTarget);
    const remark = data.get('reason') ? data.get('reason') : 'default reason';
    const payload = {
      emp_basic_id: formData.emp_basic_id,
      client_id: formData.client_id,
      deduct_date: data.get('deduct_date'),
      rate: data.get('rate'),
      amount: data.get('amount'),
      duration: data.get('duration'),
      reason: remark,
      user_id: user.id,
    };
    if (editData && editData.id) {
      api.put(`additional_deductable/${editData.id}`, payload).then(() => {
        setNotification('add deductable record updated successfully');
        setEditData({});
        queryClient.invalidateQueries({ queryKey: 'additional_deductable' });
      });
    } else {
      createAddDeduct.mutate(payload);
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="From Date"
            name="deduct_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={
              addDeduct.deduct_date || new Date().toISOString().slice(0, 10)
            }
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Rate hour/days"
            name="rate"
            fullWidth
            select
            variant="standard"
            type="number"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={addDeduct.rate ? addDeduct.rate : 'days'}
          >
            {rateType.map((num) => (
              <MenuItem key={num.value} value={num.value}>
                {num.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Number of Days Worked"
            name="duration"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={addDeduct.duration}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="salary per hour or day"
            name="amount"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={addDeduct.amount}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Reason"
            name="reason"
            fullWidth
            multiline
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={addDeduct.reason}
          />
        </Grid>
      </Grid>
      <span style={{ float: 'inline-start' }}>
        <Button type="submit" variant="contained" color="success">
          {editData.id ? <>update</> : <>create</>}
        </Button>
      </span>
    </Box>
  );
}

export default AddDeductableForm;
