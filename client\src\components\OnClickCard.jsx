import { Card, CardContent, Typography, useTheme } from "@mui/material";
import React from "react";
import { tokens } from "../utils/theme";

function OnClickCard({ onClick, title, content }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  return (
    <Card
      onClick={onClick}
      style={{
        color: colors.grey,
        background: colors.primary[400],
        padding: 5,
        cursor: "pointer",
      }}
      variant="outlined"
    >
      <CardContent>
        <Typography variant="h5" component="div" gutterBottom>
          {title}
        </Typography>
        <Typography color="textSecondary" variant="body2">
          {content}
        </Typography>
      </CardContent>
    </Card>
  );
}

export default OnClickCard;
