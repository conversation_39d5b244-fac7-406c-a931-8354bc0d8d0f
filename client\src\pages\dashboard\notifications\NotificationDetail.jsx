import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Container,
  Typography,
  useTheme,
} from '@mui/material';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import api from '../../../api/config/axiosConfig';
import _CircularProgress from '../../../components/_CircularProgress';
import { tokens } from '../../../utils/theme';

const cardStyle = {
  padding: '10px',
  margin: '5px',
  // boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
  borderRadius: '8px',
  display: 'flex',
  justifyContent: 'space-between',
};
const NotificationDetail = () => {
  const queryClient = useQueryClient();
  const { id } = useParams();
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [doc, setDoc] = useState('');
  const [ext, setExt] = useState('');
  const currentMonth = new Date().getMonth() + 1;

  const {
    data: contract,
    isLoading,
    isError,
  } = useQuery(
    [`notification_${id}`, id],
    () =>
      api.get(`/markAsRead/${id}`).then(({ data }) => {
        console.log('API response:', data);
        if (data.data.document) {
          setDoc(data.data.document);
          setExt(data.data.ext);
        }
        console.log(data);
        return data;
      }),
    {
      onSuccess: ({ data }) => {
        console.log('before invalidateQueries', data);
        const responseData = data;
        // Invalidate cached queries related to unread notifications to ensure the data is fresh
        queryClient.invalidateQueries({
          queryKey: ['notifications_unread', 'notify'],
        });

        // Log the received data for debugging purposes
        console.log(responseData);

        // if (responseData.message.type === 'contract') {
        //   api
        //     .get(`contract-follow-up/${responseData.message.contract_id}`)
        //     .then(({ data }) => {
        //       // Log the contract follow-up data for debugging
        //       console.log(data);
        //       // Extract the month from the last triggered date of the contract follow-up
        //       const last = new Date(data.last_triggerd_date).getMonth() + 1;

        //       // Check if the last triggered month is the current month
        //       if (last === currentMonth) {
        //         // If so, make an API call to mark the follow-up as due
        //         api.get(`due/${data.data.id}`);
        //       }
        //     });
        // }
        // Fetch detailed information about the contract follow-up using the contract ID from the notification data
      },
      onError(error) {
        console.log(error);
      },
      enabled: !!id,
    }
  );

  const handleDownload = () => {
    const byteCharacters = atob(doc); // Use the `doc` variable
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: `application/${ext}` }); // Use the `ext` variable

    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${contract.data.message.company_name}-contract-document.${ext}`; // Use the `ext` variable
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return <_CircularProgress />;
  }

  return (
    <Container maxWidth="md">
      <Card
        variant="elevation"
        style={{
          color: colors.grey,
          background:
            theme.palette.mode == 'dark' ? colors.primary[400] : undefined,
        }}
      >
        <CardContent>
          <div className="info-container">
            <div>Contract sight agreement</div>
            <div style={{}}>
              <Typography variant="h3" color={colors.grey[100]} align="center">
                {contract.data.message.company_name}
              </Typography>
              <div style={{ maxWidth: '80%', margin: 'auto' }}>
                <div style={cardStyle}>
                  <Typography variant="button">Agreement Date :</Typography>
                  <Typography marginLeft={1} variant="body1">
                    <u>{contract.data.message.start_date}</u>
                  </Typography>
                </div>
                <div style={cardStyle}>
                  <Typography variant="button">
                    Agreement closing Date :
                  </Typography>
                  <Typography marginLeft={1} variant="body1">
                    <u>{contract.data.message.end_date}</u>
                  </Typography>
                </div>
                <div style={cardStyle}>
                  <Typography variant="button">message</Typography>
                  <Typography marginLeft={1} variant="body1">
                    <u>{contract.data.message.message}</u>
                  </Typography>
                </div>
                <div style={cardStyle}>
                  <Typography variant="button">reminderset</Typography>
                  <Typography marginLeft={1} variant="body1">
                    <u>
                      {contract.data.message.contract_schedule
                        ? contract.data.message.contract_schedule
                        : contract.data.message.collection_schedule}
                    </u>
                  </Typography>
                </div>
                {contract.data.message.type === 'contract' ? (
                  <div style={cardStyle}>
                    <Typography variant="button">DownloadAgreement</Typography>
                    <Button
                      onClick={handleDownload}
                      size="small"
                      color="secondary"
                    >
                      downloadLink
                    </Button>
                  </div>
                ) : null}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Container>
  );
};

export default NotificationDetail;
