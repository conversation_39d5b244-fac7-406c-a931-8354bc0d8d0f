<?php

namespace App\Http\Controllers\api\report;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\settings\PayrollDate;

class GeneralPayrollReportController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getGeneralPayrollReport(Request $request)
    {
        $firstDate = $request->input('firstDate');
        $lastDate = $request->input('lastDate');
        $clientId = $request->input('clientId');
        $year = $request->input('year');
        $status = $request->input('status');
        $taxCenter = $request->input('taxCenter');
        $company = $request->input('company');
        $currentPayrollDate = $request->input('current');
        $region = $request->input('region');
        $bankCode = $request->input('bankCode');

        $nextYear = Carbon::parse($year)->addYear()->format('Y-m-d');

        $payrollDate = PayrollDate::latest()->first();

        $query = DB::table('payrolls')
        ->leftJoin('emp_basics', 'payrolls.emp_basic_id', '=', 'emp_basics.id')
        ->join('clients', 'payrolls.client_id', '=', 'clients.id')
        ->join('users', 'payrolls.user_id', '=', 'users.id')
        ->leftJoin('emp_banks', 'payrolls.emp_basic_id', '=', 'emp_banks.emp_basic_id')
        ->select(
            'payrolls.*',
            'payrolls.id as id',
            'emp_basics.start_date',
            'emp_basics.first_name',
            DB::raw("IF(emp_basics.id IS NULL, 'Deleted', 'Active') as emp_status"),
            'emp_basics.id as empId',
            'emp_basics.middle_name',
            'emp_basics.last_name',
            'emp_basics.gender',
            'emp_banks.bank_account',
            'clients.company_name',
            'clients.company_code',
            DB::raw("CONCAT_WS(' ', users.firstName, users.lastName) as userName"),
        );

        if ($status != 1) {
            if ($firstDate && $lastDate) {
                $query->whereBetween('payrolls.pay_date', [$firstDate, $lastDate]);
            }
            if ($clientId) {
                $query->where('payrolls.client_id', $clientId);
            }
            if ($company) {
                $query->where('clients.company_code', 'like', "%$company%");
            }
            if ($taxCenter) {
                $query->where('clients.tax_center', 'like', "%$taxCenter%");
            }
            if ($year) {
                $query->whereBetween('payrolls.pay_date', [$year, $nextYear]);
            }
            if ($currentPayrollDate != 0) {
                $payrollStart = $payrollDate['start_date'];
                $payrollEnd = $payrollDate['end_date'];
                $query->whereBetween('payrolls.pay_date', [$payrollStart, $payrollEnd]);
            }
            if ($region) {
                $query->where('clients.region', 'like', "%$region%");
            }
            if ($bankCode) {
                $query->where('emp_banks.bank_code', 'like', "%$bankCode%");
            }
        }

        $results = $query->get();
        Log::info('results: ', [$results]);
        log::info('results: ', [$results]);
        log::info('query: ', [$query]);
        log::info('SQL Query: ' . $query->toSql());
        log::info('Bindings: ', $query->getBindings());

        return response($results);
    }
}
