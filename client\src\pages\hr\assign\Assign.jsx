import { Grid,Box } from "@mui/material";
import React from "react";
import { useState } from "react";
import Header from "../../../components/Header";
import LeftSide from "../../../components/LeftSide";
import RightSide from "../../../components/RightSide";
import AssignEmployee from "./AssignEmployee";
import ListOfAssigendEmployee from "./ListOfAssigendEmployee";
import EmpLocation from "./EmpLocation";

function Assign() {
  const links = ["Assign Employee", "List Of Assined Employee", 'Find Employee'];
  const components = [AssignEmployee, ListOfAssigendEmployee, EmpLocation];
  const [selectedLinkIndex, setSelectedLinkIndex] = useState(0);
  const [selectedLink, setSelectedLink] = useState(links[0]);
  const handleLinkClick = (index) => {
    setSelectedLink(links[index]);
    setSelectedLinkIndex(index);
  };
  return (
    <Box style={{ margin: "10px" }}>
      <Header title="Assign Employee" subtitle="a place to assign employee" />
      <Grid container spacing={2}>
        <LeftSide 
            links={links}
            handleLinkClick={handleLinkClick}
            activeLink={selectedLinkIndex}
            m={12}
        />
        <RightSide
            components={components}
            selectedLinkIndex={selectedLinkIndex}
        />
      </Grid>
    </Box>
  );
}

export default Assign;
