<?php

namespace App\Models\finance;

use App\Models\Client;
use App\Models\EmpBasic;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payroll extends Model
{
    use HasFactory;
    // protected $guarded=[];

    protected $fillable = [
        'emp_basic_id', 'client_id', 'total_work_duration', 'basic_salary', 'all_holiday', 'over_time',
        'transport_allowance', 'absence', 'medical_cost', 'loan', 'other_loan', 'labor_union', 'social_supprot',
        'creadit_association', 'sport_association', 'additional', 'provident_emp', 'provident_sns', 'income_tax',
        'gross_salary', 'total_deduction', 'net_pay', 'other_transport_allowance', 'transport_allowance_deduction',
        'lw_out_pay_ded', 'add_ded_reason', 'oth_loan_reason', 'pay_date', 'pension_emp', 'pension_comp',
        'pcrate_salary', 'addpcreate_salary', 'nontax_transport_allowance', 'nontax_desert_allowance',
        'nontax_position_allowance', 'nontax_other_allowance', 'nontax_mobile_allowance', 'nontax_cleaning_allowance',
        'nontax_dsa_allowance', 'nontax_medical_allowance', 'additional_nontax_allowance', 'housing_allowance',
        'tax_dsa_allowance', 'exchange_rate', 'user_id', 'created_at','updated_at',
    ];
    public function empBasic(){
        return $this->belongsTo(EmpBasic::class);
    }
}
