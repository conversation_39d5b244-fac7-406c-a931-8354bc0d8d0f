import { ArrowForwardOutlined, DoubleArrowOutlined } from "@mui/icons-material";
import { IconButton, Box, Typography } from "@mui/material";

import React from "react";
import { Link } from "react-router-dom";

function LandingPage() {
  return (
    <Box
      display="flex"
      justifyContent="center"
      mt={15}
      mx={2}
      flexDirection="column"
      alignItems="center"
      gap={10}
    >
      <Typography variant="h1">Welcome to Edomias</Typography>
      <IconButton
        sx={{
          px: 5,
          py: 2,
          display: "flex",
          gap: 1,
          "&:hover": {
            bgColor: "red !important",
            bgcolor: "dark",
          },
        }}
        LinkComponent={Link}
        to="/login"
      >
        <Typography variant="h4">Sign in Here</Typography>
        <DoubleArrowOutlined sx={{ font: 10 }} />
      </IconButton>
    </Box>
  );
}

export default LandingPage;
