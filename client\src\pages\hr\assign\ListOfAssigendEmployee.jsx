import {
  Box,
  TextField,
  MenuItem,
  useTheme,
  Button,
  Typography,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';
import { Link } from 'react-router-dom';
import api from '../../../api/config/axiosConfig';
import { useStateContext } from '../../../context/ContextProvider';
import { tokens } from '../../../utils/theme';
import SearchBar from '../../../components/SearchBar';
import { useClientData } from '../../../api/userApi/clinetHook';

function ListOfAssigendEmployee() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const [clientId, setClientId] = useState('');

  const { data: client, isLoading } = useClientData();
  const handleClientSelect = ({ value, clicked }) => {
    setClientId(value.id);
    console.log(value.id);
  };

  const { data: assign, isSuccess } = useQuery(
    ['assign', clientId],
    () =>
      api.get(`/assign/client/${clientId}`).then(({ data }) => {
        console.log(data.data, clientId);
        return data.data;

      }),
    {
      enabled: !!clientId,
    }
  );

  const columns = [
    {
      field: 'count',
      headerName: 'Count',
      flex: 1,
      valueGetter: (params) => params.api.getRowIndex(params.row.id) + 1,
    },
    {
      field: 'id',
      headerName: 'Emp basic ID',
    },
    {
      field: 'first_name',
      headerName: 'FirstName',
      flex: 1,
    },
    {
      field: 'middle_name',
      headerName: 'MiddleName',
      flex: 1,
    },
    {
      field: 'last_name',
      headerName: 'LastName',
      flex: 1,
    },
    {
      field: 'position',
      headerName: 'Position',
      flex: 1,
    },
    {
      field: 'assign_date',
      headerName: 'Assignment Date',
      flex: 1,
    },
    {
      field: 'delete',
      headerName: 'Delete',
      flex: 1,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deletePosition = (e) => {
          e.stopPropagation();
          console.log(params.row.id);
          if (
            !window.confirm(
              'Are you sure you want to delete the employee from client?'
            )
          ) {
            return;
          }
          // delete api in here
          api
            .delete(`assign/${params.row.id}`)
            .then(() => {
              setNotification(
                'employee removed successfully Deleted successfully'
              );
              queryClient.invalidateQueries(['assign', clientId]);
            })
            .catch((err) => {
              console.log(err);
            });
        };

        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deletePosition}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                Remove
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];


  return (
    <Box>
      <SearchBar client={client} onClientSelect={handleClientSelect} />

      <Box
        marginTop="10px"
        height="75vh"
        sx={{
          '& .MuiDataGrid-root': {
            border: 'none',
          },
          '& .MuiDataGrid-cell': {
            borderBottom: 'none',
          },
          '& .name-column--cell': {
            color: colors.greenAccent[300],
          },
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: colors.blueAccent[700],
            borderBottom: 'none',
          },
          '& .MuiDataGrid-virtualScroller': {
            backgroundColor: colors.primary[400],
          },
          '& .MuiDataGrid-footerContainer': {
            borderTop: 'none',
            backgroundColor: colors.blueAccent[700],
          },
          '& .MuiCheckBox-root': {
            color: `${colors.greenAccent[200]}!important`,
          },
          '& .MuiDataGrid-toolbarContainer .MuiButton-text': {
            color: `${colors.grey[100]}!important`,
          },
        }}
      >
        {isSuccess && (
          <DataGrid columns={columns} rows={assign} hideFooter={true} />
        )}
      </Box>
    </Box>
  );
}

export default ListOfAssigendEmployee;
