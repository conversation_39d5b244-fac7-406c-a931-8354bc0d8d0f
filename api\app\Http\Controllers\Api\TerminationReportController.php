<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\settings\PayrollDate;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class TerminationReportController extends Controller
{
    public function getTerminationReport(Request $request)
    {
        $firstDate = $request->input('firstDate');
        $lastDate = $request->input('lastDate');
        $clientId = $request->input('clientId');
        $year = $request->input('year');
        $status = $request->input('status');
        $taxCenter = $request->input('taxCenter');
        $company = $request->input('company');
        $currentPayrollDate = $request->input('current');

        $nextYear = Carbon::parse($year)->addYear()->format('Y-m-d');

        $payrollDate = PayrollDate::latest()->first();

        $query = DB::table('terminations')
            ->join('emp_basics', 'terminations.emp_basic_id', '=', 'emp_basics.id')
            ->join('clients', 'terminations.client_id', '=', 'clients.id')
            ->select(
                'emp_basics.first_name',
                'emp_basics.id',
                'emp_basics.middle_name',
                'emp_basics.last_name',
                'emp_basics.start_date',
                'clients.company_name',
                'terminations.termination_date',
                'terminations.ref_no',
                'terminations.reason',
            );

        if ($status == 0) {
            if ($firstDate && $lastDate) {
                $query->whereBetween('terminations.termination_date', [$firstDate, $lastDate]);
            }
            if ($clientId) {
                $query->where('terminations.client_id', $clientId);
            }
            if ($year) {
                $query->whereBetween('terminations.termination_date', [$year, $nextYear]);
            }
            if ($company) {
                $query->where('clients.company_name', $company);
            }
            if ($currentPayrollDate != 0) {
                $payrollStart = $payrollDate['start_date'];
                $payrollEnd = $payrollDate['end_date'];
                //log::info('current payroll date: ' . $payrollStart . " " . $payrollEnd);
                $query->whereBetween('payrolls.pay_date', [$payrollStart, $payrollEnd]);
            }
            if ($taxCenter) {
                $query->where('clients.tax_center', $taxCenter);
            }
        }

        $results = $query->get();

        return response($results);
    }
}
