<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Applicant extends Model
{
    use HasFactory;

    protected $table = 'applicants';

    protected $fillable = [
        'job_posting_id',
        'first_name',
        'middle_name',
        'last_name',
        'email',
        'phone',
        'resume_url',
        'application_date',
        'status',
    ];

    public function jobPosting()
    {
        return $this->belongsTo(JobPosting::class);
    }

    public function examScore()
    {
        return $this->hasOne(ExamScore::class);
    }

    public function evaluationData()
    {
        return $this->hasOne(EvaluationData::class);
    }
}