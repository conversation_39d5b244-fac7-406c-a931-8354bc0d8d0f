import { useQuery } from "@tanstack/react-query";
import React from "react";
import api from "../../../api/config/axiosConfig";
import { useLocation } from "react-router-dom";
import { Box, CircularProgress } from "@mui/material";
import Header from "../../../components/Header";
import { DataGrid } from "@mui/x-data-grid";
import { useEmployeeData } from "../../../api/userApi/clinetHook";

function LeaveTakenTwoYears() {
  const location = useLocation();
  const data = location.state;
  const options = {
    weekday: "short",
    month: "short",
    day: "numeric",
    year: "numeric",
  };
  const {
    data: inTwoYears,
    isLoading,
    isFetched,
  } = useQuery(
    ["leavedetail", data.emp_basic_id],
    () =>
      api.get(`leave_twoyears/${data.emp_basic_id}`).then(({ data }) => {
        return data.map((row) => ({
          ...row,
          from_date: new Date(row.from_date).toLocaleDateString(
            "en-US",
            options
          ),
          to_date: new Date(row.to_date).toLocaleDateString("en-US", options),
          reg_date: new Date(row.reg_date).toLocaleDateString("en-US", options),
        }));
      }),
    {
      enabled: !!data.emp_basic_id,
      staleTime: 600000,
    }
  );
  const {data:emp}=useEmployeeData(data.emp_basic_id)
  const columns = [
    {
      field: "id",
      headerName: "ID",
    },
    {
      field: "leave_taken",
      headerName: "LeaveTaken",
    },
    {
      field: "from_expired",
      headerName: "FromExpired",
    },
    {
      field: "from_date",
      headerName: "startDate",
      flex:1,
    },
    {
      field: "to_date",
      headerName: "toDate",
      flex:1,
    },
    {
      field: "reg_date",
      headerName: "RegistrationDate",
      flex:1,
    },
    {
      field: "reason",
      headerName: "Reason",
    },
  ];
  return (
    <Box m={2} maxWidth="80%">
      <Header title="List of Leave Taken In Two Years" 
          subtitle={
          isFetched && `Employee Name: ${emp.first_name} ${emp.middle_name} ${emp.last_name}`
        }
      />
      {isLoading && <CircularProgress />}
      {isFetched && <DataGrid columns={columns} rows={inTwoYears} autoHeight />}
    </Box>
  );
}

export default LeaveTakenTwoYears;
