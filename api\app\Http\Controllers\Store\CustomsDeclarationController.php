<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Models\CustomsDeclaration;
use Illuminate\Http\Request;

class CustomsDeclarationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return CustomsDeclaration::with('exportOrder')->latest()->paginate();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'export_order_id' => 'required|exists:export_orders,id',
            'declaration_number' => 'required|string|unique:customs_declarations|max:255',
            'hs_code' => 'nullable|string|max:255',
            'export_license_number' => 'nullable|string|max:255',
            'date_filed' => 'nullable|date',
            'pdf_document_url' => 'nullable|string|max:255',
        ]);

        return CustomsDeclaration::create($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\CustomsDeclaration  $customsDeclaration
     * @return \Illuminate\Http\Response
     */
    public function show(CustomsDeclaration $customsDeclaration)
    {
        return $customsDeclaration->load('exportOrder');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\CustomsDeclaration  $customsDeclaration
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, CustomsDeclaration $customsDeclaration)
    {
        $data = $request->validate([
            'export_order_id' => 'sometimes|required|exists:export_orders,id',
            'declaration_number' => 'sometimes|required|string|unique:customs_declarations,declaration_number,' . $customsDeclaration->id . '|max:255',
            'hs_code' => 'nullable|string|max:255',
            'export_license_number' => 'nullable|string|max:255',
            'date_filed' => 'nullable|date',
            'pdf_document_url' => 'nullable|string|max:255',
        ]);

        $customsDeclaration->update($data);

        return $customsDeclaration;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\CustomsDeclaration  $customsDeclaration
     * @return \Illuminate\Http\Response
     */
    public function destroy(CustomsDeclaration $customsDeclaration)
    {
        $customsDeclaration->delete();

        return response()->noContent();
    }
}
