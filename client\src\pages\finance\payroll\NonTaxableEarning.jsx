import React from 'react'
import {
    Box,
    TextField,
  } from "@mui/material";
  import Header from "../../../components/Header";
function NonTaxableEarning() {
  return (
    <Box>
            <Header title="Non Taxable earning" heading="h6" />
            <TextField
              label="Transport Allowance"
              value="0"
              variant="standard"
              InputProps={{
                readOnly: true,
              }}
              disabled
              size="small"
              sx={{ mr: 1 }}
            />
            <TextField
              label="Position Allowance"
              value="0"
              variant="standard"
              InputProps={{
                readOnly: true,
              }}
              disabled
              size="small"
              sx={{ mr: 1 }}
            />
            <TextField
              label="Mobile Allowance"
              value="0"
              variant="standard"
              InputProps={{
                readOnly: true,
              }}
              disabled
              size="small"
              sx={{ mr: 1 }}
            />
            <TextField
              label="Desert Allowance"
              value="0"
              variant="standard"
              InputProps={{
                readOnly: true,
              }}
              disabled
              size="small"
              sx={{ mr: 1 }}
            />
            <TextField
              label="Cleaning Allowance"
              value="0"
              variant="standard"
              InputProps={{
                readOnly: true,
              }}
              disabled
              size="small"
              sx={{ mr: 1 }}
            />
            <TextField
              label="Medical Allowance"
              value="0"
              variant="standard"
              InputProps={{
                readOnly: true,
              }}
              disabled
              size="small"
              sx={{ mr: 1 }}
            />
            <TextField
              label="Other Allowance"
              value="0"
              variant="standard"
              InputProps={{
                readOnly: true,
              }}
              disabled
              size="small"
              sx={{ mr: 1 }}
            />
            <TextField
              label="Dsa Allowance"
              value="0"
              variant="standard"
              InputProps={{
                readOnly: true,
              }}
              disabled
              size="small"
              sx={{ mr: 1 }}
            />
          </Box>
  )
}

export default NonTaxableEarning