import {
  Box,
  Button,
  TextField,
  Typography,
  Grid,
  MenuItem,
  Paper,
  Checkbox,
  useTheme,
  CircularProgress,
} from "@mui/material";

import React, { useEffect, useState } from "react";
import { useStateContext } from "../../context/ContextProvider";
import Header from "../../components/Header";
import { register } from "../../api/userApi/login";
import { useNavigate, useParams } from "react-router-dom";
import { getSingleUser, updateUsers } from "../../api/userApi/users";
import { tokens } from "../../utils/theme";
// import router from "../../router";
import { routes } from "../../helper/routes";
import { groupRoutes } from "../../helper/groupRoutes";
const roles = [
  {
    label: "Admin",
    value: "admin",
  },
  {
    label: "HR",
    value: "hr",
  },
  {
    label: "Finance",
    value: "finance",
  },
  {
    label: "Operation",
    value: "operation",
  },
];
function Register() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  let { id } = useParams();
  const [loading, setLoading] = useState(false);
  const { setErrors, errors, setNotification, notification } =
    useStateContext();
  const [formData, setFormdata] = useState({
    firstName: "",
    lastName: "",
    username:"",
    email: "",
    phone: "",
    role: "",
    image: "",
    password: "",
    password_confirmation: "",
  });
  // const routes = router.routes[1].children;
  const getRoutes = routes.find((route) => route.role === formData.role);
  const [checkAll, setCheckAll] = useState(false);
  const [checkedItems, setCheckedItems] = useState([]);

  const handleCheckAllChange = (event) => {
    const isChecked = event.target.checked;
    setCheckAll(isChecked);
  };

  const handleCheckboxChange = (event, index) => {
    const isChecked = event.target.checked;
    if (checkAll) {
      if (isChecked) {
        setCheckedItems([
          ...checkedItems,
          ...getRoutes.children.map((route) => route.path),
        ]);
      } else {
        setCheckedItems([]);
      }
    } else {
      if (isChecked) {
        setCheckedItems([...checkedItems, index]);
      } else {
        setCheckedItems(
          checkedItems.filter((itemIndex) => itemIndex !== index)
        );
      }
    }
  };
  const handleChange = (e) => {
    setFormdata({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  if (id) {
    useEffect(() => {
      //
      setLoading(true);
      getSingleUser(id, setFormdata, setCheckedItems);
    }, [id]);
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    data.append("image", formData.image);
    const payload = {
      id,
      firstName: data.get("firstName"),
      lastName: data.get("lastName"),
      username: data.get("username"),
      email: data.get("email"),
      phone: data.get("phone"),
      role: data.get("role"),
      image: data.get("image"),
      password: data.get("password"),
      password_confirmation: data.get("password_confirmation"),
    };

    //api call to register
    if (formData.id) {
      payload.id = formData.id;
      payload.permissionId = formData.permissionId;
      updateUsers(payload, setNotification, checkedItems, setErrors);
    } else {
      register(payload, setErrors, setNotification, checkedItems);
    }
    
    setErrors({});
  };

  return (
    <Box m="5px">
      <Header
        title={
          formData.id
            ? `Update User:${formData.firstName} ${formData.lastName}`
            : "Register User"
        }
        subtitle="welcome to register users"
      />
      <Grid container spacing={1}>
        <Grid item xs={12} sm={4} height="100vh">
          <Paper
            variant="outlined"
            sx={{
              p: { xs: 2, md: 3 },
              color: colors.grey[100],
              backgroundColor: colors.primary[400],
            }}
          >
            <Typography variant="h2">
              {id ? <>Update</> : <>Register</>}
            </Typography>
            <Box
              component="form"
              onSubmit={handleSubmit}
              sx={{ mt: 1 }}
              encType="multipart/form-data"
            >
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    error={!errors.firstName == ""}
                    id="firstName"
                    margin="normal"
                    fullWidth
                    variant="standard"
                    label="First Name"
                    name="firstName"
                    autoComplete="firstName"
                    InputLabelProps={{
                      color: "success",
                    }}
                    value={formData.firstName}
                    onChange={handleChange}
                    size="small"
                    helperText={errors.firstName ? errors.firstName[0] : null}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    error={!errors.lastName == ""}
                    id="lastName"
                    margin="normal"
                    fullWidth
                    label="Last Name"
                    name="lastName"
                    autoComplete="lastName"
                    variant="standard"
                    InputLabelProps={{
                      color: "success",
                    }}
                    value={formData.lastName}
                    onChange={handleChange}
                    size="small"
                    helperText={errors.lastName ? errors.lastName[0] : null}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    error={!errors.username == ""}
                    margin="normal"
                    fullWidth
                    label="Username"
                    name="username"
                    variant="standard"
                    InputLabelProps={{
                      color: "success",
                    }}
                    type="text"
                    size="small"
                    value={formData.username}
                    onChange={handleChange}
                    helperText={errors.username ? errors.username[0] : null}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    error={!errors.email == ""}
                    id="email"
                    margin="normal"
                    fullWidth
                    label="Email Address"
                    name="email"
                    variant="standard"
                    autoComplete="email"
                    InputLabelProps={{
                      color: "success",
                    }}
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    size="small"
                    helperText={errors.email ? errors.email[0] : null}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    error={!errors.phone == ""}
                    id="phone"
                    margin="normal"
                    fullWidth
                    label="Phone Number"
                    name="phone"
                    autoComplete="phone"
                    variant="standard"
                    InputLabelProps={{
                      color: "success",
                    }}
                    value={formData.phone}
                    onChange={handleChange}
                    size="small"
                    helperText={errors.phone ? errors.phone[0] : null}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    error={!errors.role == ""}
                    id="role"
                    margin="normal"
                    fullWidth
                    label="Role"
                    name="role"
                    select
                    autoComplete="role"
                    variant="standard"
                    InputLabelProps={{
                      color: "success",
                    }}
                    value={formData.role}
                    onChange={handleChange}
                    size="small"
                    defaultValue={roles["none"]}
                    helperText={errors.role ? errors.role[0] : null}
                  >
                    {roles.map((role) => (
                      <MenuItem key={role.value} value={role.value}>
                        {role.label}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    error={!errors.image == ""}
                    margin="normal"
                    fullWidth
                    name="image"
                    label="Image"
                    type="file"
                    inputProps={{ accept: "image/*" }}
                    id="image"
                    autoComplete="select image"
                    variant="standard"
                    InputLabelProps={{
                      color: "success",
                    }}
                    size="small"
                    onChange={handleChange}
                    helperText={errors.image ? errors.image[0] : null}
                  />
                </Grid>
                {!id && (
                  <>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        error={!errors.password == ""}
                        margin="normal"
                        fullWidth
                        name="password"
                        label="password"
                        type="password"
                        id="password"
                        autoComplete="current-password"
                        variant="standard"
                        InputLabelProps={{
                          color: "success",
                        }}
                        size="small"
                        helperText={errors.password ? errors.password[0] : null}
                        onChange={handleChange}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        margin="normal"
                        fullWidth
                        name="password_confirmation"
                        label="password_confirmation"
                        type="password"
                        id="password_confirmation"
                        autoComplete="current-password_confirmation"
                        variant="standard"
                        InputLabelProps={{
                          color: "success",
                        }}
                        size="small"
                        onChange={handleChange}
                      />
                    </Grid>
                  </>
                )}
              </Grid>
              <Button
                fullWidth
                type="submit"
                variant="contained"
                sx={{ mt: 2, py: 2 }}
                color="success"
                size="small"
              >
                {id && <>UPDATE</>}
                {!id && <>REGISTER</>}
              </Button>
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={8}>
          <Paper
            variant="outlined"
            sx={{
              p: { xs: 2, md: 3 },
              color: colors.grey[100],
              backgroundColor: colors.primary[400],
            }}
          >
            <Typography variant="h4" align="left" alignContent="start">
              Give permission to a certain pages
              <label>
                <Checkbox
                  color="success"
                  size="small"
                  checked={checkAll}
                  onChange={handleCheckAllChange}
                />
                Select All
              </label>
            </Typography>
            <Grid container spacing={1}>
              {getRoutes &&
                Object.entries(groupRoutes(getRoutes)).map(
                  ([groupName, routes]) =>
                    routes.length > 0 && (
                      <Grid item xs={12} sm={4} key={groupName}>
                        <Typography variant="h6">{groupName}</Typography>
                        {routes.map((route, index) => (
                          <div key={index}>
                            <label>
                              <Checkbox
                                color="success"
                                size="small"
                                checked={checkedItems.includes(route.path)}
                                onChange={(event) =>
                                  handleCheckboxChange(event, route.path)
                                }
                              />
                              {route.element}
                            </label>
                          </div>
                        ))}
                      </Grid>
                    )
                )}

              {!getRoutes && (
                <Grid item xs={12} sm={12}>
                  <Typography align="center" sx={{ color: "red", mt: 2 }}>
                    You need To Select User Role Inorder to give permissions to
                    Routes
                  </Typography>
                </Grid>
              )}
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}

export default Register;
