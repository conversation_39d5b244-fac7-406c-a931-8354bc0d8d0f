import React from "react";
import { Box, TextField } from "@mui/material";
import Header from "../../../components/Header";
function TaxableEarnings() {
  return (
    <Box>
      <Header heading="h6" title="Taxable Earnings" />
      <TextField
        label="Basic Salary ((BS X WD ) + PRS)"
        value="0"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
      <TextField
        label="Over Time (BS X OT)"
        value="0"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
      <TextField
        label="Holiday (BS X HD)"
        value="0"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
      <TextField
        label="Additional"
        value="0"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
      <TextField
        label="Taxable Transport Allowance(TA-TD)"
        value="0"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
      <TextField
        label="Housing Allowance"
        value="0"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
      <TextField
        label="Other Allowance"
        value="0"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
      <TextField
        label="Dsa Allowance"
        value="0"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
    </Box>
  );
}

export default TaxableEarnings;
