<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('company_name')->nullable();
            $table->string('company_code')->nullable();
            $table->string('city')->nullable();
            $table->string('sub_city')->nullable();
            $table->string('street_address1')->nullable();
            $table->string('street_address2')->nullable();
            $table->string('kebele')->nullable();
            $table->string('house_no')->nullable();
            $table->string('tel')->nullable();
            $table->string('residence')->nullable();
            $table->date('start_date')->format('Y-m-d')->nullable();
            $table->date('end_date')->format('Y-m-d')->nullable();
            $table->integer('sh_id')->nullable();
            $table->integer('guard_no')->nullable();
            $table->integer('leader_no')->nullable();
            $table->float('guard_salary')->nullable();
            $table->float('leader_salary')->nullable();
            $table->float('guard_lsalary')->nullable();
            $table->float('leader_lsalary')->nullable();
            $table->float('guard_transport')->nullable();
            $table->float('guard_transport_rate')->nullable();
            $table->string('rate_type')->nullable();
            $table->string('email')->nullable();
            $table->string('location')->nullable();
            $table->string('jv_number')->nullable();
            $table->float('total_working_hours')->nullable();
            $table->string('tax_center')->nullable();
            $table->string('user_id')->nullable();
            $table->timestamps();
        });     
        $path=Storage::path('client.json');
        $data=file_get_contents($path);
        $decode=json_decode($data,true);

        DB::table('clients')->insert($decode);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('clients');
    }
};
