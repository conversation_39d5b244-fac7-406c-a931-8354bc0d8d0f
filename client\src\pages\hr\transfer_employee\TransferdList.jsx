import {
  Box,
  CircularProgress,
  Typography,
  useTheme,
  Button,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import api from "../../../api/config/axiosConfig";

import { tokens } from "../../../utils/theme";

function TransferdList() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { data: transfered, isLoading } = useQuery(
    ["transfered"],
    () =>
      api.get(`transfer`).then(async ({ data }) => {
        return data.map((row, index) => ({ ...row, id: index + 1 }));
      }),
    {
      refetchOnWindowFocus: false,
      staleTime: 60000,
    }
  );

  const getRowId = (row) => {
    return `${transfered.indexOf(row)}`;
  };
  const columns = [
    {
      field: "id",
      headerName: "ID",
    },
    {
      field: "first_name",
      headerName: "FirstName",
      flex: 1,
    },
    {
      field: "f_company_name",
      headerName: "Transferd From",
      flex: 1,
    },
    {
      field: "t_company_name",
      headerName: "Transfered To",
      flex: 1,
    },
    {
      field: "effective_date",
      headerName: "Effective Date",
      flex: 1,
    },
    {
      field: "reason",
      headerName: "Reason",
      flex: 1,
    },
  ];
  if (isLoading) {
    return <CircularProgress />;
  }
  return (
    <Box
      height="75vh"
      sx={{
        "& .MuiDataGrid-root": {
          border: "none",
        },
        "& .MuiDataGrid-cell": {
          borderBottom: "none",
        },
        "& .name-column--cell": {
          color: colors.greenAccent[300],
        },
        "& .MuiDataGrid-columnHeaders": {
          backgroundColor: colors.blueAccent[700],
          borderBottom: "none",
        },
        "& .MuiDataGrid-virtualScroller": {
          backgroundColor: colors.primary[400],
        },
        "& .MuiDataGrid-footerContainer": {
          borderTop: "none",
          backgroundColor: colors.blueAccent[700],
        },
        "& .MuiCheckBox-root": {
          color: `${colors.greenAccent[200]}!important`,
        },
        "& .MuiDataGrid-toolbarContainer .MuiButton-text": {
          color: `${colors.grey[100]}!important`,
        },
      }}
    >
      <DataGrid columns={columns} rows={transfered} getRowId={getRowId} />
    </Box>
  );
}

export default TransferdList;
