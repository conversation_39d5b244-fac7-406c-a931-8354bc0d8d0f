<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateEmpStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'emp_basic_id'=>'required',
            'finggure_print' => 'required',
            'holiday' => 'required',
            'credit_association' => 'required',
            'sport_association' => 'required',
            'labor_union'=>'required'
        ];
    }
}
