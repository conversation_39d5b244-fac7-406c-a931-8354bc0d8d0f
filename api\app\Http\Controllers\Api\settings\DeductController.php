<?php

namespace App\Http\Controllers\Api\settings;

use App\Http\Controllers\Controller;
use App\Models\settings\Deduct;
use Illuminate\Http\Request;

class DeductController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(Deduct::latest()->first());
    }



    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = Deduct::create([
            'social_fund' => $request->input('social_fund'),
        ]);

        return response($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\settings\Deduct  $deduct
     * @return \Illuminate\Http\Response
     */
    public function show(Deduct $deduct)
    {
        return response($deduct);
    }



    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\settings\Deduct  $deduct
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // var_dump($request);
        $deduct=Deduct::find($id);
        $deduct->update([
            'social_fund' => $request->social_fund,
        ]);

        return response($deduct);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\settings\Deduct  $deduct
     * @return \Illuminate\Http\Response
     */
    public function destroy(Deduct $deduct)
    {
        $deduct->delete();

        return response('', 204);
    }
}
