<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreContractFollowUpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'client_id' => 'required',
            'contract_schedule' => 'required',
            'start_date' => 'date|required',
            'end_date' => 'date|required',
            'contract_document' => 'required|mimes:doc,pdf,jpg,png|max:10240',
            'reminder' => 'required',
            'message' => '',
            'amount' => '',
            'user_id' => ''
        ];
    }
}
