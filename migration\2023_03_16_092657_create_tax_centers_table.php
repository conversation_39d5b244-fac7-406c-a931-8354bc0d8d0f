<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tax_centers', function (Blueprint $table) {
            $table->id();
            $table->string('tax_center_name');
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
        $path = Storage::path('tax_center.json');
        $data = file_get_contents($path);
        $decoded = json_decode($data, true);
        DB::table('tax_centers')->insert($decoded);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tax_centers');
    }
};
