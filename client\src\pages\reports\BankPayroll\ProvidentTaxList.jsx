import { useQuery } from "@tanstack/react-query";
import React from "react";
import { useLocation } from "react-router-dom";
import api from "../../../api/config/axiosConfig";
import { Box } from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import _CircularProgress from "../../../components/_CircularProgress";
import CustomToolBar from "../../../helper/CustomToolBar";
import Header from "../../../components/Header";
import { useState } from "react";
import {pensionTaxMessage}from "../formattedMessage"
import CustomFooterTable from "./CustomFooterTable";
const ProvidentTaxList = () => {
    const location = useLocation();
    const recivedData = location.state;
    const {
      data: response,
      isLoading,
      isFetched,
    } = useQuery(["incometax"], () =>
      api.get("incometax", { params: recivedData }).then(({ data }) => {
       return data.map((item,index)=>({
          ...item,
          id:index+1
        }))
      })
    );
    const columns = [
      {
        field: "id",
        headerName: "ID",
      },
      {
        field: "fullname",
        headerName: "fullName",
        flex: 1.5,
      },
      {
        field: "tin_no",
        headerName: "Tin Number",
        flex: 1,
      },
      {
        field: "start_date",
        headerName: "Empoyeed Date",
        flex: 1,
      },
      {
        field: "basic_salary",
        headerName: "Basic_Salary",
        flex: 1,
      },
      {
        field: "provident_emp",
        headerName: "employee provident Tax",
        flex: 1,
      },
      {
        field: "provident_sns",
        headerName: "company Provident Tax",
        flex: 1,
      },
      {
        field: "provident_total",
        headerName: "total Provident Tax",
        flex: 1,
      },
      {
        field: "tax_center",
        headerName: "Tax Center",
        flex: 1,
      },
      {
        field: "company_name",
        headerName: "Company",
        flex: 1,
      },
      {
        field: "company_code",
        headerName: "Company Code",
        flex: 1,
      },
      
    ];
    return (
      <Box margin="10px">

        <Header title="PROVIDENT TAX REPORT" subtitle={pensionTaxMessage(recivedData.firstDate,recivedData.lastDate,recivedData.taxCenter)} />
      {isLoading && <_CircularProgress />}
      {isFetched && (
        <DataGrid
          columns={columns}
          rows={response}
          autoHeight
          components={{ Toolbar: CustomToolBar,
          Footer:()=><CustomFooterTable rows={response} columns={columns}/> }}
        />
      )}
    </Box>
    )
}

export default ProvidentTaxList