<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\settings\UserPermission;
use Illuminate\Http\Request;

class UserPermissionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(UserPermission::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
           $response= UserPermission::create([
                'user_id' => $request->input('user_id'),
                'routes' => $request->input('routes'),
                'roles' => $request->input('roles'),
            ]);
            return response($response,201);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\settings\UserPermission  $userPermission
     * @return \Illuminate\Http\Response
     */
    public function show(UserPermission $userPermission)
    {
        
        return response($userPermission);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\settings\UserPermission  $userPermission
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request,  $id)
    {
        $userPermission=UserPermission::find($id);
        $userPermission->update([
            'user_id' => $request->input('user_id'),
            'routes' => $request->input('routes'),
            'roles' => $request->input('roles'),
        ]);

        return response($userPermission);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\settings\UserPermission  $userPermission
     * @return \Illuminate\Http\Response
     */
    public function destroy(UserPermission $userPermission)
    {
        $userPermission->delete();

        return response('',204);
    }
}
