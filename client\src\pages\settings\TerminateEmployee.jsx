import { Box, Button, Grid, MenuItem, TextField } from '@mui/material';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import api from '../../api/config/axiosConfig';
import {
  useClientData,
  useTerminationReasons,
} from '../../api/userApi/clinetHook';
import SearchBar from '../../components/SearchBar';
import { useStateContext } from '../../context/ContextProvider';
function TerminateEmployee() {
  const useQueryClinet = useQueryClient();
  const { setNotification } = useStateContext();
  const [formData, setFormData] = useState({
    emp_basic_id: '',
    client_id: '',
    termination_date: '',
    reason: '',
    ref_no: '',
  });

  const { data: assignedEmployee } = useQuery(
    ['assign', formData.client_id],
    () =>
      api
        .get(`assign/client/${formData.client_id}`)
        .then(async ({ data }) => await data.data),
    {
      enabled: !!formData.client_id,
    }
  );

  const { data: termination_reasons } = useTerminationReasons();

  const handleClientSelect = ({ value }) => {
    setFormData({
      ...formData,
      client_id: value.id,
    });
  };
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const terminate = useMutation((data) => api.post('terminate', data), {
    onSuccess: () => {
      setNotification('Employee is terminated successfully');
      useQueryClinet.invalidateQueries(['assign', 'client']);
    },
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    terminate.mutate(formData);
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={4}>
          <SearchBar onClientSelect={handleClientSelect} />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            margin="normal"
            label="Select Employee To  Terminate"
            name="emp_basic_id"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={formData.emp_basic_id}
          >
            {assignedEmployee ? (
              assignedEmployee.map((val, index) => (
                <MenuItem key={index} value={val.id}>
                  {val.first_name + ' ' + val.middle_name + ' ' + val.last_name}
                </MenuItem>
              ))
            ) : (
              <MenuItem value={''}>select employee</MenuItem>
            )}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            margin="normal"
            label="Effective Date"
            name="termination_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={formData.termination_date}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            margin="normal"
            label="Reason"
            name="reason"
            fullWidth
            variant="standard"
            select
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={formData.reason}
          >
            {termination_reasons ? (
              termination_reasons.map((reason, index) => (
                <MenuItem key={index} value={reason.id}>
                  {reason.termination_reason}
                </MenuItem>
              ))
            ) : (
              <MenuItem>Select Termination Reason</MenuItem>
            )}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            margin="normal"
            label="Reference Number"
            name="ref_no"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={formData.ref_no}
          />
        </Grid>
      </Grid>
      <Button
        type="submit"
        variant="contained"
        sx={{ mt: 3, ml: 1 }}
        color="success"
      >
        submit
      </Button>
    </Box>
  );
}

export default TerminateEmployee;
