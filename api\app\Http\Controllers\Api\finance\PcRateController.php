<?php

namespace App\Http\Controllers\Api\finance;

use App\Http\Controllers\Controller;
use App\Models\finance\PcRate;
use Illuminate\Http\Request;

class PcRateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(PcRate::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $total = $request->input('no_pc') * $request->input('pc_rate_amount');
        $data = PcRate::create([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'no_pc' => $request->input('no_pc'),
            'total' => $total,
            'pc_type' => $request->input('pc_type'),
            'salary' => $request->input('pc_rate_amount'),
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'user_id' => $request->input('user_id'),
        ]);

        return response($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\finance\PcRate  $pcRate
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $pcRate = PcRate::where('emp_basic_id', $id)->where('paid', 0)->get();
        return response($pcRate);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\finance\PcRate  $pcRate
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $total = $request->input('no_pc') * $request->input('pc_rate_amount');
        $pcRate = PcRate::find($id);
        $pcRate->update([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'no_pc' => $request->input('no_pc'),
            'total' => $total,
            'pc_type' => $request->input('pc_type'),
            'salary' => $request->input('pc_rate_amount'),
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'user_id' => $request->input('user_id'),
        ]);

        return response($pcRate);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\finance\PcRate  $pcRate
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $pcrate = PcRate::find($id);
        $pcrate->delete();

        return response('', 204);
    }
}
