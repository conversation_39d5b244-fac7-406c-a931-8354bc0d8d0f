import { useQuery } from "@tanstack/react-query";
import React, { useRef } from "react";
import { useLocation } from "react-router-dom";
import api from "../../../api/config/axiosConfig";
import { PaySlipTable } from "./PaySlipTable";
import { Button, CircularProgress, Grid } from "@mui/material";
import { useMemo } from "react";

function PaymentSlipList() {
  const location = useLocation();
  const object = location.state;
  const { data: slip, isLoading } = useQuery(["slip", object.client_id], () =>
    api.get(`getslip/${object.client_id}`).then(({ data }) => data)
  );
  const { data: date } = useQuery(["payroll_date"], () =>
    api.get("payroll_date").then(({ data }) => data.start_date)
  );

  const memoizedSlip = useMemo(() => slip, [slip]);

  const printableContentRef = useRef(null);

  const handlePrint = () => {
    const printableContent = printableContentRef.current;
    if (printableContent) {
      const originalContents = document.body.innerHTML;
      const printContents = printableContent.innerHTML;
      document.body.innerHTML = printContents;
      window.print();
      document.body.innerHTML = originalContents;
    }
  };

  return (
    <div style={{ maxWidth: "60%", margin: "0 auto" }}>
      <div>
        <Grid spacing={2} container>
          {isLoading ? (
            <CircularProgress />
          ) : (
            <div ref={printableContentRef}>
              {memoizedSlip.map((employee) => (
                <Grid item xs={12} sm={12} key={employee.emp_basic_id}>
                  <PaySlipTable employee={employee} date={date} />
                </Grid>
              ))}
            </div>
          )}
        </Grid>
        <div style={{ textAlign: "center", marginTop: "20px" }}>
          <Button
            variant="contained"
            color="secondary"
            sx={{ width: "140px" }}
            onClick={handlePrint}
          >
            Print
          </Button>
        </div>
      </div>
    </div>
  );
}

export default PaymentSlipList;
