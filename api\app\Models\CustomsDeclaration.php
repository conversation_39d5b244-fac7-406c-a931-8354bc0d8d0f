<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomsDeclaration extends Model
{
    use HasFactory;

    protected $fillable = [
        'export_order_id',
        'declaration_number',
        'hs_code',
        'export_license_number',
        'date_filed',
        'pdf_document_url',
    ];

    public function exportOrder()
    {
        return $this->belongsTo(ExportOrder::class);
    }
}
