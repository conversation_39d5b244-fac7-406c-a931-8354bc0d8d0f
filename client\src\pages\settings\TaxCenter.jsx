import { AddOutlined } from '@mui/icons-material';
import {
  Button,
  Typography,
  useTheme,
  Box,
  IconButton,
  Grid,
  TextField,
  MenuItem,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import api from '../../api/config/axiosConfig';
import Header from '../../components/Header';
import { useStateContext } from '../../context/ContextProvider';
import { tokens } from '../../utils/theme';
import _CircularProgress from '../../components/_CircularProgress';
function TaxCenter() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const queryClient = useQueryClient();
  const [clicked, setClicked] = useState(false);
  const [selectedRow, setSelectedRow] = useState({});
  const { setNotification, errors, setErrors } = useStateContext();

  const regionList = [
    'Addis Ababa',
    'Afar',
    'Amhara',
    'Benishangul-Gumuz',
    'Dire Dawa',
    'Gambela',
    'Harari',
    'Oromia',
    'Sidama',
    'Somali',
    'South West Ethiopia Peoples',
    'Tigray',
    'Central Ethiopia',
    'South Ethiopia',
  ];

  const { data: tax_center, isLoading } = useQuery(
    ['tax_center'],
    async () =>
      await api.get('tax_center').then(({ data }) => {
        return data;
      })
  );

  const columns = [
    {
      field: 'id',
      headerName: 'Id',
    },
    {
      field: 'tax_center_name',
      headerName: 'Tax Center',
      flex: 1,
    },
    {
      field: 'region',
      headerName: 'Region',
      flex: 1,
    },
    {
      field: 'edit',
      headerName: 'Edit',
      flex: 1,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          setClicked(true);
          setSelectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: 'delete',
      headerName: 'Delete',
      flex: 1,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deletTaxCenter = useMutation(
          (id) => api.delete(`tax_center/${id}`),
          {
            onSuccess: () => {
              setNotification('Tax Center Deleted Successfully ');
            },
            onSettled: () => {
              queryClient.invalidateQueries(['tax_center']);
            },
          }
        );
        const deleteTaxCenter = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              'Are you sure you want to delete the selected  tax center?'
            )
          ) {
            return;
          }
          deletTaxCenter.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deleteTaxCenter}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];

  const handleClick = (e) => {
    e.preventDefault();
    setClicked(true);
  };

  const handleChange = (e) => {
    setSelectedRow({
      ...selectedRow,
      [e.target.name]: e.target.value,
    });
  };

  const postTaxCenter = useMutation((data) => api.post('tax_center', data), {
    onSuccess: () => {
      queryClient.invalidateQueries(['tax_center']);
      setNotification('tax_center created successfully');
      setClicked(false);
    },
    onError: (err) => {
      setClicked(true);
      if (err.response && err.response.status === 422) {
        if (err.response.data.errors) {
          setErrors(err.response.data.errors);
        }
      }
    },
  });
  const onSubmit = (e) => {
    e.preventDefault();
    const payload = {
      tax_center_name: selectedRow.tax_center_name,
      region: selectedRow.region,
    };
    if (selectedRow.id) {
      api
        .put(`tax_center/${selectedRow.id}`, payload)
        .then(() => {
          setNotification('tax center updated successfully');
          queryClient.invalidateQueries(['tax_center']);
          setClicked(false);
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
          setClicked(true);
        });
    } else {
      postTaxCenter.mutate(payload);
    }
  };

  return (
    <Box m="10px">
      <Header title="Tax Center Table" subtitle="List of tax centers" />
      <Box display="flex" justifyContent="center" alignItems="center">
        <IconButton
          sx={{
            boxShadow: '2px 2px 4px rgba(0,0,0,0.25)',
            background: colors.primary[400],
          }}
          onClick={handleClick}
        >
          <AddOutlined color={colors.grey[100]} />
        </IconButton>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={clicked ? 9 : 12}>
          <Box
            m="10px 0 0 0"
            height="67vh"
            sx={{
              '& .MuiDataGrid-root': {
                border: 'none',
              },
              '& .MuiDataGrid-cell': {
                borderBottom: 'none',
              },
              '& .name-column--cell': {
                color: colors.greenAccent[300],
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: colors.blueAccent[700],
                borderBottom: 'none',
              },
              '& .MuiDataGrid-virtualScroller': {
                backgroundColor: colors.primary[400],
              },
              '& .MuiDataGrid-footerContainer': {
                borderTop: 'none',
                backgroundColor: colors.blueAccent[700],
              },
              '& .MuiCheckBox-root': {
                color: `${colors.greenAccent[200]}!important`,
              },
              '& .MuiDataGrid-toolbarContainer .MuiButton-text': {
                color: `${colors.grey[100]}!important`,
              },
            }}
          >
            {isLoading ? (
              <_CircularProgress />
            ) : (
              <DataGrid columns={columns} rows={tax_center} />
            )}
          </Box>
        </Grid>

        {clicked ? (
          <Grid item xs={12} sm={3} component="form" onSubmit={onSubmit}>
            <Typography variant="h5" color={colors.grey[100]} align="center">
              {selectedRow.id ? 'Edit Tax Center' : 'Add Tax Center'}
            </Typography>
            <TextField
              error={!errors.tax_center_name == ''}
              id="tax_name"
              margin="normal"
              fullWidth
              label="Tax Center"
              name="tax_center_name"
              InputLabelProps={{
                color: 'success',
              }}
              value={selectedRow.tax_center_name || ''}
              onChange={handleChange}
              helperText={
                errors.tax_center_name ? errors.tax_center_name[0] : null
              }
            />

            <TextField
              select
              error={!errors.region == ''}
              id="region"
              margin="normal"
              fullWidth
              label="Region"
              name="region"
              InputLabelProps={{
                color: 'success',
              }}
              value={selectedRow.region || ''}
              onChange={handleChange}
              helperText={errors.region ? errors.region[0] : null}
            >
              {regionList.map((region) => (
                <MenuItem key={region} value={region}>
                  {region}
                </MenuItem>
              ))}
            </TextField>

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 2, py: 2 }}
              color="success"
              size="small"
            >
              submit
            </Button>

            <Button
              onClick={() => {
                setClicked(false);
                setSelectedRow({});
              }}
              fullWidth
              variant="contained"
              sx={{ mt: 2, py: 2 }}
              color="error"
              size="small"
            >
              cancel
            </Button>
          </Grid>
        ) : undefined}
      </Grid>
    </Box>
  );
}

export default TaxCenter;
