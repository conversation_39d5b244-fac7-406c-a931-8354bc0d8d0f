<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Farmer extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'cooperative_name',
        'location',
        'phone',
    ];

    public function purchasesFromFarmers()
    {
        return $this->hasMany(PurchaseFromFarmer::class);
    }
}
