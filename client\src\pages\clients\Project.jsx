import { Box, Grid } from "@mui/material";
import React from "react";
import Header from "../../components/Header";
import LinkCard from "../../components/LinkCard";

function Project() {
  return (
    <Box m={2}>
      <Header
        title="Clients Page"
        subtitle="a place to register,update,delete set client-deduct for a project"
      />
      <Grid container spacing={2}>
        <Grid item xs={12} sm={4}>
          <LinkCard
            title="List Of Projects"
            subtitle="list of projects in a table with thier detail information"
            to="/clients/list"
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <LinkCard
            title="Register New Project"
            subtitle="Register New Project Here "
            to="/clients/new"
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <LinkCard
            title="Set Client Deductable"
            subtitle="set Project Deductable here including labor unioun ,sport and creadit association"
            to="/clients/deduct"
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <LinkCard
            title="Create payment follow-up"
            subtitle="create payment follow-up for a projects here "
            to="/clients/payment-follow-up"
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <LinkCard
            title="Create contract follow-up"
            subtitle="create contract follow-up for projects in here"
            to="/clients/contract-follow-up"
          />
        </Grid>
      </Grid>
    </Box>
  );
}

export default Project;
