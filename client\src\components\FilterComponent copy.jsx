import {
  Box,
  Checkbox,
  FormControl,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  ListItemText,
  Grid,
  FormControlLabel,
} from '@mui/material';

import React, { useState } from 'react';
import DateDiffrenceComponent from './DateDiffrenceComponent';
import SearchBar from './SearchBar';
import CompanySearch from './CompanySearch';
import YearDropDown from './YearDropDown';
import TaxCenterDropDown from './TaxCenterDropDown';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;

const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

const FilterComponent = ({
  passFromDate,
  passToDate,
  passProjectId,
  passYear,
  passTaxCenter,
  passCompany,
  passCurrent,
}) => {
  const [selectedComponents, setSelectedComponents] = useState([]);
  const [isCurrentDateChecked, setIsCurrentDateChecked] = useState(false);


  const handleChange = (event) => {
    const {
      target: { value },
    } = event;
    setSelectedComponents(value);
  };

  const handleDateChanges = (fromDate, toDate) => {
    passFromDate(fromDate);
    passToDate(toDate);
  };

  const handleClientSelect = ({ value, clicked }) => {
    passProjectId(value.id);
  };

  const handleTaxSelect = ({ value, clicked }) => {
    console.log(value);
    passTaxCenter(value);
  };

  const handleSelectedYear = (year) => {
    console.log(year);
    passYear(year);
  };

  const handleCompanySelect = ({ value, clicked }) => {
    console.log(value);
    passCompany(value);
  };

  const handleCurrent = (event) => {
    const { checked } = event.target;
    setIsCurrentDateChecked(checked);
    passCurrent(checked ? 1 : 0);
    console.log(checked);
  };

  
  const componentList = [
    {
      name: 'Site',
      component: <SearchBar onClientSelect={handleClientSelect} />,
    },
    {
      name: 'Company',
      component: <CompanySearch onCompanySelect={handleCompanySelect} />,
    },
    {
      name: 'DateDiffrence',
      component: (
        <DateDiffrenceComponent
          labelOne="fromDate"
          labelTwo="toDate"
          onDateChange={handleDateChanges}
        />
      ),
    },
    {
      name: 'fullYear',
      component: <YearDropDown onSelectedYear={handleSelectedYear} />,
    },
    {
      name: 'Tax Center',
      component: <TaxCenterDropDown onSelectedTaxCenter={handleTaxSelect} />,
    },
    {
      name: 'Current Date',
      component: (
        <FormControlLabel
          control={
            <Checkbox
              checked={isCurrentDateChecked}
              onChange={handleCurrent}
            />
          }
          label="Current Date"
        />
      ),
    }
  ];

  return (
    <div style={{ display: 'flex' }}>
      <div>
        <FormControl sx={{ m: 1, width: 300 }}>
          <InputLabel id="demo-multiple-checkbox-label">
            Select Components
          </InputLabel>
          <Select
            labelId="demo-multiple-checkbox-label"
            id="demo-multiple-checkbox"
            multiple
            value={selectedComponents}
            onChange={handleChange}
            input={<OutlinedInput label="Select Filtering Methods" />}
            renderValue={(selected) => selected.join(', ')}
            MenuProps={MenuProps}
          >
            {componentList.map((component) => (
              <MenuItem key={component.name} value={component.name}>
                <Checkbox
                  checked={selectedComponents.indexOf(component.name) > -1}
                />
                <ListItemText primary={component.name} />
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </div>
      <Grid container spacing={1}>
        {componentList.map((component) => {
          if (selectedComponents.includes(component.name)) {
            return (
              <Grid item xs={12} sm={6} md={4} key={component.name}>
                {component.component}
              </Grid>
            );
          }
          return null;
        })}
      </Grid>
    </div>
  );
};

export default FilterComponent;
