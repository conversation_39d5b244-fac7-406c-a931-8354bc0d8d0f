<?php

namespace App\Http\Controllers\Api\Report;

use App\Http\Controllers\Controller;
use App\Models\Assign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class EmployeesReportController extends Controller
{
    public function query()
    {
        $query = Assign::with(['empBasic', 'client', 'empBasic.empDetail'])
            ->whereHas('EmpBasic', function ($query) {
                $query->where('termination_status', 0);
            });
        return $query;
    }

    public function getEmployeesReport(Request $request)
    {
        $startDate = $request->input('firstDate');
        $endDate = $request->input('lastDate');
        $clientId = $request->input('clientId');
        $status = $request->input('status');
        $region = $request->input('region');
        $gender = $request->input('gender');
        $year = $request->input('year');
        $bankCode = $request->input('bankCode');
        $nextYear = Carbon::parse($year)->addYear()->format('Y-m-d');

        $count = 0;

        $query = DB::table('emp_basics')
            ->leftJoin('emp_details', 'emp_details.emp_basic_id', '=', 'emp_basics.id')
            ->leftJoin('assigns', 'assigns.emp_basic_id', '=', 'emp_basics.id')
            ->leftJoin('clients', 'assigns.client_id', '=', 'clients.id')
            ->leftJoin('emp_banks', 'emp_banks.emp_basic_id', '=', 'emp_basics.id')
            ->select(
                'emp_basics.id as emp_id',
                'emp_basics.first_name',
                'emp_basics.middle_name',
                'emp_basics.last_name',
                DB::raw("CONCAT(emp_basics.first_name,' ', COALESCE(emp_basics.middle_name, ''), ' ', emp_basics.last_name) as fullname"),
                'emp_basics.gender',
                'emp_basics.*',
                'emp_details.address',
                'emp_details.city',
                'emp_details.sub_city',
                'emp_details.kebele',
                'emp_details.phone',
                'clients.company_name',
                //'assigns.position',
                //'assigns.assign_date',
                //'assigns.client_id',
            )->where('emp_basics.termination_status', 0);

        if ($status != 1) {
            if ($startDate && $endDate) {
                $query->whereBetween('emp_basics.start_date', [$startDate, $endDate]);
            }
            if ($clientId) {
                $query->where('clients.id', $clientId);
            }
            if ($region) {
                $query->where('empDetail.address', 'like', "%$region%");
            }
            if ($gender) {
                $query->where('emp_basics.gender', '=', "$gender");
            }
            if ($year) {
                $query->whereBetween('emp_basics.start_date', [$year, $nextYear]);
            }
            if ($bankCode) {
                $query->where('emp_banks.bank_code', $bankCode);
            }
        }

        $results = $query->get();

        return response($results);
    }
}
