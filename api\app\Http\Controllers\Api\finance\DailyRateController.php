<?php

namespace App\Http\Controllers\Api\finance;

use App\Http\Controllers\Controller;
use App\Models\finance\DailyRate;
use Illuminate\Http\Request;

class DailyRateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(DailyRate::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = DailyRate::create([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'no_days' => $request->input('no_days'),
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'user_id' => $request->input('user_id')
        ]);

        return response($data, 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\finance\DailyRate  $dailyRate
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
       $dailyRate=DailyRate::where('emp_basic_id',$id)->where('paid',0)->get();
        return response($dailyRate);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\finance\DailyRate  $dailyRate
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, DailyRate $dailyRate)
    {
        $dailyRate->update([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'no_days' => $request->input('no_days'),
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'user_id' => $request->input('user_id')
        ]);

        return response($dailyRate);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\finance\DailyRate  $dailyRate
     * @return \Illuminate\Http\Response
     */
    public function destroy(DailyRate $dailyRate)
    {

        $dailyRate->delete();

        return response('', 204);
    }
}
