<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\finance\User;
class ChangePasswordController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }
    public function changePassword(Request $request)
    {
      
        $request->validate([
            'current_password' => "required",
            'password' => "required|confirmed|min:8",
        ]);
        $user = Auth::user();
        $currentPassword = $request->input('current_password');
        if (Hash::check($currentPassword, $user->password)) {
            $user->password = Hash::make($request->input('password'));
            $user->save();
            return response($user);
        }
         else{
            return response([
                'message' => 'Your old Password is Not correct'
            ],422);
        }
    }

    public function resetPassword(Request $request,$userId){

        $request->validate([
            'password'=>'required|min:8|confirmed'
        ]);

        $user=\App\Models\User::findOrFail($userId);

        $user->password=bcrypt($request->input('password'));
        $user->save();
    }
}
