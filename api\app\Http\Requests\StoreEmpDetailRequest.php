<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreEmpDetailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'emp_basic_id'=>'required',
            'address'=>'required',
            'city'=>'required',
            'sub_city'=>'required',
            'kebele'=>'required',
            'house_no'=>'required',
            'country'=>'required',
            'phone'=>'required',
            'photo_url'=>'required|image|mimes:png,jpg,svg|max:2048',
        ];
    }
}
