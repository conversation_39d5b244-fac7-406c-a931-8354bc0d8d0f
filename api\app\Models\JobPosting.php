<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JobPosting extends Model
{
    use HasFactory;

    protected $table = 'job_postings';

    protected $fillable = [
        'hire_requisition_id',
        'posting_date',
        'closing_date',
        'description',
        'online_posting_url',
    ];

    public function hireRequisition()
    {
        return $this->belongsTo(HireRequisition::class);
    }
}