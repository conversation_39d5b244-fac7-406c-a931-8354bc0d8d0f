import React from "react";
import {
  Card,
  CardActionArea,
  CardContent,
  Typography,
  useTheme,
} from "@mui/material";
import { Link } from "react-router-dom";
import { tokens } from "../utils/theme";

const LinkCard = ({ title, subtitle, to }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  return (
    <Card
      style={{
        color: colors.grey,
        background: colors.primary[400],
        padding: 5,
      }}
      variant="outlined"
    >
      <CardActionArea component={Link} to={to}>
        <CardContent>
          <Typography gutterBottom variant="h5" component="div">
            {title}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        </CardContent>
      </CardActionArea>
    </Card>
  );
};

export default LinkCard;
