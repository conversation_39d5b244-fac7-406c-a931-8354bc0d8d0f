import { useTheme, CircularProgress } from "@mui/material";
import { Box } from "@mui/system";

import React from "react";
import { tokens } from "../utils/theme";

function _CircularProgress() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  return (
    <Box display="flex" justifyContent="center">
      <CircularProgress
        // variant="determinate"
        sx={{ color: colors.blueAccent[400] }}
        size={90}
        thickness={2}
        // value={50}
      />
    </Box>
  );
}

export default _CircularProgress;
