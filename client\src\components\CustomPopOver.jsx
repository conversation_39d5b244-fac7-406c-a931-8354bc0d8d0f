import { Popover, useTheme } from "@mui/material";
import React from "react";
import { tokens } from "../utils/theme";
function CustomPopOver({ children, isopen, anchorEl, handleClose }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  return (
    <Popover
      open={isopen}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "center",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "center",
      }}
      style={{
        color: colors.grey,
      }}
    >
      {children}
    </Popover>
  );
}

export default CustomPopOver;
