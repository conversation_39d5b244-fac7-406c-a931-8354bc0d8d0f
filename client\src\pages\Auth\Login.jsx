import { LockOutlined, Visibility, VisibilityOff } from "@mui/icons-material";
import {
  Alert,
  Avatar,
  Button,
  TextField,
  Typography,
  useTheme,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { Box } from "@mui/material";

import React, { useEffect, useState } from "react";

import login from "../../api/userApi/login";
import { useStateContext } from "../../context/ContextProvider";
import { tokens } from "../../utils/theme";

function Login() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [showPassword, setShowPassword] = useState(false);
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  const [formData, setFormdata] = useState({
    username: "",
    password: "",
  });

  const { setUser, setToken, setErrors, errors } = useStateContext();
  const handleChange = (e) => {
    setFormdata({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    const payload = {
      username: data.get("username"),
      password: data.get("password"),
    };
    login(payload, setToken, setUser, setErrors);
  };
  return (
    <Box
      sx={{
        marginTop: 8,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <Avatar sx={{ m: 1, bgColor: "secondary.main" }}>
        <LockOutlined />
      </Avatar>
      <Typography variant="h2">Sign in </Typography>
      <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
        {errors && (
          <div>
            {Object.keys(errors).map((key) => (
              <Alert variant="filled" severity="error" key={key} sx={{ mt: 1 }}>
                {errors[key][0]}
              </Alert>
            ))}
          </div>
        )}
        <TextField
          error={!errors.username == ""}
          id="username"
          margin="normal"
          fullWidth
          label="Username"
          name="username"
          autoComplete="username"
          InputLabelProps={{
            color: "success",
          }}
          type="text"
          value={formData.username}
          onChange={handleChange}
          helperText={errors.username ? errors.username[0] : null}
        />
        <TextField
          error={!errors.password == ""}
          margin="normal"
          fullWidth
          name="password"
          label="password"
          type={showPassword ? "text" : "password"}
          id="password"
          autoComplete="current-password"
          InputLabelProps={{
            color: "success",
          }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  aria-label="toggle password visibility"
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            ),
          }}
          helperText={errors.password ? errors.password[0] : null}
        />

        <Button
          fullWidth
          type="submit"
          variant="contained"
          sx={{ mt: 3, py: 2 }}
          color="success"
        >
          Sign in
        </Button>
      </Box>
    </Box>
  );
}

export default Login;
