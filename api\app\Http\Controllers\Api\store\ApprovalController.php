<?php

namespace App\Http\Controllers\Api\store;

use App\Http\Controllers\Controller;
use App\Http\Resources\ApprovalResource;
use App\Models\Approval;
use App\Models\InventoryMovement;
use App\Models\Request as StoreRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ApprovalController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return ApprovalResource::collection(Approval::with(['request', 'approver'])->get());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'request_id' => 'required|exists:requests,id',
            'status' => 'required|in:approved,rejected',
            'notes' => 'nullable|string',
        ]);

        $storeRequest = StoreRequest::findOrFail($request->request_id);

        // Check if the request is already approved or rejected
        if ($storeRequest->status !== 'pending') {
            return response()->json(['message' => 'This request has already been processed.'], 422);
        }

        DB::beginTransaction();
        try {
            $approval = Approval::create([
                'request_id' => $request->request_id,
                'approved_by' => Auth::id(),
                'status' => $request->status,
                'notes' => $request->notes,
            ]);

            $storeRequest->update(['status' => $request->status]);

            if ($request->status === 'approved') {
                $item = $storeRequest->item;
                if ($item->stock_quantity < $storeRequest->quantity) {
                    throw new \Exception('Not enough stock available.');
                }
                $item->decrement('stock_quantity', $storeRequest->quantity);

                InventoryMovement::create([
                    'item_id' => $item->id,
                    'movement_type' => 'outward',
                    'quantity' => $storeRequest->quantity,
                    'notes' => 'Approved request',
                    'created_by' => Auth::id(),
                    'request_id' => $storeRequest->id,
                ]);
            }

            DB::commit();

            return new ApprovalResource($approval);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['message' => $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Approval $approval)
    {
        return new ApprovalResource($approval->load(['request', 'approver']));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Approval $approval)
    {
        // Generally, approvals are not updated.
        // If you need to change an approval, it's better to create a new one.
        return response()->json(['message' => 'Approvals cannot be updated.'], 405);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Approval $approval)
    {
        $approval->delete();

        return response()->noContent();
    }
}