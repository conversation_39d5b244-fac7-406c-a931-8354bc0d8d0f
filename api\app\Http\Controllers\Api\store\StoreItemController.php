<?php

namespace App\Http\Controllers\Api\store;

use App\Http\Controllers\Controller;
use App\Http\Resources\StoreItemResource;
use App\Models\storeItems;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class StoreItemController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return StoreItemResource::collection(storeItems::with('category')->get());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'required|exists:categories,id',
            'stock_quantity' => 'required|integer|min:0',
            'reorder_level' => 'required|integer|min:0',
        ]);

        $item = storeItems::create([
            'name' => $request->name,
            'description' => $request->description,
            'category_id' => $request->category_id,
            'stock_quantity' => $request->stock_quantity,
            'reorder_level' => $request->reorder_level,
            'user_id' => Auth::id(),
        ]);

        return new StoreItemResource($item->load('category'));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(storeItems $item)
    {
        return new StoreItemResource($item->load('category'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, storeItems $item)
    {
        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'category_id' => 'sometimes|required|exists:categories,id',
            'stock_quantity' => 'sometimes|required|integer|min:0',
            'reorder_level' => 'sometimes|required|integer|min:0',
        ]);

        $item->update($request->all());

        return new StoreItemResource($item);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(storeItems $item)
    {
        $item->delete();

        return response()->noContent();
    }
}
