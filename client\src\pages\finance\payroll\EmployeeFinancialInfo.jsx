import React from "react";
import {
    Box,
    TextField,
  } from "@mui/material";
  import Header from "../../../components/Header";
function EmployeeFinancialInfo() {
  return (
    <Box>
      <Header title="Employee's Financial Information" heading="h6" />

      <TextField
        label="Basic Salary"
        value="0 in a month"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
      <TextField
        label="Location Salary"
        value="0 in a month"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
      <TextField
        label="Transport Allowance"
        value="0 in a month"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
      <TextField
        label="Bank Account"
        value="0"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
      <TextField
        label="Pay Tax"
        value="0"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
      <TextField
        label="Has Pension"
        value="Yes"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
      <TextField
        label="Has Provident Fund"
        value="Yes"
        variant="standard"
        InputProps={{
          readOnly: true,
        }}
        disabled
        size="small"
        sx={{ mr: 1 }}
      />
    </Box>
  );
}

export default EmployeeFinancialInfo;
