import { Container, Typography, useTheme } from "@mui/material";
import { tokens } from "../../utils/theme";

function UnauthorizedPage() {
    const theme =useTheme()
    const colors=tokens(theme.palette.mode)
    return (
      <Container maxWidth="sm" color={colors
      .grey[100]} >
        <Typography variant="h2" align="center" gutterBottom>
          403 Forbidden  Resource Access
        </Typography>
        <Typography variant="body1" align="center" color={colors
        .redAccent[700]}>
          You do not have permission to access this page. Please contact the administrator.
        </Typography>
      </Container>
    );
  }
  export default UnauthorizedPage;
