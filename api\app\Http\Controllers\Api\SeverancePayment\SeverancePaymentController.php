<?php

namespace App\Http\Controllers\Api\SeverancePayment;

use App\Http\Controllers\Controller;
use App\Http\Resources\TerminationResource;
use App\Models\EmpBasic;
use App\Models\settings\Termination;
use App\Models\SeverancePayment;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SeverancePaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        Termination::where('emp_basic_id', $request->input('emp_basic_id'))->update(['severance_payment_status' => 1]);
        $data = SeverancePayment::create([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'termination_date' => $request->input('termination_date'),
            'basic_salary' => $request->input('basic_salary'),
            'y_of_service' => $request->input('y_of_service'),
            'total_severance_pay' => $request->input('total_severance_pay'),
            'severance_tax' => $request->input('severance_tax'),
            'severance_net' => $request->input('severance_net'),
            'termination_reason' => $request->input('termination_reason'),
            'approval_date' => $request->input('approval_date'),
            'user_id' => $request->input('user_id'),
        ]);
        return response($data);
    }
    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
