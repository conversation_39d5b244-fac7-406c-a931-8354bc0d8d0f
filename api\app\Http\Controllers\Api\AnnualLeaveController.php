<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\helper\LeaveHelper;
use App\Http\Controllers\Controller;
use App\Models\AnnualLeave;
use App\Models\EmpBasic;
use DateTime;
use Illuminate\Http\Request;


class AnnualLeaveController extends Controller
{

    public $employedYear;
    public $currentYear;
    public $effectiveDate;
    public $leaveIndex14;
    public $expiredYear;
    public $totalLeave14;
    public $totalLeave16;
    public $lengthOfDays;
    public $leaveHelper;
    public $leaveFraction14;
    public $leaveFraction16;
    public function __construct()
    {
        // $this->employeedYear = new DateTime($employedDate);
        $this->employedYear = '';
        $this->currentYear = new DateTime(date('Y-m-d'));
        $this->effectiveDate = new DateTime('2019-09-05');
        $this->expiredYear = new DateTime(date('Y-m-d', strtotime('-2 years')));
        $this->leaveIndex14 = 0;
        $this->totalLeave14 = [];
        $this->totalLeave16 = [];
        $this->lengthOfDays = 365;
        $this->leaveFraction14 = 0;
        $this->leaveFraction16 = 0;
        $this->leaveHelper = new LeaveHelper();
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(AnnualLeave::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = AnnualLeave::create([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'client_id' => $request->input('client_id'),
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'reg_date' => $request->input('reg_date'),
            'reason' => $request->input('reason'),
            'leave_taken' => $request->input('leave_taken'),
            'from_expired' => $request->input('from_expired'),
            'user_id' => $request->input('user_id'),

        ]);
        return response($data);
    }

    public function LeaveTaken($id)
    {
        $leaves = AnnualLeave::where('emp_basic_id', $id)->get();

        return response($leaves);
    }
    public function leaveTakenTwoYears($id)
    {
        $leaveTakens = AnnualLeave::where('emp_basic_id', $id)->where('reason', 'Annual leave')->where('from_expired', null)->where('reg_date', '>=', $this->expiredYear)->get();

        return response($leaveTakens);
    }
    /**
     * Display the specified resource.
     *
     * This method will calculate the employee's annual leave based on
     * the employment date and the current date.
     *
     * @param  int  $id The employee ID
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // Get the employee's start date
        $startDate = EmpBasic::select('start_date')->where('id', $id)->first();
        if (!$startDate) {
            return response(['error' => 'Employee not found'], 404);
        }

        // Set the employed year and current year
        $this->employedYear = new DateTime($startDate->start_date);
        $this->currentYear = new DateTime('now');

        // Calculate the interval from the employment date to the current date
        $intervalFromEmpToCurrentDate = $this->employedYear->diff($this->currentYear);

        // If the employee has not completed 6 months of employment, return 0 values
        if ($intervalFromEmpToCurrentDate->days <= 182) {
            return response([
                'totalLeaveTaken' => 0,
                'leaveTakenTwoYears' => 0,
                'totalLeave' => 0,
                'leaveDiff' => 0,
                'expiredLeave' => 0,
                'availableLeave' => 0,
                'availableLeaveEntitled' => 0,
                'leaveLookUp16' => 0,
                'leaveFraction14' => 0,
                'leaveFraction1' => 0,
                'leaveFraction2' => 0,
                'employeedYear' => $this->employedYear,
                'currentYear' => $this->currentYear,
                'intervalFromEmpToCurrentDate' => $intervalFromEmpToCurrentDate,
            ]);
        }

        // Call the calculation method
        $leaveData = $this->calculation($id);

        // Return the response
        return response($leaveData);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\AnnualLeave  $annualLeave
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $annualLeave = AnnualLeave::find($id);
        $annualLeave->update([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'client_id' => $request->input('client_id'),
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'reg_date' => $request->input('reg_date'),
            'reason' => $request->input('reason'),
            'leave_taken' => $request->input('leave_taken'),
            'from_expired' => $request->input('from_expired'),
            'user_id' => $request->input('user_id'),
        ]);
        return response($annualLeave);
    }

    public function getLeave($id)
    {
        $leave = AnnualLeave::find($id);
        return response($leave);
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\AnnualLeave  $annualLeave
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $annualLeave = AnnualLeave::find($id);
        $annualLeave->delete();

        return response('', 204);
    }


    public function calculation($id, $is_under_six = false)
    {
        $startDate = EmpBasic::select('start_date')->where('id', $id)->first();
        $this->employedYear = new DateTime($startDate->start_date);
        $totalLeaveTaken = $this->leaveHelper->annualLeave($id);

        $leaveTaken = $this->leaveHelper->leaveTakenTwoYears($id, $this->expiredYear);
        $intervalFromEmpToCurrentDate = $this->employedYear->diff($this->currentYear);
        $intervalFromEmpToEffectiveDate = $this->employedYear->diff($this->effectiveDate);
        if ($intervalFromEmpToCurrentDate->days > 182 || $is_under_six) {
            if ($this->employedYear < $this->effectiveDate) {
                if ($intervalFromEmpToEffectiveDate->days < 365) {
                    $isLeapYear = $this->leaveHelper->isLeapYear($this->employedYear);
                    if ($isLeapYear) {
                        $this->lengthOfDays = 366;
                    }
                    $this->leaveIndex14 = $intervalFromEmpToEffectiveDate->days * 14 / $this->lengthOfDays;
                    $this->totalLeave14['totalLeave'] = $intervalFromEmpToEffectiveDate->days * 14 / $this->lengthOfDays;
                    $this->leaveFraction14 = $intervalFromEmpToEffectiveDate->days * 14 / $this->lengthOfDays;
                } else {
                    $this->totalLeave14 = $this->leaveHelper->getTotalLeave14($this->employedYear, $this->effectiveDate);

                    $pattern = $this->totalLeave14['pattern'];
                    $key = end($pattern);
                    $key = key($pattern);
                    $lastYearItrated = new DateTime("$key");
                    $diff = $lastYearItrated->diff($this->effectiveDate);

                    if ($this->leaveHelper->isLeapYear($this->effectiveDate)) {
                        $this->lengthOfDays = 366;
                    }

                    if ($diff->days > 0) {

                        $this->totalLeave14['totalLeave'] =
                            $this->totalLeave14['totalLeave'] + (($diff->days * ($this->totalLeave14['pattern'][$key] + 1)) / $this->lengthOfDays);
                        $this->leaveIndex14 = $this->totalLeave14['pattern'][$key] + 1;
                        $this->leaveFraction14 = (($diff->days * ($this->totalLeave14['pattern'][$key] + 1)) / $this->lengthOfDays);
                    } else {
                        $this->totalLeave14['totalLeave'] = $this->totalLeave14['totalLeave'] + (($diff->days * ($this->totalLeave14['pattern'][$key])) / $this->lengthOfDays);
                        $this->leaveIndex14 = $this->totalLeave14['pattern'][$key];
                        $this->leaveFraction14 = (($diff->days * ($this->totalLeave14['pattern'][$key])) / $this->lengthOfDays);
                    }
                }
                if ($this->leaveIndex14 < 16) {
                    $this->totalLeave16 = $this->leaveHelper->getTotalLeave16($this->effectiveDate, $this->currentYear);
                } else {
                    $this->totalLeave16 = $this->leaveHelper->getTotalLeave16($this->effectiveDate, $this->currentYear, $this->leaveIndex14);
                }

                $pattern16 = $this->totalLeave16['pattern'];
                $key = end($pattern16);
                $key = key($pattern16);
                $dateString = $this->effectiveDate;
                $month = date('m', strtotime($dateString->format('Y-m-d')));
                $day = date('d', strtotime($dateString->format('Y-m-d')));
                $date = new DateTime("$key-$month-$day");
                $diff = $date->diff($this->currentYear);
                if ($this->leaveHelper->isLeapYear($this->currentYear)) {
                    $this->lengthOfDays = 366;
                }
                if ($this->totalLeave16['isReapted']) {
                    $this->totalLeave16['totalLeave'] = $this->totalLeave16['totalLeave'] + $diff->days * (($this->totalLeave16['pattern'][$this->currentYear->format('Y') - 1] + 1) / $this->lengthOfDays);
                } else {
                    $this->totalLeave16['totalLeave'] = $this->totalLeave16['totalLeave'] + $diff->days * $this->totalLeave16['pattern'][$this->currentYear->format('Y') - 1] / $this->lengthOfDays;
                }
                $gteIndex = $this->totalLeave16['pattern'][$this->expiredYear->format('Y')];
                $getIndex2 = $this->totalLeave16['pattern'][$key];
                $lastTwo = $this->totalLeave16['lastTwo'];

                $leaveFraction1 = ($diff->days * $gteIndex / $this->lengthOfDays);
                if ($this->totalLeave16['isReapted']) {
                    $leavefraction2 = ($diff->days * ($getIndex2 + 1) / $this->lengthOfDays);
                } else {
                    $leavefraction2 = ($diff->days * $getIndex2 / $this->lengthOfDays);
                }

                $entitled = $lastTwo + $leavefraction2 - $leaveFraction1;
                $totalLeave = $this->totalLeave16['totalLeave'] + $this->totalLeave14['totalLeave'];

                $leaveAvailableBeforTwoYears = $totalLeave - $entitled;

                $totalLeaveTakenBeforeTwoYears = $totalLeaveTaken['totalLeave'] - $leaveTaken['leaveTakenIntwoYears'];

                $expiredLeave = $leaveAvailableBeforTwoYears - $totalLeaveTakenBeforeTwoYears;

                $availableLeave = $entitled - $leaveTaken['leaveTakenIntwoYears'];
                return [
                    'totalLeaveTaken' => $totalLeaveTaken['totalLeave'],
                    'leaveTakenTwoYears' => $leaveTaken['leaveTakenIntwoYears'],
                    'totalLeave' => $totalLeave,
                    'availableLeave' => $availableLeave,
                    'expiredLeave' => $expiredLeave,
                    'availableLeaveEntitled' => $entitled,
                    'leaveLookUp14' => $this->totalLeave14['pattern'] ?? 0,
                    'leaveLookUp16' => $this->totalLeave16['pattern'],
                    'leaveFraction14' => $this->leaveFraction14,
                    'leaveFraction1' => $leaveFraction1,
                    'employeedYear' => $this->effectiveDate,
                    'leaveFraction2' => $leavefraction2
                ];
            } else {
                if ($intervalFromEmpToCurrentDate->days < 365) {
                    if ($this->leaveHelper->isLeapYear($this->employedYear)) {
                        $this->lengthOfDays = 366;
                    }
                    $totalLeave = ($intervalFromEmpToCurrentDate->days * 16 / $this->lengthOfDays);
                    $leaveDiff = $totalLeave - $totalLeaveTaken['totalLeave'];
                    $availableLeaveEntitled = $totalLeave;
                    return [
                        'totalLeaveTaken' => $totalLeaveTaken['totalLeave'],
                        'leaveTakenTwoYears' => $leaveTaken['leaveTakenIntwoYears'],
                        'totalLeave' => $totalLeave,
                        'availableLeave' => $leaveDiff,
                        'expiredLeave' => 0,
                        'availableLeaveEntitled' => $availableLeaveEntitled,
                        'leaveLookUp14' => 0,
                        'leaveLookUp16' => 0,
                        'leaveFraction14' => 0,
                        'leaveFraction1' => 0,
                        'employeedYear' => $this->employedYear,
                        'leaveFraction2' => 0
                    ];
                } else {
                    $this->totalLeave16 = $this->leaveHelper->getTotalLeave16($this->employedYear, $this->currentYear);
                    $dateString = $this->employedYear;
                    $pattern16 = $this->totalLeave16['pattern'];
                    $key = end($pattern16);
                    $key = key($pattern16);
                    $month = date('m', strtotime($dateString->format('Y-m-d')));
                    $day = date('d', strtotime($dateString->format('Y-m-d')));
                    $date = new DateTime("$key-$month-$day");
                    $diff = $date->diff($this->currentYear);

                    if ($this->leaveHelper->isLeapYear($this->currentYear)) {
                        $this->lengthOfDays = 366;
                    }
                    if ($this->totalLeave16['yearIndex'] <= 1) {
                        $this->totalLeave16['totalLeave'] = $this->totalLeave16['days'] * 16 / $this->lengthOfDays;
                    } else {
                        if ($this->totalLeave16['isReapted']) {
                            $this->totalLeave16['totalLeave'] = $this->totalLeave16['totalLeave'] + $diff->days * (($this->totalLeave16['pattern'][$this->currentYear->format('Y') - 1] + 1) / $this->lengthOfDays);
                        } else {
                            $this->totalLeave16['totalLeave'] = $this->totalLeave16['totalLeave'] + $diff->days * $this->totalLeave16['pattern'][$this->currentYear->format('Y') - 1] / $this->lengthOfDays;
                        }
                    }
                    if (isset($this->totalLeave16['pattern'][$this->expiredYear->format('Y')])) {
                        $leaveFraction1 = $this->totalLeave16['pattern'][$this->expiredYear->format('Y')] * $diff->days / $this->lengthOfDays;
                    } else {
                        $leaveFraction1 = 0;
                    }
                    $getIndex2 = $this->totalLeave16['pattern'][$key];
                    $lastTwo = $this->totalLeave16['lastTwo'];
                    if ($this->totalLeave16['isReapted']) {
                        $leaveFraction2 = ($diff->days * ($getIndex2 + 1) / $this->lengthOfDays);
                    } else {
                        $leaveFraction2 = $diff->days * $getIndex2 / $this->lengthOfDays;
                    }
                    $entitled = $lastTwo + $leaveFraction2 - $leaveFraction1;
                    $totalLeave = $this->totalLeave16['totalLeave'];
                    $leaveAvailableBeforTwoYears = $totalLeave - $entitled;
                    $totalLeaveTakenBeforeTwoYears = $totalLeaveTaken['totalLeave'] - $leaveTaken['leaveTakenIntwoYears'];
                    $expiredLeave = $leaveAvailableBeforTwoYears - $totalLeaveTakenBeforeTwoYears;

                    if ($this->totalLeave16['yearIndex'] <= 1) {
                        $availableLeave = $totalLeave - $leaveTaken['leaveTakenIntwoYears'];
                        $entitled = $totalLeave;
                        $expiredLeave = 0;
                    } else {
                        $availableLeave = $entitled - $leaveTaken['leaveTakenIntwoYears'];
                        $expiredLeave = $leaveAvailableBeforTwoYears - $totalLeaveTakenBeforeTwoYears;
                    }

                    return [
                        'totalLeaveTaken' => $totalLeaveTaken['totalLeave'],
                        'leaveTakenTwoYears' => $leaveTaken['leaveTakenIntwoYears'],
                        'totalLeave' => $totalLeave,
                        'availableLeave' => $availableLeave,
                        'expiredLeave' => $expiredLeave,
                        'availableLeaveEntitled' => $entitled,
                        'leaveLookUp14' => 0,
                        'leaveLookUp16' => $this->totalLeave16['pattern'],
                        'leaveFraction14' => 0,
                        'employeedYear' => $this->employedYear,
                        'leaveFraction1' => $leaveFraction1,
                        'leaveFraction2' => $leaveFraction2
                    ];
                }
            }
        } else {

            return [
                'totalLeaveTaken' => 0,
                'leaveTakenTwoYears' => 0,
                'totalLeave' => 0,
                'leaveDiff' => 0,
                'expiredLeave' => 0,
                'availableLeave' => 0,
                'availableLeaveEntitled' => 0,
                'leaveLookUp16' => 0,
                'leaveFraction14' => 0,
                'leaveFraction1' => 0,
                'leaveFraction2' => 0,
                'employeedYear' => $this->employedYear,
            ];
        }
    }
}
