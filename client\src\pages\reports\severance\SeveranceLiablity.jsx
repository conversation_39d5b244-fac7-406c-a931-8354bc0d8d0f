import React from "react";
import { Box, Paper, Typography, useTheme } from "@mui/material";
import { commonGridStyles } from "../../../helper/usegridStyle";
import { DataGrid } from "@mui/x-data-grid";
import Header from "../../../components/Header";
import { useSeveranceLiablity } from "../../../api/userApi/clinetHook";
import { tokens } from "../../../utils/theme";
import CustomToolBar from "../../../helper/CustomToolBar";
const SeveranceLiablity = () => {
  const theme = useTheme();
  const colors=tokens(theme.palette.mode)
  const gridStyles = commonGridStyles(theme);
  const { data: severance_liablity, isLoading } = useSeveranceLiablity();
  const columns = [
    {
      field: "id",
      headerName: "ID",
    },
    {
      field: "full_name",
      headerName: "FullName",
      // width: "200",
      flex: 1.5,
    },
    {
      field: "basic_salary",
      headerName: "BasicSalary",
      flex: 1,
    },
    {
      field: "start_date",
      headerName: "startDate",
      flex: 1,
    },
    {
      field: "y_of_service",
      headerName: "YearOfService",
      flex: 1,
    },
    {
      field: "total_severance_pay",
      headerName: "Gross-severancePayment",
      flex: 1,
      cellClassName: "custom-pay",
    },
  ];
  if (isLoading) {
    return <>loading....</>;
  }
  return (
    <div style={{ margin: "10px" }}>
      <Header title="severance liablity Report" />
      <Box
        sx={{
          ...gridStyles,
        }}
      >
        {severance_liablity && (
          <DataGrid
            sx={{
              width: "100%",
              marginBottom: 5,
              "& .MuiDataGrid-columnHeaderTitle": {
                whiteSpace: "normal",
                wordWrap: "break-word",
                overflowWrap: "",
                lineHeight: "normal",
              },
              "& .MuiDataGrid-columnHeader": {
                height: "unset !important",
                paddingX: "20px",
              },
            }}
            columns={columns}
            rows={severance_liablity.employees}
            autoHeight
            pagination
            pageSize={10}
            rowsPerPageOptions={[10, 20, 30]}
            components={{
            Toolbar:CustomToolBar
          }}
          />
        )}
        {severance_liablity && (
          <Paper
            sx={{
              padding: "1rem",
              color: colors.grey[100],
              background: colors.primary[400],
              display:'flex',
            }}
          >
            <Typography variant="button">Sum of Total Severance Liablity:<span style={{
                color:colors.redAccent[500],
                fontSize:'16px'
            }}>{` ${severance_liablity.total_severance} `}</span> birr</Typography>
          </Paper>
        )}
      </Box>
    </div>
  );
};

export default SeveranceLiablity;
