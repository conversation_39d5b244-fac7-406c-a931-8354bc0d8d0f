import React from "react";
import { Box, Grid, Button, TextField, MenuItem } from "@mui/material";
import { useState, useEffect } from "react";
import { useStateContext } from "../../../context/ContextProvider";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";

const loanType = [
  {
    label: "Medical Cost",
    value: "Medical Loan",
  },
  {
    label: "Company Loan",
    value: "Company Loan",
  },
  {
    label: "Labor Union Loan",
    value: "Labor Loan",
  },
  {
    label: "CostSharing Loan",
    value: "CostSharing Loan",
  },
  {
    label: "Other Loan",
    value: "Other Loan",
  },
];
function EmployeeDeductableForm({ formData, editData, handleRefetch, setEditData }) {
  const numberArray = [...Array(12).keys()].map((x) => ++x);
  const queryClient = useQueryClient();
  const [deductable, setDeductable] = useState({
    loan_type: "",
    loan_amount: "",
    loan_period: "",
    loan_date: "",
    reason: "",
  });
  useEffect(() => {
    if (editData && editData.id) {
      setDeductable(editData);
    }
  }, [editData]);
  const { setNotification, user } = useStateContext();
  const handleChange = (e) => {
    setDeductable({
      ...deductable,
      [e.target.name]: e.target.value,
    });
  };
  const createEmpDeduct = useMutation(
    (data) => api.post("emp_deductable", data),
    {
      onSuccess: () => {
        setNotification("addtitonal deductable created successfully");
        handleRefetch();
        queryClient.invalidateQueries({ queryKey: "empDeductable" });
      },
    }
  );
  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    const remark = data.get("reason") ? data.get("reason") : "default reason";

    const payload = {
      emp_basic_id: formData.emp_basic_id,
      loan_type: data.get("loan_type"),
      loan_amount: data.get("loan_amount"),
      loan_period: data.get("loan_period"),
      loan_date: data.get("loan_date"),
      reason: remark,
      user_id: user.id,
    };
    
    if (editData && editData.id) {
      api.put(`emp_deductable/${editData.id}`, payload).then(() => {
        setNotification("Employee deductable record updated successfully");
        handleRefetch();
        setEditData({});
        queryClient.invalidateQueries({ queryKey: "empDeductable" });
      });
    } else {
      createEmpDeduct.mutate(payload);
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Loan Type"
            name="loan_type"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={deductable.loan_type ? deductable.loan_type : "Medical Loan"}
          >
            {loanType.map((loan) => (
              <MenuItem key={loan.value} value={loan.value}>
                {loan.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>

        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Loan Period "
            name="loan_period"
            fullWidth
            select
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={deductable.loan_period}
          >
            {numberArray.map((num) => (
              <MenuItem key={num} value={num}>
                {num === 1 ? <>{num} month</> : <>{num} months</>}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Loan Amount"
            name="loan_amount"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={deductable.loan_amount}
          />
        </Grid>

        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Effective Date"
            name="loan_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={
              deductable.loan_date || new Date().toISOString().slice(0, 10)
            }
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Reason"
            name="reason"
            multiline
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={deductable.reason}
          />
        </Grid>
      </Grid>
      <span style={{ float: "inline-start" }}>
        <Button type="submit" variant="contained" color="success">
          {editData.id ? <>update</> : <>create</>}
        </Button>
      </span>
    </Box>
  );
}

export default EmployeeDeductableForm;
