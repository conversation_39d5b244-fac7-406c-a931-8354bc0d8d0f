<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\SeverancePayment\CalculateSveranceController;
use App\Http\Controllers\Controller;
use App\Models\EmpBasic;
use App\Models\User;
use Illuminate\Http\Request;

class MetricsController extends Controller
{
    public function getMetrics(){
        $severance=new CalculateSveranceController();
        // $leave=new LeaveLiablityController();
        $users=User::count();
        $employees=EmpBasic::count();
        $activeEmployees=EmpBasic::where('termination_status',0)->count();
        $liablitiy=$severance->severanceLiablityTotal();
        // $leaveLiablity=$leave->liablityFunc();
        return response([
            'users' => $users,
            'employees' => $employees,
            'active_employees' => $activeEmployees,
            'severance_liablity'=>$liablitiy,
            // 'leave_liablity_days'=>$leaveLiablity['totals_leave'],
            // 'leave_liablity_money'=>$leaveLiablity['totals_money'],
        ]);
    }
}
