<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('loan_payment_historiys', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('employee_deductable_id');
            $table->foreign('employee_deductable_id')->references('id')->on('employee_deductables')->onDelete('cascade');
            $table->date('payment_date')->default(now());
            $table->string('loan_type')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('loan_payment_historiys');
    }
};
