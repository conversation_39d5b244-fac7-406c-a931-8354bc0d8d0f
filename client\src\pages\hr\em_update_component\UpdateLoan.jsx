import { Box, Grid, TextField, Button, MenuItem } from "@mui/material";
import React, { useEffect } from "react";

import { useState } from "react";
import api from "../../../api/config/axiosConfig";

import { useStateContext } from "../../../context/ContextProvider";

function UpdateLoan({ id }) {
  const { errors, setErrors, setNotification } = useStateContext();
  const [isemp, setisEmp] = useState(false);
  const [formData, setFormData] = useState({
    emp_basic_id: "",
    loan_type: "",
    loan_amount: "",
    loan_period: "",
    loan_date: "",
    paid_amount: "",
    loan_paid: "",
    reason: "",
  });

  if (id) {
    useEffect(() => {
      api
        .get(`emp_basics/${id}?all=true`, { params: { all: id } })
        .then(({ data }) => {
          if (data.data.emp_loan) {
            setFormData(data.data.emp_loan);
            setisEmp(true);
          }
        });
    }, [id]);
  }
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  const boolValues = [
    {
      lable: "Yes",
      value: 1,
    },
    {
      lable: "No",
      value: 0,
    },
  ];
  const handleSubmit = (e) => {
    e.preventDefault();
    if (isemp) {
      api
        .put(`emp_loan/${formData.id}`, formData)
        .then(() => {
          setNotification("loan table updated successfully");
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
        });
    } else {
      const data = new FormData(e.currentTarget);
      const payload = {
        emp_basic_id: id,
        loan_type: data.get("loan_type"),
        loan_amount: data.get("loan_amount"),
        loan_period: data.get("loan_period"),
        loan_date: data.get("loan_date"),
        paid_amount: data.get("paid_amount"),
        loan_paid: data.get("loan_paid"),
        reason: data.get("reason"),
      };
      api
        .post("emp_loan", payload)
        .then(() => {
          setNotification("successfully created");
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
        });
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.loan_type == ""}
            margin="normal"
            label="Loan Type"
            name="loan_type"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.loan_type}
            helperText={errors.loan_type ? errors.loan_type[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.loan_amount == ""}
            margin="normal"
            label="Loan Amount"
            name="loan_amount"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.loan_amount}
            helperText={errors.loan_amount ? errors.loan_amount[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.loan_period == ""}
            margin="normal"
            label="Loan Period"
            name="loan_period"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.loan_period}
            helperText={errors.loan_period ? errors.loan_period[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.loan_date == ""}
            margin="normal"
            label="Loan Date"
            name="loan_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.loan_date}
            helperText={errors.loan_date ? errors.loan_date[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.paid_amount == ""}
            margin="normal"
            label="Paid Amount"
            name="paid_amount"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.paid_amount}
            helperText={errors.paid_amount ? errors.paid_amount[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.loan_paid == ""}
            margin="normal"
            label="Loan Paid"
            name="loan_paid"
            fullWidth
            variant="standard"
            type="text"
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.loan_paid ? formData.loan_paid : 0}
            helperText={errors.loan_paid ? errors.loan_paid[0] : null}
          >
            {boolValues.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.reason == ""}
            margin="normal"
            label="Loan Reason"
            name="reason"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.reason}
            helperText={errors.reason ? errors.reason[0] : null}
          />
        </Grid>
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
          {isemp ? <div>update</div> : <div>create</div>}
        </Button>
      </Box>
    </Box>
  );
}

export default UpdateLoan;
