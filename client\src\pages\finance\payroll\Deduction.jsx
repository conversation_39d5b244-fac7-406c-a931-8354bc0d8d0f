import React from 'react'
import { Box, TextField } from "@mui/material";
import Header from "../../../components/Header";
function Deduction() {
  return (
    <Box>
    <Header heading="h6" title="Deductions" />
    <TextField
      label="Income Tax:"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Pension 7%:"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Provident Fund 4%:"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Medical (0% X TMC)"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Absence (AD X (BS + DS))"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Additional Deductions(Loan + CA + LU +OD +SP):"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
  </Box>
  )
}

export default Deduction