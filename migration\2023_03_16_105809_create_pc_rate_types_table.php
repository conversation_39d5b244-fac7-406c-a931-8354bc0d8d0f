<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use League\CommonMark\Extension\CommonMark\Node\Inline\Strong;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pc_rate_types', function (Blueprint $table) {
            $table->id();
            $table->string('pc_rate_type')->nullable();
            $table->float('pc_rate_amount')->default(0.0);
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
        $path=Storage::path('pcrate_type.json');
        $data=file_get_contents($path);
        $decoded=json_decode($data,true);
        DB::table('pc_rate_types')->insert($decoded);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pc_rate_types');
    }
};
