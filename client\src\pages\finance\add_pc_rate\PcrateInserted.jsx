import React from "react";
import Header from "../../../components/Header";
import {
  Button,
  Typography,
  useTheme,
  Box,
  CircularProgress,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { tokens } from "../../../utils/theme";
import api from "../../../api/config/axiosConfig";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useStateContext } from "../../../context/ContextProvider";
function PcrateInserted({ selectedRow, emp_basic_id }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const {
    data: pcrate,
    isLoading,
    isFetched,
  } = useQuery(
    ["pcrate", emp_basic_id],
    async () =>
      await api.get(`pcrate/${emp_basic_id}`).then(({ data }) => data),
    {
      enabled: !!emp_basic_id,
      staleTime: 6000,
    }
  );
  const columns = [
    {
      field: "id",
      headerName: "ID",
      flex: 0.2,
    },
    {
      field: "from_date",
      headerName: "StartDate",
      flex: 0.5,
    },
    {
      field: "to_date",
      headerName: "To Date",
      flex: 0.5,
    },
    {
      field: "pc_type",
      headerName: "pc Type",
      flex: 0.8,
    },
    {
      field: "no_pc",
      headerName: "Number of Pc",
      flex: 0.5,
    },
    {
      field: "pc_rate_amount",
      headerName: "Pc Rate Amount",
      flex: 0.5,
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 0.7,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          selectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "delete",
      headerName: "Delete",
      flex: 0.8,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deletPcRate = useMutation((id) => api.delete(`pcrate/${id}`), {
          onSuccess: () => {
            setNotification("Absence record deleted successfully");
            queryClient.invalidateQueries({ queryKey: "pcrate" });
          },
        });
        const deleteOverTime = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              "Are you sure you want to delete the selected  pcrate record?"
            )
          ) {
            return;
          }

          deletPcRate.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deleteOverTime}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];
  return (
    <Box>
      <Header title="List Of Unpaid Pc Rate Records" heading="h5" />
      {isLoading && <CircularProgress />}
      {isFetched && (
        <DataGrid columns={columns} hideFooter autoHeight rows={pcrate} />
      )}
    </Box>
  );
}

export default PcrateInserted;
