<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PhytosanitaryCertificate extends Model
{
    use HasFactory;

    protected $fillable = [
        'coffee_batch_id',
        'certificate_number',
        'issue_date',
        'expiry_date',
        'inspector_name'
    ];

    public function coffeeBatch()
    {
        return $this->belongsTo(CoffeeBatch::class);
    }
}
