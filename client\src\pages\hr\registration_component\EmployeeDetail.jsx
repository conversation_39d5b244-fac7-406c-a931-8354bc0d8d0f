import {
  <PERSON>rid,
  <PERSON>Field,
  Typography,
  Box,
  Button,
  MenuItem,
} from '@mui/material';
import React from 'react';
import { useState, useEffect } from 'react';
import api from '../../../api/config/axiosConfig';
import { useStateContext } from '../../../context/ContextProvider';
import address from './address';
import { useQuery } from '@tanstack/react-query';

function EmployeeDetail({ onNext, onBack, clicked }) {
  const { errors, setErrors, empBasic, empdetail, setEmpDetail, user } =
    useStateContext();
  const [formData, setFormData] = useState({
    height: '',
    weight: '',
    address: '',
    city: '',
    sub_city: '',
    kebele: '',
    house_no: '',
    country: '',
    phone: '',
    photo_url: '',
    user_id: user.id,
  });

  useEffect(() => {
    setFormData({
      ...formData,
      user_id: user.id,
    });
  }, [user]);

  if (clicked && empdetail) {
    useQuery(['detailInfo'], () => api.get(`emp_details/${empdetail}`), {
      onSuccess: ({ data }) => {
        const {
          height,
          weight,
          address,
          city,
          sub_city,
          kebele,
          house_no,
          country,
          phone,
        } = data;
        setFormData((prevFormData) => ({
          ...prevFormData,
          height,
          weight,
          address,
          city,
          sub_city,
          kebele,
          house_no,
          country,
          phone,
        }));
      },
    });
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    data.append('photo_url', formData.photo_url);

    const payload = {
      emp_basic_id: empBasic,
      height: data.get('height'),
      weight: data.get('weight'),
      address: data.get('address'),
      city: data.get('city'),
      sub_city: data.get('sub_city'),
      kebele: data.get('kebele'),
      house_no: data.get('house_no'),
      country: data.get('country'),
      phone: data.get('phone'),
      photo_url: data.get('photo_url'),
      user_id: user.id,
    };

    if (clicked && empdetail) {
      api
        .put(`emp_details/${empdetail}`, payload)
        .then(() => {
          onNext();
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
        });
    } else {
      api
        .post('emp_details', payload, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
        .then(({ data }) => {
          onNext();
          setEmpDetail(data.id);
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
        });
    }
  };
  
  return (
    <Box>
      <Typography>Employee Detail Inoformation</Typography>
      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              margin="normal"
              label="Height"
              name="height"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.height}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              margin="normal"
              label="Weight"
              name="weight"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.weight}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.address == ''}
              margin="normal"
              label="Address"
              name="address"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.address ? formData.address : 'addis ababa'}
              helperText={errors.address ? errors.address[0] : null}
            >
              {address.map((val) => (
                <MenuItem key={val.value} value={val.value}>
                  {val.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.city == ''}
              margin="normal"
              label="City"
              name="city"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.city}
              helperText={errors.city ? errors.city[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.sub_city == ''}
              margin="normal"
              label="Sub City"
              name="sub_city"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.sub_city}
              helperText={errors.sub_city ? errors.sub_city[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.kebele == ''}
              margin="normal"
              label="Kebele"
              name="kebele"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.kebele}
              helperText={errors.kebele ? errors.kebele[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.house_no == ''}
              margin="normal"
              label="House Number"
              name="house_no"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.house_no}
              helperText={errors.house_no ? errors.house_no[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.country == ''}
              margin="normal"
              label="Country"
              name="country"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.country}
              helperText={errors.country ? errors.country[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.phone == ''}
              margin="normal"
              label="Phone"
              name="phone"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.phone}
              helperText={errors.phone ? errors.phone[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              margin="normal"
              label="Image"
              name="photo_url"
              fullWidth
              variant="standard"
              type={'file'}
              inputProps={{ accept: 'image/*' }}
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.photo_url}
            />
          </Grid>
        </Grid>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button onClick={onBack} sx={{ mt: 3, ml: 1 }} variant="contained">
            Back
          </Button>
          <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
            next
          </Button>
        </Box>
      </Box>
    </Box>
  );
}

export default EmployeeDetail;
