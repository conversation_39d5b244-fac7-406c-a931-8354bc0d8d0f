import {
  Box,
  Card,
  CardActionArea,
  CardContent,
  Grid,
  Typography,
  useTheme,
} from "@mui/material";
import React from "react";
import { tokens } from "../utils/theme";
function OpenModal({ title, subtitle, handleOpen }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  return (
    <>
      <Card
        style={{
          color: colors.grey,
          background: colors.primary[400],
          padding: 5,
        }}
        onClick={handleOpen}
      >
        <CardActionArea>
          <CardContent>
            <Typography gutterBottom variant="h5" component="div">
              {title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          </CardContent>
        </CardActionArea>
      </Card>
    </>
  );
}

export default OpenModal;
