import {
  Box,
  Button,
  Grid,
  Paper,
  Alert,
  Container,
  IconButton,
  TextField,
  InputAdornment,
  Tooltip,
} from "@mui/material";
import React, { useState } from "react";
import Header from "../../components/Header";
import { PasswordField } from "../users/ChangePassword";
import SearchUser from "../../components/SearchUser";
import api from "../../api/config/axiosConfig";
import { useStateContext } from "../../context/ContextProvider";
import {
  CopyAllOutlined,
  ShareOutlined,
  FileCopyOutlined,
  ContentCopyRounded,
} from "@mui/icons-material";

function ResetPassword() {
  const [formData, setFormData] = useState({
    password: "",
    password_confirmation: "",
  });
  const [clicked, setClicked] = useState(false);
  const [userId, setUserId] = useState("");
  const [issuccess, setIsSuccess] = useState(false);
  const [copyPassword, setCopyPassword] = useState("");
  const { setErrors, setNotification, errors } = useStateContext();
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleUserSelect = ({ value, clicked }) => {
    setUserId(value.id);
    setClicked(clicked);
    handleSubmit;
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    api
      .post(`reset-password/${userId}`, formData)
      .then(() => {
        setErrors({});
        setNotification("Password is Reseted successfully");
        setCopyPassword(formData.password);
        setIsSuccess(true);
      })
      .catch((err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          } else {
            setErrors({
              message: [err.response.data.message],
            });
          }
        }
      });
    setErrors({});
  };
  const handleCopy = () => {
    navigator.clipboard.writeText(copyPassword);
    setNotification("Password copied to clipboard");
  };
  //   const handleShare = () => {
  //     const telegramShareURL = `tg://msg?text=${encodeURIComponent(copyPassword)}`;
  //     window.open(telegramShareURL, "_blank");

  //   };
  return (
    <Box margin="20px">
      <Header title="Reset Users Password" subtitle="Reset Lost Passwords" />
      {errors && (
        <Paper
          sx={{
            marginY: 3,
            width: "400px",
          }}
        >
          {Object.keys(errors).map((key) => (
            <Alert variant="filled" severity="error" key={key} sx={{ mb: 1 }}>
              {errors[key][0]}
            </Alert>
          ))}
        </Paper>
      )}
      <Grid spacing={2} container component="form" onSubmit={handleSubmit}>
        <Grid item xs={12} sm={4}>
          <SearchUser onUserSelect={handleUserSelect} />
        </Grid>
        <Grid item xs={12} sm={8}>
          <Grid container spacing={2}>
            <Grid item sm={4} xs={12}>
              <PasswordField
                label="New Password"
                name="password"
                onChange={handleChange}
                value={formData.password}
              />
            </Grid>
            <Grid item sm={4} xs={12}>
              <PasswordField
                label="confirm New Password"
                name="password_confirmation"
                onChange={handleChange}
                value={formData.password_confirmation}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <Button
                type="submit"
                variant="contained"
                color="success"
                fullWidth
                disabled={!clicked}
              >
                reset password
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <Container sx={{ margin: "20px auto", maxWidth: "20%" }}>
        {issuccess && (
          <TextField
            label="copy new password"
            multiline
            rows={3}
            name="copy_password"
            value={copyPassword ?? ""}
            size="small"
            type="text"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <>
                    <Tooltip title="Copy Password">
                      <IconButton onClick={handleCopy}>
                        <CopyAllOutlined />
                      </IconButton>
                    </Tooltip>
                    {/* <Tooltip title="Share Password">
                    <IconButton onClick={handleShare}>
                      <ShareOutlined />
                    </IconButton>
                  </Tooltip> */}
                  </>
                </InputAdornment>
              ),
            }}
          />
        )}
      </Container>
    </Box>
  );
}

export default ResetPassword;
