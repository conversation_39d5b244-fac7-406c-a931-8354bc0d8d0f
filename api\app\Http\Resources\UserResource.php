<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            'id' => $this->id,
            'firstName' => $this->firstName,
            'lastName' => $this->lastName,
            'username' => $this->username,
            'role' => $this->role,
            'phone' => $this->phone,
            'email' => $this->email,
            'image'=>$this->image,
            'userPermission' =>$this->userPermission[0]['routes'],
            'permissionId' =>$this->userPermission[0]['id'],
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
