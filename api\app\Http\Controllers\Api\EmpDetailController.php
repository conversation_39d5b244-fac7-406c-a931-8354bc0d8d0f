<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\EmpDetail;
use App\Http\Requests\StoreEmpDetailRequest;
use App\Http\Requests\UpdateEmpDetailRequest;
use App\Http\Resources\EmpDetailResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class EmpDetailController extends Controller
{
    /**
     * Display a listing of the resource.
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection 
     * 
     */
    public function index()
    {
        return  EmpDetailResource::collection(EmpDetail::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreEmpDetailRequest  $request
     * @return \Illuminate\Http\Response
     * @return \Illuminate\Contracts\View\View|array|null|string
     */
    public function store(Request $request)
    {

        // $data = $request->validated();
        $data = $request->all();
        $imagePath = '';
        $ext = '';
        if ($request->hasFile('photo_url')) {
            $imagePath = $request->file('photo_url')->store('public/images');
        }

        $empDetail = EmpDetail::create([
            'emp_basic_id' => $data['emp_basic_id'],
            'address' => $data['address'],
            'city' => $data['city'],
            'sub_city' => $data['sub_city'],
            'kebele' => $data['kebele'],
            'house_no' => $data['house_no'],
            'country' => $data['country'],
            'phone' => $data['phone'],
            'photo_url' => $imagePath ? $imagePath : null,
            'user_id' => $request->input('user_id'),
            'height' => $request->height,
            'weight' => $request->weight,
            'user_id'=> $request->user_id,
        ]);
        if ($empDetail->photo_url) {
            $imageUrl = Storage::path($empDetail->photo_url);
            $imageFile = file_get_contents($imageUrl);
            $ext = pathinfo($imageUrl, PATHINFO_EXTENSION);
            $base64Image = base64_encode($imageFile);
            $empDetail['photo_url'] = $base64Image;
        }
        $empDetail['ext'] = $ext;
        return response($empDetail, 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EmpDetail  $empDetail
     * @return \Illuminate\Http\Response
     */
    public function show(EmpDetail $empDetail)
    {
        $ext = '';
        if ($empDetail->photo_url) {
            $imageUrl = Storage::path($empDetail->photo_url);
            $imageFile = file_get_contents($imageUrl);
            $ext = pathinfo($imageUrl, PATHINFO_EXTENSION);
            $base64Image = base64_encode($imageFile);
            $empDetail->photo_url = $base64Image;
        }
        $empDetail['ext'] = $ext;
        return response($empDetail);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateEmpDetailRequest  $request
     * @param  \App\Models\EmpDetail  $empDetail
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateEmpDetailRequest $request, EmpDetail $empDetail)
    {
        // $validated = $request->validated();
        $data = $request->all();
        // $merged = array_merge($validated, $request->all());
        $empDetail->update($data);
        
        return response(new EmpDetailResource($empDetail));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EmpDetail  $empDetail
     * @return \Illuminate\Http\Response
     */
    public function destroy(EmpDetail $empDetail)
    {

        $empDetail->delete();

        return response('', 201);
    }
}
