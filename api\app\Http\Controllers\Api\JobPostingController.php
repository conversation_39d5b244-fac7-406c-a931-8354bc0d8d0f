<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\JobPosting;
use Illuminate\Http\Request;

class JobPostingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return JobPosting::all();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'hire_requisition_id' => 'required|exists:hire_requisitions,id',
            'posting_date' => 'required|date',
            'closing_date' => 'required|date|after_or_equal:posting_date',
            'description' => 'required|string',
            'online_posting_url' => 'nullable|url',
        ]);

        $jobPosting = JobPosting::create($validatedData);
        return $jobPosting;
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        return JobPosting::find($id);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $jobPosting = JobPosting::find($id);

        $validatedData = $request->validate([
            'hire_requisition_id' => 'sometimes|required|exists:hire_requisitions,id',
            'posting_date' => 'sometimes|required|date',
            'closing_date' => 'sometimes|required|date|after_or_equal:posting_date',
            'description' => 'sometimes|required|string',
            'online_posting_url' => 'nullable|url',
        ]);

        $jobPosting->update($validatedData);
        return $jobPosting;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        return JobPosting::destroy($id);
    }
}
