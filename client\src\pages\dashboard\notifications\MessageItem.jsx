import { Delete as DeleteIcon } from '@mui/icons-material';
import MarkEmailReadOutlinedIcon from '@mui/icons-material/MarkEmailReadOutlined';
import {
  IconButton,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
} from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../../../api/config/axiosConfig';
import { useStateContext } from '../../../context/ContextProvider';

const MessageItem = ({
  readAt,
  companyName,
  contractSchedule,
  createdAt,
  id,
}) => {
  const queryClient = useQueryClient();
  const { setNotification } = useStateContext();
  const navigate = useNavigate();

  const onDeleteClick = () => {
    api.delete(`/notification/delete/${id}`).then(() => {
      queryClient.invalidateQueries({
        queryKey: ['notifications', 'notify'],
      });
      setNotification('notifcation is deleted');
    });
  };

  const markAsRead = () => {
    api.get(`/markAsRead/${id}`).then(() => {
      queryClient.invalidateQueries({
        queryKey: ['notifications', 'notify'],
      });
      setNotification('notifcations is marked as read');
    });
  };

  const handleOnClick = () => {
    navigate(`/messages/detail/${id}`);
  };

  return (
    <ListItem
      sx={{
        fontWeight: `${readAt == null ? 700 : undefined}`,
        borderBottom: '1px solid black',
        cursor: 'pointer',
        boxShadow: '0px 2px 3px rgba(0, 0, 0, 0.1)',
      }}
      onClick={handleOnClick}
    >
      <ListItemText
        primary={`notifcation message ${companyName} contract followup ${contractSchedule} Schedule reminder  is due please click the Message to find out more in detail`}
        secondary={`sent ${createdAt}`}
        color="black"
      />
      <ListItemIcon>
        <IconButton
          edge="start"
          onClick={onDeleteClick}
          aria-label="delete"
          size="small"
        >
          <DeleteIcon />
        </IconButton>
        {readAt ? (
          <Typography alignSelf="center" variant="body2">
            {readAt}
          </Typography>
        ) : (
          <IconButton
            edge="start"
            onClick={markAsRead}
            aria-label="markasread"
            size="small"
          >
            <MarkEmailReadOutlinedIcon />
          </IconButton>
        )}
      </ListItemIcon>
    </ListItem>
  );
};

export default MessageItem;
