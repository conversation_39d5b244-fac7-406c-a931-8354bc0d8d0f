<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class NonTaxableAllowanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'transport_allowance' => $this->transport_allowance,
            'desert_allowance' => $this->desert_allowance,
            'mobile_allowance' => $this->mobile_allowance,
            'position_allowance' => $this->position_allowance,
            'cleaning_allowance' => $this->cleaning_allowance,
            'medical_allowance' => $this->medical_allowance,
            'other_allowance' => $this->other_allowance,
            'effective_date' => $this->effective_date,

        ];
    }
}
