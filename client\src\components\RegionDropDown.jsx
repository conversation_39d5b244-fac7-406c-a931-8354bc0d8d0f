import { TextField, MenuItem, Box } from "@mui/material";
import React, { useState } from "react";
import address from "../pages/hr/registration_component/address"
const RegionDropDown = ({ onSelectedRegion }) => {
  const [selectedRegion, setSelectedRegion] = useState("");
  const handleCenterChange = (e) => {
    const centerValue = e.target.value;
    setSelectedRegion(centerValue);
    onSelectedRegion(centerValue);
  };
  return (
    <Box>
      <TextField
        select
        name="region"
        value={selectedRegion}
        onChange={handleCenterChange}
        fullWidth
        label="Select Region"
        // variant='standard'
        margin="normal"
        type="text"
        InputLabelProps={{
          color: "success",
        }}
      >
        {address.map((region) => (
          <MenuItem key={region.value} value={region.value}>
            {region.label}
          </MenuItem>
        ))}
      </TextField>
    </Box>
  );
};

export default RegionDropDown;
