import React from 'react';
import {
  <PERSON>,
  <PERSON>Field,
  MenuItem,
  useTheme,
  Button,
  Typography,
} from '@mui/material';

import { DataGrid } from '@mui/x-data-grid';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { Link } from 'react-router-dom';
import api from '../../../api/config/axiosConfig';
import { useStateContext } from '../../../context/ContextProvider';
import { tokens } from '../../../utils/theme';
import EmpSearchBar from '../../../components/EmpSearchBar';
import { useEmpData } from '../../../api/userApi/clinetHook';

function EmpLocation() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const [clientId, setClientId] = useState('');

  const queryClient = useQueryClient();

  const { data: emp_basics, isLoading } = useEmpData();

  const handleClientSelect = ({ value, clicked }) => {
    setClientId(value.id);
    console.log(value.id);
  };

  const { data: assign, isSuccess } = useQuery(
    ['assign', clientId],
    () =>
      api.get(`/assign/employee/${clientId}`).then(({ data }) => {
        console.log(data);
        return [data];
        //return data.map((row, index) => ({ ...row, tid: index + 1 }));
      }),
    {
      enabled: !!clientId,
    }
  );

  const columns = [
    {
      field: 'emp_basic_id',
      headerName: 'Employee ID',
    },
    {
      field: 'company_name',
      headerName: 'Company Name',
      flex: 1,
    },
    {
      field: 'company_code',
      headerName: 'Company Code',
      flex: 1,
    },
    {
      field: 'position',
      headerName: 'Position',
      flex: 1,
    },
    {
      field: 'assign_date',
      headerName: 'Assign Date',
      flex: 1,
    },
  ];

  //console.log(emp_basics);

  if (clientId) {
    console.log('Assign Data:', assign);
  }

  const getRowId = (row) => row.emp_basic_id;

  return (
    <Box>
      <EmpSearchBar client={emp_basics} onClientSelect={handleClientSelect} />

      <Box
        marginTop="10px"
        height="75vh"
        sx={{
          '& .MuiDataGrid-root': {
            border: 'none',
          },
          '& .MuiDataGrid-cell': {
            borderBottom: 'none',
          },
          '& .name-column--cell': {
            color: colors.greenAccent[300],
          },
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: colors.blueAccent[700],
            borderBottom: 'none',
          },
          '& .MuiDataGrid-virtualScroller': {
            backgroundColor: colors.primary[400],
          },
          '& .MuiDataGrid-footerContainer': {
            borderTop: 'none',
            backgroundColor: colors.blueAccent[700],
          },
          '& .MuiCheckBox-root': {
            color: `${colors.greenAccent[200]}!important`,
          },
          '& .MuiDataGrid-toolbarContainer .MuiButton-text': {
            color: `${colors.grey[100]}!important`,
          },
        }}
      >
        {isSuccess ? (
          assign.message ? (
            <Typography variant="h6" color="error">
              {assign.message}
            </Typography>
          ) : (
            <DataGrid columns={columns} rows={assign} hideFooter={true} />
          )
        ) : (
          <Typography variant="h6">
            Select a client to view assigned employees.
          </Typography>
        )}
      </Box>
    </Box>
  );
}

export default EmpLocation;
