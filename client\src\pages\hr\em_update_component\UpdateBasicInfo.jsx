import {
  Box,
  Grid,
  Paper,
  useTheme,
  TextField,
  MenuItem,
  Button,
} from '@mui/material';
import React from 'react';
import { useEffect } from 'react';
import { useState } from 'react';
import api from '../../../api/config/axiosConfig';
import { useStateContext } from '../../../context/ContextProvider';
import { tokens } from '../../../utils/theme';
import { usePositionData } from '../../../api/userApi/clinetHook';

const rateType = [
  {
    label: 'monthly',
    value: 'Month',
  },
  {
    label: 'dayliy',
    value: 'Daily',
  },
  {
    label: 'hourly',
    value: 'Hour',
  },
  {
    label: 'pcrate',
    value: 'Pcrate',
  },
];
function UpdateBasicInfo({ id }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const { errors, setErrors, setNotification, user } = useStateContext();
  const { data: positions, isFetched } = usePositionData();

  const [formData, setFormData] = useState({
    first_name: '',
    middle_name: '',
    last_name: '',
    amharic_name: '',
    company_id: '',
    tin_no: '',
    pension_id: '',
    file_no: '',
    start_date: '',
    gender: '',
    date_of_birth: '',
    position: '',
    basic_salary: '',
    rate_type: '',
    initial_salary: '',
    insurance: '',
    pension: '',
    provident: '',
    user_id: user.id,
  });

  useEffect(() => {
    setFormData({
      ...formData,
      user_id: user.id,
    })
  }, [user])

  const boolValues = [
    {
      lable: 'True',
      value: 1,
    },
    {
      lable: 'False',
      value: 0,
    },
  ];

  useEffect(() => {
    api.get(`emp_basics/${id}?all=true`).then(({ data }) => {
      setFormData(data.data);
    });
  }, [id]);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    api
      .put(`emp_basics/${id}`, formData)
      .then(() => {
        setNotification('updated successfully');
      })
      .catch((err) => {
        if (err.response && err.response.status === 422) {
          setErrors(err.response.data.errors);
        }
      });
  };
  
  return (
    <Paper
      variant="standard"
      sx={{
        color: colors.grey[100],
        backgroundColor: colors.primary[400],
      }}
    >
      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.first_name == ''}
              margin="normal"
              label="First Name"
              name="first_name"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.first_name}
              helperText={errors.first_name ? errors.first_name[0] : null}
            />
          </Grid>

          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.middle_name == ''}
              margin="normal"
              label="Middle Name"
              name="middle_name"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.middle_name}
              helperText={errors.middle_name ? errors.middle_name[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.last_name == ''}
              margin="normal"
              label="Last Name"
              name="last_name"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.last_name}
              helperText={errors.last_name ? errors.last_name[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.amharic_name == ''}
              margin="normal"
              label="Amharic Name"
              name="amharic_name"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.amharic_name}
              helperText={errors.amharic_name ? errors.amharic_name[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.company_id == ''}
              margin="normal"
              label="companyId"
              name="company_id"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.company_id}
              helperText={errors.company_id ? errors.company_id[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.tin_no == ''}
              margin="normal"
              label="Tin Number"
              name="tin_no"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.tin_no}
              helperText={errors.tin_no ? errors.tin_no[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.pension_id == ''}
              margin="normal"
              label="Pension Id"
              name="pension_id"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.pension_id}
              helperText={errors.tin_no ? errors.pension_id[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.file_no == ''}
              margin="normal"
              label="File Number"
              name="file_no"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.file_no}
              helperText={errors.file_no ? errors.file_no[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.start_date == ''}
              margin="normal"
              label="StartDate"
              name="start_date"
              fullWidth
              variant="standard"
              type="date"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.start_date}
              helperText={errors.start_date ? errors.start_date[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.gender == ''}
              margin="normal"
              label="Gender"
              name="gender"
              fullWidth
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.gender}
              helperText={errors.gender ? errors.gender[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.date_of_birth == ''}
              margin="normal"
              label="Date of Birth"
              name="date_of_birth"
              fullWidth
              variant="standard"
              type="date"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.date_of_birth}
              helperText={errors.date_of_birth ? errors.date_of_birth[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.position == ''}
              margin="normal"
              label="Position"
              name="position"
              fullWidth
              select
              variant="standard"
              type="text"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.position}
              helperText={errors.position ? errors.position[0] : null}
            >
              {isFetched ? (
                positions.map((val) => (
                  <MenuItem key={val.id} value={val.position_name}>
                    {val.position_name}
                  </MenuItem>
                ))
              ) : (
                <MenuItem value={''}>Select Positions</MenuItem>
              )}
            </TextField>
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.basic_salary == ''}
              margin="normal"
              label="BasicSalary"
              name="basic_salary"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.basic_salary}
              helperText={errors.basic_salary ? errors.basic_salary[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.rate_type == ''}
              margin="normal"
              label="Rate Type"
              name="rate_type"
              fullWidth
              variant="standard"
              type="text"
              select
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.rate_type}
              helperText={errors.rate_type ? errors.rate_type[0] : null}
            >
              {rateType.map((val) => (
                <MenuItem key={val.value} value={val.value}>
                  {val.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.initial_salary == ''}
              margin="normal"
              label="Initial Salary"
              name="initial_salary"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.initial_salary}
              helperText={
                errors.initial_salary ? errors.initial_salary[0] : null
              }
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.insurance == ''}
              margin="normal"
              label="Insurance"
              name="insurance"
              fullWidth
              variant="standard"
              type="text"
              defaultValue={boolValues[0][1]}
              select
              // size="small"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.insurance}
              helperText={errors.insurance ? errors.insurance[0] : null}
            >
              {boolValues.map((val) => (
                <MenuItem key={val.value} value={val.value}>
                  {val.lable}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.pension == ''}
              margin="normal"
              label="Penstion"
              name="pension"
              fullWidth
              variant="standard"
              select
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.pension}
              helperText={errors.pension ? errors.pension[0] : null}
            >
              {boolValues.map((val) => (
                <MenuItem key={val.value} value={val.value}>
                  {val.lable}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.provident == ''}
              margin="normal"
              label="Provident"
              name="provident"
              fullWidth
              variant="standard"
              select
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.provident}
              helperText={errors.provident ? errors.provident[0] : null}
            >
              {boolValues.map((val) => (
                <MenuItem key={val.value} value={val.value}>
                  {val.lable}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
        </Grid>
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button
            type="submit"
            sx={{ mt: 3, ml: 1, px: 2, py: 1 }}
            variant="contained"
          >
            update
          </Button>
        </Box>
      </Box>
    </Paper>
  );
}

export default UpdateBasicInfo;
