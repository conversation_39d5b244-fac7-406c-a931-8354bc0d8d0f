import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { getCoffeeBatches } from '../../api/coffee';
import { DataGrid } from '@mui/x-data-grid';
import { tokens } from '../../utils/theme';
import { useTheme } from '@mui/material/styles';

const CoffeeBatches = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['coffeeBatches'],
    queryFn: getCoffeeBatches,
  });

  const columns = [
    { field: 'id', headerName: 'ID', flex: 0.5 },
    { field: 'batch_number', headerName: 'Batch Number', flex: 1 },
    { field: 'coffee_type', headerName: 'Coffee Type', flex: 1 },
    { field: 'grade', headerName: 'Grade', flex: 1 },
    { field: 'quantity_kg', headerName: 'Quantity (kg)', flex: 1 },
    { field: 'status', headerName: 'Status', flex: 1 },
    {
      field: 'warehouse',
      headerName: 'Warehouse',
      flex: 1,
      valueGetter: (params) => params.row.warehouse?.name,
    },
  ];

  if (isLoading) {
    return <Typography>Loading...</Typography>;
  }

  if (isError) {
    return <Typography>Error: {error.message}</Typography>;
  }

  return (
    <Box m="20px">
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="h2" component="h1" gutterBottom>
          Coffee Batches
        </Typography>
        <Button
          variant="contained"
          sx={{ backgroundColor: colors.greenAccent[600] }}
        >
          Add New Batch
        </Button>
      </Box>
      <Box
        m="40px 0 0 0"
        height="75vh"
        sx={{
          '& .MuiDataGrid-root': {
            border: 'none',
          },
          '& .MuiDataGrid-cell': {
            borderBottom: 'none',
          },
          '& .name-column--cell': {
            color: colors.greenAccent[300],
          },
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: colors.blueAccent[700],
            borderBottom: 'none',
          },
          '& .MuiDataGrid-virtualScroller': {
            backgroundColor: colors.primary[400],
          },
          '& .MuiDataGrid-footerContainer': {
            borderTop: 'none',
            backgroundColor: colors.blueAccent[700],
          },
        }}
      >
        <DataGrid
          rows={data?.data || []}
          columns={columns}
          pageSize={10}
          rowsPerPageOptions={[10]}
          checkboxSelection
        />
      </Box>
    </Box>
  );
};

export default CoffeeBatches;
