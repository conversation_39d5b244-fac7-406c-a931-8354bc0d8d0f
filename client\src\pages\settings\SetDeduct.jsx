import { Api } from "@mui/icons-material";
import { TextField, Box, useTheme, Button } from "@mui/material";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import React, { useState } from "react";
import CustomPopOver from "../../components/CustomPopOver";
import { tokens } from "../../utils/theme";
import api from "../../api/config/axiosConfig";
import { useStateContext } from "../../context/ContextProvider";
function SetDeduct({ isopen, anchorEl, handleClose }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const { data: deduct } = useQuery(
    ["deduct"],
    async () =>
      await api.get("deduct").then(({ data }) => {
        setSocialFund(data.social_fund);
        return data;
      }),
    {
      staleTime: 600000,
    }
  );
  const createDeduct = useMutation((data) => api.post("deduct", data), {
    onSuccess: () => {
      setNotification("dedcut created successfully");
      queryClient.invalidateQueries(["deduct"]);
    },
  });
  const [socialfund, setSocialFund] = useState("");
  const handleSubmit = (e) => {
    e.preventDefault();
    const payload = {
      social_fund: socialfund,
    };
    if (deduct.id) {
      api.put(`deduct/${deduct.id}`, payload).then(() => {
        setNotification("dedcut updated successfully");
        queryClient.invalidateQueries(["deduct"]);
        handleClose()
      });
    } else {
      createDeduct.mutate(payload);
      handleClose()
    }
  };

  return (
    <CustomPopOver
      isopen={isopen}
      anchorEl={anchorEl}
      handleClose={handleClose}
    >
      <Box
        component="form"
        onSubmit={handleSubmit}
        sx={{
          background: colors.primary[400],
          color: colors.grey[100],
          padding: 2,
        }}
      >
        <TextField
          label="set deductable"
          name="social_fund"
          onChange={(val) => setSocialFund(val.target.value)}
          value={socialfund}
          InputLabelProps={{
            color: "success",
          }}
          type="number"
        />
        <Box sx={{ pt: 1, display: "flex", justifyContent: "space-between" }}>
          <Button onClick={handleClose} sx={{ color: colors.grey[100] }}>
            cancel
          </Button>
          <Button type="submit" sx={{ color: colors.grey[100] }}>
            submit
          </Button>
        </Box>
      </Box>
    </CustomPopOver>
  );
}

export default SetDeduct;
