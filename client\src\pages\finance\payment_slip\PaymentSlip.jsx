import { Box, Grid, Paper, useTheme } from "@mui/material";
import React, { useState } from "react";
import Header from "../../../components/Header";
import { tokens } from "../../../utils/theme";
import { useQuery } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";
import SearchBar from "../../../components/SearchBar";
import { useNavigate } from "react-router-dom";

function PaymentSlip() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const navigate = useNavigate();
  const { data: client, isFetched } = useQuery(
    ["client"],
    () => api.get("client").then(async ({ data }) => await data.data),
    { refetchOnWindowFocus: false, staleTime: 60000 }
  );
  const handleClientSelect = ({ value, clicked }) => {
    if (clicked) {
      navigate("/finance/slip/list", {
        state: { client_id: value.id },
      });
    }
  };
  return (
    <Box m={2}>
      <Header
        title="Payment Slip"
        subtitle="prepare payment slip for a client "
      />
      <Grid container spacing={2}>
        <Grid item xs={12} sm={5}>
          <Paper
            sx={{
              padding: "1rem",
              color: colors.grey[100],
              background: colors.primary[400],
            }}
          >
            <SearchBar client={client} onClientSelect={handleClientSelect} />
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}

export default PaymentSlip;
