<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use App\Models\ActivityLog; // Ensure you have the correct namespace for your model

class LogRequest
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {

        // Define the URLs to exclude from logging
        $excludedPaths = [
            '/api/v1/activity-logs',
            '/api/v1/countNotification',
            '/api/v1/notifications/unread',
            '/api/v1/user',
        ];

        if (collect($excludedPaths)->contains(fn($path) => Str::contains($request->fullUrl(), $path))) {
            return $next($request);
        }

        // Check if the user is authenticated
        $userId = Auth::check() ? Auth::id() : null;


        // Log the request details BEFORE processing the request
        // Log::info('Incoming Request', [
        //     'user_id' => $userId,
        //     'method' => $request->method(),
        //     'url' => $request->fullUrl(),
        //     'ip' => $request->ip(),
        //     'headers' => $request->headers->all(), // Log all headers
        //     'body' => $request->all(), // Log the request body
        // ]);

        // Process the request (pass it to the next middleware/controller)
        //$response = $next($request);

        // Log information AFTER processing the request (e.g., response status)
        //  Log::info('Outgoing Response', [
        //     'user_id' => $userId,
        //     'status_code' => $response->status(),
        //  ]);

        $response = $next($request);

        $logData =  [
            'user_id' => $userId,
            'method' => $request->method(),
            'url' => $request->fullUrl(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->header('User-Agent'),
            //'request_body' => json_encode($request->all()), // Store request body as JSON
            'request_body' => json_encode($request->except(['password', 'password_confirmation'])),
            'response_status' => $response->status(),
        ];

        try {
            // Create the log entry in the database
            ActivityLog::create($logData);
        } catch (\Exception $e) {
            // Log error if database saving fails, but don't stop the response
            Log::error('Failed to save activity log to database.', [
                'error' => $e->getMessage(),
                'data' => $logData
            ]);
        }

        return $response;
    }
}
