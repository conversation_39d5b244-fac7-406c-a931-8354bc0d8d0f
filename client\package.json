{"name": "edomias-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --port=4000", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/icons-material": "^5.11.0", "@mui/material": "^5.11.7", "@mui/x-data-grid": "^5.17.21", "@tanstack/react-query": "^4.24.4", "axios": "^1.3.1", "date-fns": "^4.1.0", "localforage": "^1.10.0", "lodash.debounce": "^4.0.8", "match-sorter": "^6.3.1", "minisearch": "^6.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-pro-sidebar": "^0.7.1", "react-router-dom": "^6.8.0", "sort-by": "^0.0.2"}, "devDependencies": {"@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "@vitejs/plugin-react": "^3.1.0", "vite": "^4.0.0"}}