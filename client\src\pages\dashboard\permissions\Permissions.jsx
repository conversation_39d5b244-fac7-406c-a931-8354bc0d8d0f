import React from "react";
import {
  <PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  Container,
  Grid,
  Typography,
  useTheme,
} from "@mui/material";
import { useStateContext } from "../../../context/ContextProvider";
import { tokens } from "../../../utils/theme";
import { routes } from "../../../helper/routes";
const Permissions = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { user } = useStateContext();
  const userRole = user.role;
  const paths = user.user_permission;

  if (userRole == null) {
    return (
      <Container sx={{ display: "flex", justifyContent: "center" }}>
        <CircularProgress />
      </Container>
    );
  }
  if (userRole && paths[0].routes) {
    const userRoutes = [];
    const getRoutes = routes.find((route) => route.role == userRole);
    for (const apiPath of JSON.parse(paths[0].routes)) {
      const matchingRoutes = getRoutes.children.find(
        (obj) => obj.path === apiPath
      );
      if (matchingRoutes) {
        userRoutes.push(matchingRoutes);
      }
    }
    return (
      <Box>
        <Grid container spacing={2} sx={{ marginTop: 2 }}>
          {userRoutes
            .sort((a, b) => a.element.localeCompare(b.element))
            .map((route, index) => (
              <Grid item sm={2} key={index}>
                <Card
                  variant="elevation"
                  style={{
                    color: colors.grey,
                    background: colors.primary[400],
                    padding: 3,
                    cursor: "pointer",
                  }}
                >
                  <CardContent>
                    <Typography
                      sx={{
                        overflowWrap: "break-word",
                        wordWrap: "break-word",
                      }}
                    >
                      {route.element}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
        </Grid>
      </Box>
    );
  }
};

export default Permissions;
