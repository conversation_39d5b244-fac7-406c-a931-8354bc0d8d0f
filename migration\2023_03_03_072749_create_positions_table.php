<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('positions', function (Blueprint $table) {
            $table->id();
            $table->string('position_name');
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });

        $path = Storage::path('positions.json');

        $data = file_get_contents($path);
        $decoded = json_decode($data, true);
        //maping user_id to null 
        foreach ($decoded as &$item) {
            $item['user_id'] = null;
        }
        DB::table('positions')->insert($decoded);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('positions');
    }
};
