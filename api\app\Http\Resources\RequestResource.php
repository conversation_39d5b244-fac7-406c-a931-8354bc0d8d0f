<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class RequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user' => new UserResource($this->whenLoaded('user')),
            'item' => new StoreItemResource($this->whenLoaded('item')),
            'quantity' => $this->quantity,
            'status' => $this->status,
            'notes' => $this->notes,
            'approval' => new ApprovalResource($this->whenLoaded('approval')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}