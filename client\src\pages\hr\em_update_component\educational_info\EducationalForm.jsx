import React from "react";
import { Box, Grid, TextField, Button, MenuItem } from "@mui/material";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useStateContext } from "../../../../context/ContextProvider";
import { useState } from "react";
import api from "../../../../api/config/axiosConfig";
import { useEffect } from "react";
import { LevelType, certefication } from "./constant";

const EducationalForm = ({ empId, data }) => {
  const { errors, setErrors, setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    emp_basic_id: "",
    institution: "",
    level: "",
    grade: "",
    certefication: "",
    start_date: "",
    end_date: "",
  });
  if (data) {
    useEffect(() => {
      setFormData(data);
    }, [data]);
  }
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  const educationMutation = useMutation(
    (payload) => api.post(`emp_education`, payload),
    {
      onSuccess: () => {
        setNotification("Information created successfully");
        queryClient.invalidateQueries({ queryKey: "education" });
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );
  const updateEducation = useMutation(
    (formData) => api.put(`emp_education/${formData.id}`, formData),
    {
      onSuccess: () => {
        setNotification("Information updated successfully");
        queryClient.invalidateQueries({ queryKey: "education" });
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );
  const handleSubmit = (e) => {
    e.preventDefault();

    if (data && data.id) {
      updateEducation.mutate(formData);
    } else {
      const form = new FormData(e.currentTarget);
      const payload = {
        emp_basic_id: empId,
        institution: form.get("institution"),
        level: form.get("level"),
        grade: form.get("grade"),
        certefication: form.get("certefication"),
        start_date: form.get("start_date"),
        end_date: form.get("end_date"),
      };
      educationMutation.mutate(payload);
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.institution == ""}
            margin="normal"
            label="Institution"
            name="institution"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.institution}
            helperText={errors.institution ? errors.institution[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.level == ""}
            margin="normal"
            label="Level"
            name="level"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.level ?? "university"}
            helperText={errors.level ? errors.level[0] : null}
          >
            {LevelType.map((value) => (
              <MenuItem key={value.value} value={value.value}>
                {value.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.grade == ""}
            margin="normal"
            label="Grade"
            name="grade"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.grade}
            helperText={errors.grade ? errors.grade[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.certefication == ""}
            margin="normal"
            label="Certefication"
            name="certefication"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.certefication ?? "degree"}
            helperText={errors.certefication ? errors.certefication[0] : null}
          >
            {certefication.map((value) => (
              <MenuItem key={value.value} value={value.value}>
                {value.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.start_date == ""}
            margin="normal"
            label="Start Date"
            name="start_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.start_date}
            helperText={errors.start_date ? errors.start_date[0] : null}
          />
        </Grid>

        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.end_date == ""}
            margin="normal"
            label="Loan Reason"
            name="end_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.end_date}
            helperText={errors.end_date ? errors.end_date[0] : null}
          />
        </Grid>
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
          {data && data.id ? <div>update</div> : <div>create</div>}
        </Button>
      </Box>
    </Box>
  );
};

export default EducationalForm;
