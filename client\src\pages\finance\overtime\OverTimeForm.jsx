import React from "react";
import { Box, Grid, Button, TextField, MenuItem } from "@mui/material";
import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";
import { useStateContext } from "../../../context/ContextProvider";
import { useEffect } from "react";
const dayType = [
  {
    label: "Normal Day",
    value: "Normal Day",
  },
  {
    label: "Midnight",
    value: "Mid Night",
  },
  {
    label: "Holiday",
    value: "Holiday",
  },
  {
    label: "Weekend",
    value: "Weekend",
  },
];

const rateType = [
  {
    label: "Days",
    value: "Days",
  },
  {
    label: "Hours",
    value: "Hour",
  },
];
function OverTimeForm({ formdata, editData, setEditData }) {
  const queryClient = useQueryClient();
  
  const [overtime, setOvertime] = useState({
    start_date: "",
    end_date: "",
    rate_type: "",
    day_type: "",
    hours: "",
    minutes: 0,
    overtime_remark: "",
  });
  if (editData) {
    useEffect(() => {
      setOvertime(editData);
    }, [editData]);
  }
  const { setNotification, user } = useStateContext();
  const handleChange = (e) => {
    setOvertime({
      ...overtime,
      [e.target.name]: e.target.value,
    });
  };

  const createOverTime = useMutation((data) => api.post("overtime", data), {
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: "overtime" });
      setNotification("overtime is inserted succcessfully");
    },
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    const remark = data.get("overtime_remark") ? data.get("overtime_remark") : "default remark";

    const payload = {
      emp_basic_id: formdata.emp_basic_id,
      client_id: formdata.client_id,
      start_date: data.get('start_date'),
      end_date: data.get('end_date'),
      rate_type: data.get('rate_type'),
      day_type: data.get('day_type'),
      hours: data.get('hours'),
      minutes: data.get('minutes'),
      overtime_remark: remark,
      user_id: user.id,
    };

    if (editData && editData.id) {
      api.put(`overtime/${editData.id}`, payload).then(() => {
        setNotification("overtime is updated succcessfully");
        setEditData({});
        queryClient.invalidateQueries({ queryKey: "overtime" });
      });
    } else {
      console.log(payload);
      createOverTime.mutate(payload);
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={1}>
        <Grid item xs={12} sm={2}>
          <TextField
            margin="normal"
            label="Starting Date"
            name="start_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={overtime.start_date || new Date().toISOString().slice(0, 10)}
          />
        </Grid>
        <Grid item xs={12} sm={2}>
          <TextField
            margin="normal"
            label="Ending Date"
            name="end_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={overtime.end_date || new Date().toISOString().slice(0, 10)}
          />
        </Grid>
        <Grid item xs={12} sm={2}>
          <TextField
            margin="normal"
            label="Hours or Days "
            name="hours"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={overtime.hours}
          />
        </Grid>
        <Grid item xs={12} sm={2}>
          <TextField
            margin="normal"
            label="Minutes"
            name="minutes"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={overtime.minutes}
          />
        </Grid>
        <Grid item xs={12} sm={2}>
          <TextField
            margin="normal"
            label="Rate Type"
            name="rate_type"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={overtime.rate_type ? overtime.rate_type : "days"}
          >
            {rateType.map((rate) => (
              <MenuItem key={rate.value} value={rate.value}>
                {rate.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={2}>
          <TextField
            margin="normal"
            label="Day Type"
            name="day_type"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={overtime.day_type ? overtime.day_type : "normalday"}
          >
            {dayType.map((value) => (
              <MenuItem key={value.value} value={value.value}>
                {value.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>

        <Grid item xs={12} sm={2}>
          <TextField
            margin="normal"
            label="Overtime Remark"
            name="overtime_remark"
            multiline
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={overtime.overtime_remark}
          />
        </Grid>
      </Grid>
      <span style={{ float: "inline-start" }}>
        <Button type="submit" variant="contained" color="success">
          {editData.id ?<>update</>:<>create</>}
        </Button>
      </span>
    </Box>
  );
}

export default OverTimeForm;
