import React from 'react'
import { Box, TextField } from "@mui/material";
import Header from "../../../components/Header";
function Summary() {
  return (
    <Box>
    <Header heading="h6" title="Summary" />
    <TextField
      label="Taxable Income (BS + OT + HO + AD + TA + OA + APRS + DSA - AB-ADU):"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Total Deductions ( IT + PF + AD + TAD):"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Non Taxable Income (TA + DA + PA +DSA +MA ) :"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Net Payment (TI + NTI - TD):"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
  </Box>
  )
}

export default Summary