<?php

namespace App\Http\Controllers\Api\store;

use App\Http\Controllers\Controller;
use App\Http\Resources\RequestResource;
use App\Models\Request as StoreRequest;
use App\Models\User;
use App\Notifications\ItemRequestNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;

class RequestController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return RequestResource::collection(StoreRequest::with(['item', 'user'])->get());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'item_id' => 'required|exists:store_items,id',
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string',
        ]);

        $storeRequest = StoreRequest::create([
            'user_id' => Auth::id(),
            'item_id' => $request->item_id,
            'quantity' => $request->quantity,
            'notes' => $request->notes,
            'status' => 'pending',
        ]);

        $users = User::whereIn('role', ['admin', 'storekeeper'])->get();
        Notification::send($users, new ItemRequestNotification($storeRequest));

        return new RequestResource($storeRequest);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(StoreRequest $request)
    {
        return new RequestResource($request->load(['item', 'user', 'approval']));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, StoreRequest $storeRequest)
    {
        $request->validate([
            'status' => 'sometimes|required|in:pending,approved,rejected',
            'notes' => 'nullable|string',
        ]);

        $storeRequest->update($request->all());

        return new RequestResource($storeRequest);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(StoreRequest $request)
    {
        $request->delete();

        return response()->noContent();
    }
}
