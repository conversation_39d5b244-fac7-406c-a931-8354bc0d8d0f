<?php

namespace App\Http\Controllers\Api\settings;

use App\Http\Controllers\Controller;
use App\Models\settings\MedicalShare;
use Illuminate\Http\Request;

class MedicalShareController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(MedicalShare::latest()->first());;
    }



    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = MedicalShare::create([
            'medical_share' =>    $request->input('medical_share')
        ]);

        return response($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\settings\MedicalShare  $medicalShare
     * @return \Illuminate\Http\Response
     */
    public function show(MedicalShare $medicalShare)
    {
        //
    }

 

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\settings\MedicalShare  $medicalShare
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, MedicalShare $medicalShare)
    {
        $medicalShare->update([
            'medical_share'=>$request->input('medical_share')
        ]);

        return response($medicalShare);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\settings\MedicalShare  $medicalShare
     * @return \Illuminate\Http\Response
     */
    public function destroy(MedicalShare $medicalShare)
    {
        $medicalShare->delete();

        return response('',204);
    }
}

