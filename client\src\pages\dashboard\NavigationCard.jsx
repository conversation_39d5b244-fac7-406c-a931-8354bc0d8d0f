import {
  Card,
  useTheme,
  CardContent,
  Typography,
  Box,
  Grid,
  Divider,
  CircularProgress,
} from "@mui/material";
import React from "react";

import { useNavigate } from "react-router-dom";
import { tokens } from "../../utils/theme";
const NavigationCard = ({ content, url,title='' }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const navigate = useNavigate();
  const handleClick = () => {
    navigate(url);
  };
  return (
    <Card
      variant="elevation"
      style={{
        color: colors.grey,
        background: colors.primary[400],
        padding: 5,
        cursor: "pointer",
      }}
      onClick={handleClick}
    >
      <CardContent>
        <Box display="flex flexDirection:column">
          <Typography variant="body2" color={colors.grey[100]}>
          {title}
          </Typography>
          <Typography variant="h3" color={colors.blueAccent[500]}>
          {content}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default NavigationCard;
