import { Box, Button, Grid, MenuItem, TextField } from '@mui/material';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import React, { useEffect, useState } from 'react';
import api from '../../../api/config/axiosConfig';
import { useStateContext } from '../../../context/ContextProvider';

const penalityRate = [
  {
    label: '1 times',
    value: 1,
  },
  {
    label: '2 times',
    value: 2,
  },
  {
    label: '3 times',
    value: 3,
  },
  {
    label: '4 times',
    value: 4,
  },
  {
    label: '5 times',
    value: 5,
  },
  {
    label: '6 times',
    value: 6,
  },
  {
    label: '7 times',
    value: 7,
  },
  {
    label: '8 times',
    value: 8,
  },
];

const rateType = [
  {
    label: 'Days',
    value: 'Days',
  },
  {
    label: 'Hours',
    value: 'Hour',
  },
];
function AbsenceForm({ formData, editedData, setEditData }) {
  const [absence, setAbsence] = useState({
    start_date: '',
    end_date: '',
    rate_type: '',
    hour_day: '',
    minutes: '0',
    penality_rate: '',
    absence_remark: '',
  });

  useEffect(() => {
    if (editedData) {
      setAbsence(editedData);
    }
  }, [editedData]);

  const queryClient = useQueryClient();
  const { setNotification, user } = useStateContext();

  const handleChange = (e) => {
    setAbsence({
      ...absence,
      [e.target.name]: e.target.value,
    });
  };

  const createAbsence = useMutation((data) => api.post('absence', data), {
    onSuccess: () => {
      setNotification('absence inserted successfully');
      queryClient.invalidateQueries({ queryKey: 'absence' });
    },
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    const remark = data.get('absence_remark')
      ? data.get('absence_remark')
      : 'default remark';

    const payload = {
      emp_basic_id: formData.emp_basic_id,
      client_id: formData.client_id,
      start_date: data.get('start_date'),
      end_date: data.get('end_date'),
      rate_type: data.get('rate_type'),
      penality_rate: data.get('penality_rate'),
      hour_day: data.get('hour_day'),
      minutes: data.get('minutes'),
      absence_remark: remark,
      user_id: user.id,
    };

    if (editedData && editedData.id) {
      api.put(`absence/${editedData.id}`, payload).then(() => {
        setNotification('absence recorded updated successfully');
        setEditData({});
        setAbsence({
          start_date: '',
          end_date: '',
          rate_type: '',
          hour_day: '',
          minutes: 0,
          penality_rate: '',
          absence_remark: '',
          user_id: '',
        });
        queryClient.invalidateQueries({ queryKey: 'absence' });
      });
    } else {
      createAbsence.mutate(payload);
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Starting Date"
            name="start_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={absence.start_date || new Date().toISOString().slice(0, 10)}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Ending Date"
            name="end_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={absence.end_date || new Date().toISOString().slice(0, 10)}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Hours or Days "
            name="hour_day"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={absence.hour_day}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Minutes"
            name="minutes"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={absence.minutes}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Rate Type"
            name="rate_type"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={absence.rate_type ? absence.rate_type : 'days'}
          >
            {rateType.map((rate) => (
              <MenuItem key={rate.value} value={rate.value}>
                {rate.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Penality rate"
            name="penality_rate"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={absence.penality_rate ? absence.penality_rate : 1}
          >
            {penalityRate.map((value) => (
              <MenuItem key={value.value} value={value.value}>
                {value.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>

        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Absence Remark"
            name="absence_remark"
            multiline
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={absence.absence_remark}
          />
        </Grid>
      </Grid>
      <span style={{ float: 'inline-start' }}>
        <Button type="submit" variant="contained" color="success">
          {editedData.id ? <>update</> : <>create</>}
        </Button>
      </span>
    </Box>
  );
}

export default AbsenceForm;
