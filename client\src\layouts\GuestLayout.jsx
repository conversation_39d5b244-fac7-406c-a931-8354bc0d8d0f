import React from "react";
import { Link, Navigate, Outlet } from "react-router-dom";
import { ColorModeContext, useMode } from "../utils/theme";
import {
  Box,
  Button,
  CssBaseline,
  ThemeProvider,
  Typography,
} from "@mui/material";
import LightMode from "../components/LightMode";
import { useStateContext } from "../context/ContextProvider";
import logo from '../assets/edomias_log.png';
function GuestLayout() {
  const { token } = useStateContext();
  const [theme, colorMode] = useMode();
  if (token) {
    return <Navigate to="/dashboard" />;
  }
  return (
    <ColorModeContext.Provider value={colorMode}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <div className="app">
          <main className="content">
            <Box display="flex" justifyContent="space-between" m={2}>
              <Button
                sx={{
                  color: theme.palette.mode === "dark" ? "white" : "black",
                }}
              >
                 <img
                  src={logo}
                  alt="logo"
                  width="60px"
                  height="60px"
                />
                <Typography
                  variant="h6"
                  marginLeft="12px"
                  textAlign="start"
                  fontWeight="bold"
                  color="success"
                >
                  EDOMIAS SECURITY SERVICE
                </Typography>
              </Button>
              <Box display="flex" justifyContent="flex-end">
                <LightMode />
                <Button
                  LinkComponent={Link}
                  to="/login"
                  sx={{
                    color: theme.palette.mode === "dark" ? "white" : "black",
                    ml: 1,
                  }}
                >
                  <Typography variant="h6">Sign in</Typography>
                </Button>
              </Box>
            </Box>
            <Outlet />
          </main>
        </div>
      </ThemeProvider>
    </ColorModeContext.Provider>
  );
}

export default GuestLayout;
