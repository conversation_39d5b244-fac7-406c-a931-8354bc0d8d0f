import React, { useState } from "react";
import { TextField, useTheme, MenuItem } from "@mui/material";
import { tokens } from "../utils/theme";
function EmployeeSelecter({ assignedEmployee, isFetched, onEmployeeSelect }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [employeeId, setEmployeeId] = useState("");
  const handleOnEmployeeSelect = (value) => {
    onEmployeeSelect({ value: value, clicked: true });
  };
  
  return (
    <TextField
      margin="normal"
      label="select Employee"
      name="emp_basic_id"
      fullWidth
      select
      variant="standard"
      type="text"
      InputLabelProps={{
        color: "success",
      }}
      onChange={(e) => setEmployeeId(e.target.value)}
      value={employeeId ? employeeId : ""}
    >
      {isFetched && assignedEmployee ? (
        assignedEmployee.map((val) => (
          <MenuItem
            key={val.id}
            value={val.id}
            onClick={() => {
              handleOnEmployeeSelect(val);
            }}
          >
            {val.first_name} {val.middle_name} {val.last_name}
          </MenuItem>
        ))
      ) : (
        <MenuItem>Select Employee</MenuItem>
      )}
    </TextField>
  );
}

export default EmployeeSelecter;
