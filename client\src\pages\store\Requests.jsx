import { AddOutlined } from '@mui/icons-material';
import {
  Button,
  Typography,
  useTheme,
  Box,
  IconButton,
  Grid,
  TextField,
  Modal,
  MenuItem,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import api from '../../api/config/axiosConfig';
import Header from '../../components/Header';
import { useStateContext } from '../../context/ContextProvider';
import { tokens } from '../../utils/theme';
import _CircularProgress from '../../components/_CircularProgress';

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
};

function Requests() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState({});
  const { setNotification, errors, setErrors } = useStateContext();

  const { data: requests, isLoading } = useQuery(
    ['requests'],
    async () =>
      await api.get('store/requests').then(({ data }) => {
        return data.data;
      })
  );

  const { data: items } = useQuery(
    ['items'],
    async () =>
      await api.get('store/items').then(({ data }) => {
        return data.data;
      })
  );

  const columns = [
    {
      field: 'id',
      headerName: 'Id',
    },
    {
      field: 'item_name',
      headerName: 'Item Name',
      flex: 1,
      valueGetter: (params) => params.row.item.name,
    },
    {
      field: 'quantity',
      headerName: 'Quantity',
      flex: 1,
    },
    {
      field: 'status',
      headerName: 'Status',
      flex: 1,
    },
    {
      field: 'user_name',
      headerName: 'Requested By',
      flex: 1,
      valueGetter: (params) => params.row.user.firstName,
    },
    {
      field: 'notes',
      headerName: 'Notes',
      flex: 1,
    },
  ];

  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    setOpen(false);
    setSelectedRow({});
  };

  const handleChange = (e) => {
    setSelectedRow({
      ...selectedRow,
      [e.target.name]: e.target.value,
    });
  };

  const postRequest = useMutation(
    (data) => api.post('store/requests', data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['requests']);
        setNotification('Request created successfully');
        handleClose();
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );

  const onSubmit = (e) => {
    e.preventDefault();
    const payload = {
      item_id: selectedRow.item_id,
      quantity: selectedRow.quantity,
      notes: selectedRow.notes,
    };
    postRequest.mutate(payload);
  };

  return (
    <Box m="10px">
      <Header title="Requests" subtitle="List of item requests" />
      <Box display="flex" justifyContent="center" alignItems="center">
        <IconButton
          sx={{
            boxShadow: '2px 2px 4px rgba(0,0,0,0.25)',
            background: colors.primary[400],
          }}
          onClick={handleOpen}
        >
          <AddOutlined color={colors.grey[100]} />
        </IconButton>
      </Box>
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={style} component="form" onSubmit={onSubmit}>
          <Typography id="modal-modal-title" variant="h6" component="h2">
            Add Request
          </Typography>
          <TextField
            select
            error={!errors.item_id == ''}
            id="item_id"
            margin="normal"
            fullWidth
            label="Item"
            name="item_id"
            InputLabelProps={{
              color: 'success',
            }}
            value={selectedRow.item_id || ''}
            onChange={handleChange}
            helperText={errors.item_id ? errors.item_id[0] : null}
          >
            {items &&
              items.map((item) => (
                <MenuItem key={item.id} value={item.id}>
                  {item.name}
                </MenuItem>
              ))}
          </TextField>
          <TextField
            error={!errors.quantity == ''}
            id="quantity"
            margin="normal"
            fullWidth
            label="Quantity"
            name="quantity"
            type="number"
            InputLabelProps={{
              color: 'success',
            }}
            value={selectedRow.quantity || ''}
            onChange={handleChange}
            helperText={errors.quantity ? errors.quantity[0] : null}
          />
          <TextField
            error={!errors.notes == ''}
            id="notes"
            margin="normal"
            fullWidth
            label="Notes"
            name="notes"
            InputLabelProps={{
              color: 'success',
            }}
            value={selectedRow.notes || ''}
            onChange={handleChange}
            helperText={errors.notes ? errors.notes[0] : null}
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 2, py: 2 }}
            color="success"
            size="small"
          >
            submit
          </Button>
        </Box>
      </Modal>
      <Box
        m="10px 0 0 0"
        height="67vh"
        sx={{
          '& .MuiDataGrid-root': {
            border: 'none',
          },
          '& .MuiDataGrid-cell': {
            borderBottom: 'none',
          },
          '& .name-column--cell': {
            color: colors.greenAccent[300],
          },
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: colors.blueAccent[700],
            borderBottom: 'none',
          },
          '& .MuiDataGrid-virtualScroller': {
            backgroundColor: colors.primary[400],
          },
          '& .MuiDataGrid-footerContainer': {
            borderTop: 'none',
            backgroundColor: colors.blueAccent[700],
          },
          '& .MuiCheckBox-root': {
            color: `${colors.greenAccent[200]}!important`,
          },
          '& .MuiDataGrid-toolbarContainer .MuiButton-text': {
            color: `${colors.grey[100]}!important`,
          },
        }}
      >
        {isLoading ? (
          <_CircularProgress />
        ) : (
          <DataGrid columns={columns} rows={requests} />
        )}
      </Box>
    </Box>
  );
}

export default Requests;
