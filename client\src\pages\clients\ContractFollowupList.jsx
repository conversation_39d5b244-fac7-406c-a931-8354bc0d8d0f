import {
  Box,
  Button,
  CircularProgress,
  Typography,
  useTheme,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useEffect } from 'react';
import api from '../../api/config/axiosConfig';
import Header from '../../components/Header';
import { useStateContext } from '../../context/ContextProvider';
import { tokens } from '../../utils/theme';

function ContractFollowupList({ clientId, selectedRow }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const queryClient = useQueryClient();

  const {
    data: ContractByClient,
    isLoading,
    isFetched,
  } = useQuery(
    ['ContractByClient', clientId],
    async () =>
      await api
        .get(`ContractByClient/${clientId}`)
        .then(({ data }) => data.data),
    {
      enabled: !!clientId,
      staleTime: 60000,
    }
  );

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      flex: 0.2,
    },
    {
      field: 'start_date',
      headerName: 'Start Date',
      flex: 0.5,
    },
    {
      field: 'end_date',
      headerName: 'To Date',
      flex: 0.5,
    },
    {
      field: 'contract_schedule',
      headerName: 'Contract Schedule',
      flex: 0.8,
    },
    {
      field: 'reminder',
      headerName: 'Reminder Date',
      flex: 0.5,
    },
    {
      field: 'amount',
      headerName: 'Amount',
      flex: 0.5,
    },
    {
      field: 'message',
      headerName: 'Message',
      flex: 0.5,
    },
    {
      field: 'edit',
      headerName: 'Edit',
      flex: 0.7,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          selectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: 'delete',
      headerName: 'Delete',
      flex: 0.8,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deletContractFollowUp = useMutation(
          (id) => api.delete(`ContractDeleteByClient/${id}`),
          {
            onSuccess: () => {
              queryClient.invalidateQueries({
                queryKey: 'ContractByClient',
              });
              setNotification('Contract Follow Up record deleted successfully');
            },
          }
        );
        const deleteContractFollowUp = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              'Are you sure you want to delete the selected Contract Follow Up?'
            )
          ) {
            return;
          }

          deletContractFollowUp.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deleteContractFollowUp}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];

  if (isFetched && ContractByClient.length > 0) {
    console.log(ContractByClient);
  }

  return (
    <>
      <Header title="list of contract follow up record" heading="h5" />

      {isLoading && <CircularProgress />}
      {isFetched && (
        <DataGrid
          columns={columns}
          hideFooter={true}
          autoHeight={true}
          rows={ContractByClient ? ContractByClient : []}
        />
      )}
    </>
  );
}

export default ContractFollowupList;
