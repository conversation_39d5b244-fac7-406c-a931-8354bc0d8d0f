import {TextField, useTheme, Autocomplete } from "@mui/material";
import { useState } from "react";
import Minisearch from "minisearch";
import { tokens } from "../utils/theme";
import { useClientData, useClientCodeData } from "../api/userApi/clinetHook";

function SearchByCompName({ onClientSelect, variant}) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [selected, setSelected] = useState(0);
  const [clientId, setClientId] = useState("");
  const [results, setResults] = useState([]);
  const { data: clientCode, isFetched } = useClientCodeData();
  //const { data: client, isFetched: clientFetched } = useClientData();

  const miniSearch = new Minisearch({
    fields: ["company_code"],
    storeFields: ["id", "company_code"],
  });


  if (clientCode) {
    //console.log(clientCode);
    miniSearch.addAll(clientCode);
  }

  const handleChange = (e, value) => {
    setClientId(value ? value.company_code : '');
    const res = miniSearch.search(value ? value.company_code : '', {
      fuzzy: 0.3,
    });
    setResults(res);
  };
  const handleClientSelect = (value) => {
    onClientSelect({ value: value, clicked: true });
    setResults([]);
  };

  if (!clientCode) {
    return 'loading ...';
  }

 return (
   <Autocomplete
     options={results.length > 0 ? results : clientCode}
     getOptionLabel={(option) => option.company_code}
     onChange={(e, value) => {
       setClientId(value ? value.company_code : '');
       handleClientSelect(value);
     }}
     renderInput={(params) => (
       <TextField
         {...params}
         margin="normal"
         label="Search Site here"
         name="client_id"
         fullWidth
         variant={variant ?? 'standard'}
         type="search"
         InputLabelProps={{
           color: 'success',
         }}
         onChange={handleChange}
         value={clientId}
       />
     )}
   />
 );
}

export default SearchByCompName;
