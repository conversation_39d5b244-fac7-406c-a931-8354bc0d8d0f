<?php

namespace App\Http\Controllers\Api\finance;

use App\Http\Controllers\Controller;
use App\Models\finance\EmployeeDeductable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EmployeeDeductableController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(EmployeeDeductable::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data = EmployeeDeductable::create([
            'emp_basic_id' => $data['emp_basic_id'],
            'loan_amount' => $data['loan_amount'],
            'remaining_amount' => $data['loan_amount'],
            'loan_period' => $data['loan_period'],
            'remaining_repayment_period' => $data['loan_period'],
            'loan_type' => $data['loan_type'],
            'repayment_status' => 0,
            'loan_date' => $data['loan_date'],
            'reason' => $data['reason'],
            'user_id' => $request->user_id,

        ]);

        return response($data);
    }

    /**
     * Display the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $employeeDeductable = EmployeeDeductable::where('emp_basic_id', $id)->where('repayment_status', 0)->get();

        return response($employeeDeductable);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\finance\EmployeeDeductable  $employeeDeductable
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $employeeDeductable = EmployeeDeductable::find($id);
        $employeeDeductable->update([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'loan_type' => $request->input('loan_type'),
            'loan_amount' => $request->input('loan_amount'),
            'loan_period' => $request->input('loan_period'),
            'loan_date' => $request->input('loan_date'),
            'reason' => $request->input('reason'),
            'user_id' => $request->input('user_id'),
        ]);

        return response($employeeDeductable);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\finance\EmployeeDeductable  $employeeDeductable
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {

        $employeeDeductable = EmployeeDeductable::find($id);
        $employeeDeductable->delete();

        return response('', 204);
    }
}
