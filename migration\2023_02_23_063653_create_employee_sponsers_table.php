<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_sponsers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('emp_basic_id');
            $table->foreign('emp_basic_id')->references('id')->on('emp_basics')->onDelete('cascade');
            $table->string('name')->nullable();
            $table->integer('age')->nullable();
            $table->string('gender')->nullable();
            $table->string('company_name')->nullable();
            $table->string('position')->nullable();
            $table->string('city')->nullable();
            $table->string('sub_city')->nullable();
            $table->string('kebele')->nullable();
            $table->string('house_no')->nullable();
            $table->string('id_no')->nullable();
            $table->string('office_tel')->nullable();
            $table->string('mobile')->nullable();
            $table->string('property_type')->nullable();
            $table->string('position2')->nullable();
            $table->string('certificate_type')->nullable();
            $table->date('effective_date')->format('Y-m-d H:i:s')->nullable();
            $table->date('expire_date')->format('Y-m-d H:i:s')->nullable();
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
    }
    // $table->integer('Esp_Id')->primary();
    // $table->integer('Eid');
    // $table->text('Name');
    // $table->integer('Age');
    // $table->text('Gender');
    // $table->text('Company_Name');
    // $table->text('Position');
    // $table->text('City');
    // $table->text('Sub_City');
    // $table->text('Kebele');
    // $table->text('House_No');
    // $table->text('Id_No');
    // $table->text('Office_Tel');
    // $table->text('Mobile');
    // $table->text('Property_Type');
    // $table->text('Position2');
    // $table->text('Certificate_Type');
    // $table->date('Effective_Date');
    // $table->date('Expire_Date');
    // $table->integer('Uid');
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_sponsers');
    }
};
