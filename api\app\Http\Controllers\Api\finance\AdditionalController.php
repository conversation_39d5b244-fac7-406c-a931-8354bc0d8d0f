<?php

namespace App\Http\Controllers\Api\finance;

use App\Http\Controllers\Controller;
use App\Http\Resources\AdditionalResource;
use App\Models\finance\Additional;
use Illuminate\Http\Request;

class AdditionalController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index()
    {
        return  AdditionalResource::collection(Additional::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $total = $request->input('salary') * $request->input('working_time');

        $data = Additional::create([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'client_id' => $request->input('client_id'),
            'working_time' => $request->input('working_time'),
            'minutes' => $request->input('minutes'),
            'salary' => $request->input('salary'),
            'total' => $total,
            'rate_type' => $request->input('rate_type'),
            'end_date' => $request->input('end_date'),
            'effective_date' => $request->input('effective_date'),
            'reason' => $request->input('reason'),
            'user_id' => $request->input('user_id'),

        ]);

        return new AdditionalResource($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\finance\Additional  $additional
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function show($id)
    {
        $additional = Additional::where('emp_basic_id', $id)->where('paid', 0)->get();
        return AdditionalResource::collection($additional);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\finance\Additional  $additional
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Additional $additional)
    {
        $total = $request->input('salary') * $request->input('working_time');
        $additional->update([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'client_id' => $request->input('client_id'),
            'working_time' => $request->input('working_time'),
            'minutes' => $request->input('minutes'),
            'salary' => $request->input('salary'),
            'total' => $total,
            'rate_type' => $request->input('rate_type'),
            'end_date' => $request->input('end_date'),
            'effective_date' => $request->input('effective_date'),
            'reason' => $request->input('reason'),
            'user_id' => $request->input('user_id'),
        ]);

        return new AdditionalResource($additional);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\finance\Additional  $additional
     * @return \Illuminate\Http\Response
     */
    public function destroy(Additional $additional)
    {
        $additional->delete();

        return response('', 204);
    }
}
