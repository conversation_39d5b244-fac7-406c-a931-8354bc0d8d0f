<?php

namespace App\Http\Controllers\Api\settings;

use App\Http\Controllers\Controller;
use App\Models\settings\PayrollDate;
use Illuminate\Http\Request;

class PayrollDateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(PayrollDate::latest()->first());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = PayrollDate::create([
            'start_date' => $request->input('start_date'),
            'end_date' => $request->input('end_date'),
        ]);
        return response($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\settings\PayrollDate  $payrollDate
     * @return \Illuminate\Http\Response
     */
    public function show(PayrollDate $payrollDate)
    {
        return response($payrollDate);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\settings\PayrollDate  $payrollDate
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, PayrollDate $payrollDate)
    {
        $data = $payrollDate->update([
            'start_date' => $request->input('start_date'),
            'end_date' => $request->input('end_date'),
        ]);

        return response($data);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\settings\PayrollDate  $payrollDate
     * @return \Illuminate\Http\Response
     */
    public function destroy(PayrollDate $payrollDate)
    {
        $payrollDate->delete();

        return response('', 204);
    }
}
