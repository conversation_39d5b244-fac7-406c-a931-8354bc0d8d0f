import React, { useState, useEffect } from "react";
import { Grid, Box, TextField, MenuItem, Button } from "@mui/material";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useStateContext } from "../../../context/ContextProvider";
import api from "../../../api/config/axiosConfig";
function DailyRateForm({ formData, editData, setEditData }) {
  const queryClient=useQueryClient()
  const { setNotification, user } = useStateContext();
  const [dailyRate, setDailyRate] = useState({
    from_date: "",
    to_date: "",
    no_days: "",
  });
  useEffect(() => {
    if (editData) {
      setDailyRate(editData);
    }
  }, [editData]);
  const handleChange = (e) => {
    setDailyRate({
      ...dailyRate,
      [e.target.name]: e.target.value,
    });
  };
  const createDailyRate = useMutation((data) => api.post("daily_rate", data), {
    onSuccess: () => {
      setNotification("daily rate created successfully");
      queryClient.invalidateQueries({queryKey:'daily_rate'})
    },
  });
  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);

    const payload = {
      emp_basic_id: formData.emp_basic_id,
      from_date: data.get("from_date"),
      to_date: data.get("to_date"),
      no_days: data.get("no_days"),
      user_id: user.id,
    };

    if (editData && editData.id) {
      api.put(`daily_rate/${editData.id}`, payload).then(() => {
        setNotification("daily rate record is updated successfully");
        setEditData({});
        queryClient.invalidateQueries({queryKey:'daily_rate'})
      });
    } else {
      createDailyRate.mutate(payload);
    }
    
  };

  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="From Date"
            name="from_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={dailyRate.from_date || new Date().toISOString().slice(0, 10)}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Ending Date"
            name="to_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={dailyRate.to_date || new Date().toISOString().slice(0, 10)}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Number of Days"
            name="no_days"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={dailyRate.no_days ? dailyRate.no_days : ""}
          />
        </Grid>
      </Grid>
      <span style={{ float: "inline-start" }}>
        <Button type="submit" variant="contained" color="success">
          {editData.id ? <div>update</div> : <div>create</div>}
        </Button>
      </span>
    </Box>
  );
}

export default DailyRateForm;
