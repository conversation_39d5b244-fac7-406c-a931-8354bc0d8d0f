import React from "react";
import { Box, Container, useTheme } from "@mui/material";
import { commonGridStyles } from "../../../helper/usegridStyle";
import { DataGrid } from "@mui/x-data-grid";
import Header from "../../../components/Header";
import {
  usePaySeverance,
  useSeverancePaid,
} from "../../../api/userApi/clinetHook";

const PaidSeverance = () => {
  const theme = useTheme();
  const gridStyles = commonGridStyles(theme);
  const { data: paid, isLoading } = useSeverancePaid();

  const columns = [
    {
      field: "id",
      headerName: "ID",
    },
    {
      field: "emp_basic_id",
      headerName: "FullName",
      width: "200",
    },
    {
      field: "basic_salary",
      headerName: "BasicSalary",
    },
    {
      field: "start_date",
      headerName: "startDate",
    },
    {
      field: "termination_date",
      headerName: "terminationDate",
    },
    {
      field: "approval_date",
      headerName: "terminationDate",
    },
    {
      field: "y_of_service",
      headerName: "YearOfService",
    },
    {
      field: "total_severance_pay",
      headerName: "grossSeverancePayment",
    },
    {
      field: "severance_tax",
      headerName: "severanceTax",
    },
    {
      field: "severance_net",
      headerName: "NetSeverancePayment",
      cellClassName: "custom-pay",
    },
    {
      field: "termination_reason",
      headerName: "terminationReason",
      flex: 1,
    },
  ];
  if (isLoading) {
    return <>loading....</>;
  }
  return (
    <div style={{ margin: "10px" }}>
      <Header title="paid severance report" />
      <Box
        sx={{
          ...gridStyles,
        }}
      >
        {paid && (
          <DataGrid
            sx={{
              width: "100%",
              marginBottom: 5,
              "& .MuiDataGrid-columnHeaderTitle": {
                whiteSpace: "normal",
                wordWrap: "break-word",
                overflowWrap: "",
                lineHeight: "normal",
              },
            }}
            columns={columns}
            rows={paid}
            autoHeight
          />
        )}
      </Box>
    </div>
  );
};

export default PaidSeverance;
