<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateContractFollowUpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'client_id' => 'required',
            'contract_schedule' => 'required',
            'start_date' => 'date|required',
            'end_date' => 'date|required',
            'contract_document' => 'nullable',
            'reminder' => 'required',
            'amount' => 'required|numeric',
            'message' => 'string',
            'user_id' => 'required',
        ];
    }
}
