<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Models\PurchaseFromFarmer;
use Illuminate\Http\Request;

class PurchaseFromFarmerController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return PurchaseFromFarmer::with('farmer', 'coffeeBatch')->latest()->paginate();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'farmer_id' => 'required|exists:farmers,id',
            'coffee_batch_id' => 'nullable|exists:coffee_batches,id',
            'coffee_type' => 'required|string|max:255',
            'quantity_kg' => 'required|numeric',
            'price_per_kg' => 'required|numeric',
            'total_amount' => 'required|numeric',
            'purchase_date' => 'required|date',
        ]);

        return PurchaseFromFarmer::create($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\PurchaseFromFarmer  $purchaseFromFarmer
     * @return \Illuminate\Http\Response
     */
    public function show(PurchaseFromFarmer $purchaseFromFarmer)
    {
        return $purchaseFromFarmer->load('farmer', 'coffeeBatch');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\PurchaseFromFarmer  $purchaseFromFarmer
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, PurchaseFromFarmer $purchaseFromFarmer)
    {
        $data = $request->validate([
            'farmer_id' => 'sometimes|required|exists:farmers,id',
            'coffee_batch_id' => 'nullable|exists:coffee_batches,id',
            'coffee_type' => 'sometimes|required|string|max:255',
            'quantity_kg' => 'sometimes|required|numeric',
            'price_per_kg' => 'sometimes|required|numeric',
            'total_amount' => 'sometimes|required|numeric',
            'purchase_date' => 'sometimes|required|date',
        ]);

        $purchaseFromFarmer->update($data);

        return $purchaseFromFarmer;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\PurchaseFromFarmer  $purchaseFromFarmer
     * @return \Illuminate\Http\Response
     */
    public function destroy(PurchaseFromFarmer $purchaseFromFarmer)
    {
        $purchaseFromFarmer->delete();

        return response()->noContent();
    }
}
