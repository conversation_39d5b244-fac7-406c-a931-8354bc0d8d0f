1. hire requist form yes/no (when, who, how much, level of salary)
2. ad for the position posted online
3. collect the applications filter the applicants based on the requirment
4. filtered applicants data are filled in the system and give notification to <PERSON> that this applicats for the role are ready for exam or interviw
5. GM sets date for intrview or exam 
6. hr is notify ed of the date contacts the applicats
7. exam score is filled in the system and ready for evaluation notify the approprate exam commite
8. <PERSON><PERSON><PERSON> sees the score and choose the applicat from the list and notifys hr
9. hr hires that applicant fills the remaining data for the employee

10. for new hires hr is notifyed to evalute his/her work ethics with in 55 days of hairing that persion
11. then the commite sees the revaluatin data and decides to make him permanent or not.
12. commite list must be avaliable from the first time of the postion posting



ok here is a todo list 
| # | Content | Status |
|---|---------|--------|
| 1 | Design database tables for recruitment module: | Completed |
| 2 | hire_requisitions | Completed |
| 3 | job_postings | Completed |
| 4 | applicants | Completed |
| 5 | exam_scores | Completed |
| 6 | evaluation_data | Completed |
| 7 | committee_members | Completed |
| 8 | Create backend models and API endpoints for recruitment module: | Completed |
| 9 | HireRequisition model and API | Completed |
| 10 | JobPosting model and API | Completed |
| 11 | Applicant model and API | Completed |
| 12 | ExamScore model and API | Completed |
| 13 | EvaluationData model and API | Completed |
| 14 | CommitteeMember model and API | Completed |

| 15 | Develop frontend components for recruitment module | Pending |
| 16 | Implement hire requisition form | Pending |
| 17 | Implement job posting functionality | Pending |
| 18 | Implement applicant filtering | Pending |
| 19 | Implement applicant data entry | Pending |
| 20 | Implement GM notification system | Pending |
| 21 | Implement interview/exam scheduling | Pending |
| 22 | Implement HR notification system | Pending |
| 23 | Implement exam score entry | Pending |
| 24 | Implement exam committee notification system | Pending |
| 25 | Implement applicant selection | Pending |
| 26 | Implement hiring functionality | Pending |
| 27 | Implement new hire evaluation | Pending |
| 28 | Implement committee decision-making | Pending |
| 29 | Implement committee list management | Pending |