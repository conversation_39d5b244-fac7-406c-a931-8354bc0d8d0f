<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customs_declarations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('export_order_id')->constrained('export_orders');
            $table->string('declaration_number')->unique();
            $table->string('hs_code')->default('090111');
            $table->string('export_license_number')->nullable();
            $table->date('date_filed')->nullable();
            $table->string('pdf_document_url')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customs_declarations');
    }
};
