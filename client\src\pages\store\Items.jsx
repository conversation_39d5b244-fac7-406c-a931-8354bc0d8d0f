import { AddOutlined } from '@mui/icons-material';
import {
  Button,
  Typography,
  useTheme,
  Box,
  IconButton,
  Grid,
  TextField,
  Modal,
  MenuItem,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import api from '../../api/config/axiosConfig';
import Header from '../../components/Header';
import { useStateContext } from '../../context/ContextProvider';
import { tokens } from '../../utils/theme';
import _CircularProgress from '../../components/_CircularProgress';

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
};

function Items() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState({});
  const { setNotification, errors, setErrors } = useStateContext();

  const { data: items, isLoading } = useQuery(
    ['items'],
    async () =>
      await api.get('store/items').then(({ data }) => {
        return data.data;
      })
  );

  const { data: categories } = useQuery(
    ['categories'],
    async () =>
      await api.get('store/categories').then(({ data }) => {
        console.log(data)
        return data.data;
      })
  );

  const columns = [
    {
      field: 'id',
      headerName: 'Id',
    },
    {
      field: 'name',
      headerName: 'Item Name',
      flex: 1,
    },
    {
      field: 'description',
      headerName: 'Description',
      flex: 1,
    },
    {
      field: 'category_name',
      headerName: 'Category',
      flex: 1,
      valueGetter: (params) => params.row.category.name,
    },
    {
      field: 'edit',
      headerName: 'Edit',
      flex: 1,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          setOpen(true);
          setSelectedRow({
            ...params.row,
            category_id: params.row.category.id,
          });
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: 'delete',
      headerName: 'Delete',
      flex: 1,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deleteItemMutation = useMutation(
          (id) => api.delete(`store/items/${id}`),
          {
            onSuccess: () => {
              setNotification('Item Deleted Successfully ');
            },
            onSettled: () => {
              queryClient.invalidateQueries(['items']);
            },
          }
        );
        const deleteItem = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              'Are you sure you want to delete the selected item?'
            )
          ) {
            return;
          }
          deleteItemMutation.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deleteItem}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];

  const handleOpen = () => setOpen(true);
  
  const handleClose = () => {
    setOpen(false);
    setSelectedRow({});
  };

  const handleChange = (e) => {
    setSelectedRow({
      ...selectedRow,
      [e.target.name]: e.target.value,
    });
  };

  const postItem = useMutation((data) => api.post('store/items', data), {
    onSuccess: () => {
      queryClient.invalidateQueries(['items']);
      setNotification('Item created successfully');
      handleClose();
    },
    onError: (err) => {
      if (err.response && err.response.status === 422) {
        if (err.response.data.errors) {
          setErrors(err.response.data.errors);
        }
      }
    },
  });

  const onSubmit = (e) => {
    e.preventDefault();
    const payload = {
      name: selectedRow.name,
      description: selectedRow.description,
      category_id: selectedRow.category_id,
      stock_quantity: selectedRow.stock_quantity,
      reorder_level: selectedRow.reorder_level,
    };
    if (selectedRow.id) {
      api
        .put(`store/items/${selectedRow.id}`, payload)
        .then(() => {
          setNotification('Item updated successfully');
          queryClient.invalidateQueries(['items']);
          handleClose();
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
        });
    } else {
      postItem.mutate(payload);
    }
  };

  return (
    <Box m="10px">
      <Header title="Items Table" subtitle="List of items" />
      <Box display="flex" justifyContent="center" alignItems="center">
        <IconButton
          sx={{
            boxShadow: '2px 2px 4px rgba(0,0,0,0.25)',
            background: colors.primary[400],
          }}
          onClick={handleOpen}
        >
          <AddOutlined color={colors.grey[100]} />
        </IconButton>
      </Box>
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={style} component="form" onSubmit={onSubmit}>
          <Typography id="modal-modal-title" variant="h6" component="h2">
            {selectedRow.id ? 'Edit Item' : 'Add Item'}
          </Typography>
          <TextField
            error={!errors.name == ''}
            id="name"
            margin="normal"
            fullWidth
            label="Item Name"
            name="name"
            InputLabelProps={{
              color: 'success',
            }}
            value={selectedRow.name || ''}
            onChange={handleChange}
            helperText={errors.name ? errors.name[0] : null}
          />
          <TextField
            error={!errors.description == ''}
            id="description"
            margin="normal"
            fullWidth
            label="Description"
            name="description"
            InputLabelProps={{
              color: 'success',
            }}
            value={selectedRow.description || ''}
            onChange={handleChange}
            helperText={errors.description ? errors.description[0] : null}
          />
          <TextField
            select
            error={!errors.category_id == ''}
            id="category_id"
            margin="normal"
            fullWidth
            label="Category"
            name="category_id"
            InputLabelProps={{
              color: 'success',
            }}
            value={selectedRow.category_id || ''}
            onChange={handleChange}
            helperText={errors.category_id ? errors.category_id[0] : null}
          >
            {categories &&
              categories.map((category) => (
                <MenuItem key={category.id} value={category.id}>
                  {category.name}
                </MenuItem>
              ))}
          </TextField>
          <TextField
            error={!errors.stock_quantity == ''}
            id="stock_quantity"
            margin="normal"
            fullWidth
            label="Stock Quantity"
            name="stock_quantity"
            type="number"
            InputLabelProps={{
              color: 'success',
            }}
            value={selectedRow.stock_quantity || ''}
            onChange={handleChange}
            helperText={errors.stock_quantity ? errors.stock_quantity[0] : null}
          />
          <TextField
            error={!errors.reorder_level == ''}
            id="reorder_level"
            margin="normal"
            fullWidth
            label="Reorder Level"
            name="reorder_level"
            type="number"
            InputLabelProps={{
              color: 'success',
            }}
            value={selectedRow.reorder_level || ''}
            onChange={handleChange}
            helperText={errors.reorder_level ? errors.reorder_level[0] : null}
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 2, py: 2 }}
            color="success"
            size="small"
          >
            submit
          </Button>
        </Box>
      </Modal>
      <Box
        m="10px 0 0 0"
        height="67vh"
        sx={{
          '& .MuiDataGrid-root': {
            border: 'none',
          },
          '& .MuiDataGrid-cell': {
            borderBottom: 'none',
          },
          '& .name-column--cell': {
            color: colors.greenAccent[300],
          },
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: colors.blueAccent[700],
            borderBottom: 'none',
          },
          '& .MuiDataGrid-virtualScroller': {
            backgroundColor: colors.primary[400],
          },
          '& .MuiDataGrid-footerContainer': {
            borderTop: 'none',
            backgroundColor: colors.blueAccent[700],
          },
          '& .MuiCheckBox-root': {
            color: `${colors.greenAccent[200]}!important`,
          },
          '& .MuiDataGrid-toolbarContainer .MuiButton-text': {
            color: `${colors.grey[100]}!important`,
          },
        }}
      >
        {isLoading ? (
          <_CircularProgress />
        ) : (
          <DataGrid columns={columns} rows={items} />
        )}
      </Box>
    </Box>
  );
}

export default Items;
