import { <PERSON>, <PERSON>ton, Typography, useTheme } from "@mui/material";
import { DataGrid, GridToolbar, GridToolbarContainer, GridToolbarExport, GridToolbarFilterButton } from "@mui/x-data-grid";
import React, { useEffect, useState } from "react";
import { Link, Navigate, useNavigate } from "react-router-dom";
import api from "../../api/config/axiosConfig";
import { getUsers, onDelete } from "../../api/userApi/users";
import Header from "../../components/Header";
import { useStateContext } from "../../context/ContextProvider";
import { tokens } from "../../utils/theme";
export function CustomToolbar(){
  return (
    <GridToolbarContainer>
      <GridToolbarFilterButton/>
      <GridToolbarExport/>
    </GridToolbarContainer>
  )
}
function Users() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [users, setUsers] = useState([]);
  const { setNotification } = useStateContext();
  useEffect(() => {
    getUsers(setUsers);
  }, []);
  const columns = [
    {
      field: "id",
      headerName: "ID",
      cellClassName: "name-column--cell",
    },
    {
      field: "firstName",
      headerName: "FirstName",
      flex: 1,
    },
    {
      field: "lastName",
      headerName: "LastName",
      flex: 1,
    },
    {
      field: "email",
      headerName: "Email",
      flex: 1,
    },
    {
      field: "phone",
      headerName: "Phone",
      flex: 1,
    },
    {
      field: "role",
      headerName: "role",
      flex: 1,
    },
    {
      field: "created_at",
      headerName: "Registration Date",
      flex: 1,
    },
    {
      field: "edit",
      headerName: "Edit",
      sortable: false,
      flex: 1,

      renderCell: (params) => {
        const id = params.row.id;
        return (
          <Box
            width="60%" 
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              <Link to={`/settings/users/${id}`}>
                <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                  Edit
                </Typography>
              </Link>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "delete",
      headerName: "Delete",
      flex: 1,
      sortable: false,
      renderCell: (params) => {
        const deleteUser = (e) => {
          e.stopPropagation();
          if (!window.confirm("Are you sure you want to delete the user?")) {
            return;
        }
          api.delete(`/settings/users/${params.row.id}`).then(()=>{
            setNotification("user is successfully deleted");
            getUsers(setUsers)
          })
         
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deleteUser}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];

  return (
    <Box m="20px">
      <Header title="Users Table" subtitle="welcome to users table" />
      <Box
        m="20px 0 0 0"
        height="75vh"
        sx={{
          "& .MuiDataGrid-root": {
            border: "none",
          },
          "& .MuiDataGrid-cell": {
            borderBottom: "none",
          },
          "& .name-column--cell": {
            color: colors.greenAccent[300],
          },
          "& .MuiDataGrid-columnHeaders": {
            backgroundColor: colors.blueAccent[700],
            borderBottom: "none",
          },
          "& .MuiDataGrid-virtualScroller": {
            backgroundColor: colors.primary[400],
          },
          "& .MuiDataGrid-footerContainer": {
            borderTop: "none",
            backgroundColor: colors.blueAccent[700],
          },
          "& .MuiCheckBox-root": {
            color: `${colors.greenAccent[200]}!important`,
          },
          "& .MuiDataGrid-toolbarContainer .MuiButton-text": {
            color: `${colors.grey[100]}!important`,
          },
        }}
      >
        <DataGrid
          rows={users}
          columns={columns}
          components={{Toolbar:CustomToolbar}}
        />
      </Box>
    </Box>
  );
}

export default Users;
