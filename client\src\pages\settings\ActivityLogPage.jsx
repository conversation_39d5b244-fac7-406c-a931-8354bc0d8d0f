import React, { useState } from 'react';
import {
  Box,
  Button,
  Grid,
  Typography,
  Paper, // Import Paper for consistency
  CircularProgress, // Import CircularProgress
  Alert, // Import Alert
  Chip,
  Tooltip,
  IconButton,
} from '@mui/material';
import { DataGrid, GridToolbar } from '@mui/x-data-grid'; // Import GridToolbar
import axiosInstance from '../../api/config/axiosConfig';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns'; // For formatting dates
import InfoIcon from '@mui/icons-material/Info'; // For viewing details
// Removed CustomToolBar import if using GridToolbar

export default function ActivityLogPage() {
  const [page, setPage] = useState(0); // MUI DataGrid uses 0-based page index
  const [rowsPerPage, setRowsPerPage] = useState(15); // Default rows per page

  // Fetch paginated data
  const {
    data: queryData, // Rename to avoid conflict with logs variable
    isLoading: activityLogsLoading,
    error: activityLogsError,
    isFetching, // Use isFetching for loading indicator during refetch
    refetch,
  } = useQuery({
    queryKey: ['activityLogs', page + 1, rowsPerPage], // Include rowsPerPage in key
    queryFn: async () => {
      const response = await axiosInstance.get(
        `/activity-logs?page=${page + 1}&per_page=${rowsPerPage}`
      );
      if (response.status !== 200) {
        throw new Error('Network response was not ok');
      }
      console.log('ActivityLogs API Response: ', response.data);
      return response.data; // Return the entire paginated response
    },
    keepPreviousData: true, // Keep previous data while fetching next page
  });

  // Extract logs and pagination info safely
  const logs =
    queryData?.data.map((log) => ({
      ...log,
      firstName: log?.user?.firstName || 'Guest/System',
      username: log?.user?.username || 'Guest/System',
    })) || [];
  const totalLogs = queryData?.total || 0;

  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize) => {
    setRowsPerPage(newPageSize);
    setPage(0); // Reset to first page when page size changes
  };

  const getStatusColor = (status) => {
    if (status >= 500) return 'error';
    if (status >= 400) return 'warning';
    if (status >= 300) return 'info';
    if (status >= 200) return 'success';
    return 'default';
  };

  const columns = [
    { field: 'id', headerName: 'ID', width: 90 },
    {
      field: 'firstName', // Use a unique field name
      headerName: 'name',
      width: 180,
    },
    // You might not need a separate 'User Name' column if Full Name is enough
    { field: 'username', headerName: 'Username', width: 130 },
    {
      field: 'created_at',
      headerName: 'Timestamp',
      width: 180,
      // valueFormatter: (params) =>
      //   params
      //     ? format(new Date(params), 'yyyy-MM-dd HH:mm:ss')
      //     : 'Invalid Date',
    },
    {
      field: 'method',
      headerName: 'Method',
      width: 100,
      renderCell: (params) => (
        <Chip label={params.value} size="small" variant="outlined" />
      ),
    },
    {
      field: 'url',
      headerName: 'URL',
      width: 350,
      renderCell: (params) => (
        <Box sx={{ whiteSpace: 'normal', wordBreak: 'break-all' }}>
          {params.value}
        </Box>
      ),
    },
    {
      field: 'response_status',
      headerName: 'Status',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getStatusColor(params.value)}
          size="small"
        />
      ),
      align: 'center',
      headerAlign: 'center',
    },
    { field: 'ip_address', headerName: 'IP Address', width: 130 },
    {
      field: 'details',
      headerName: 'Details',
      width: 100,
      sortable: false,
      filterable: false,
      renderCell: (params) => (
        <Tooltip title="View Request Body">
          <IconButton
            size="small"
            onClick={() =>
              alert(
                `Log ID: ${params.row.id}\n\nBody:\n${params.row.request_body}`
              )
            }
          >
            <InfoIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      ),
      align: 'center',
      headerAlign: 'center',
    },
  ];

  return (
    <Box sx={{ width: '100%', padding: 2 }}>
      <Grid container spacing={2} sx={{ marginBottom: 2 }} alignItems="center">
        <Grid item xs>
          <Typography variant="h4">Activity Logs</Typography>
        </Grid>
        <Grid item>
          <Button
            variant="outlined"
            color="primary"
            onClick={refetch}
            disabled={isFetching}
          >
            {isFetching ? <CircularProgress size={24} /> : 'Refresh'}
          </Button>
        </Grid>
      </Grid>

      {activityLogsError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          Failed to load activity logs: {activityLogsError.message}
        </Alert>
      )}

      <Paper sx={{ height: "90VH", width: '100%' }}>
        {' '}
        {/* Use Paper for elevation */}
        <DataGrid
          rows={logs} // Pass the guaranteed array
          columns={columns}
          loading={activityLogsLoading || isFetching} // Show loading on initial load and refetch
          rowCount={totalLogs} // Total rows for server-side pagination
          pageSize={rowsPerPage}
          rowsPerPageOptions={[15, 25, 50]}
          pagination
          paginationMode="server" // Enable server-side pagination
          page={page} // Control the current page
          onPageChange={handlePageChange} // Handle page changes
          onPageSizeChange={handlePageSizeChange} // Handle page size changes
          getRowId={(row) => row.id}
          disableSelectionOnClick
          slots={{ toolbar: GridToolbar }} // Use built-in toolbar
          sx={{ border: 0 }} // Remove default border
        />
      </Paper>
    </Box>
  );
}
