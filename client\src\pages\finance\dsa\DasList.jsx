import React from "react";
import Header from "../../../components/Header";
import {
  <PERSON>ton,
  Typography,
  useTheme,
  Box,
  CircularProgress,
} from "@mui/material";
import { tokens } from "../../../utils/theme";
import { DataGrid } from "@mui/x-data-grid";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";
import { useStateContext } from "../../../context/ContextProvider";
function DasList({ dsa, selectedRow }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const {
    data: dsaData,
    isLoading,
    isFetched,
  } = useQuery(
    ["dsa", dsa.emp_basic_id],
    async () =>
      await api.get(`dsa/${dsa.emp_basic_id}`).then(({ data }) => data.data),
    {
      enabled: !!dsa.emp_basic_id,
      staleTime: 60000,
    }
  );
  const columns = [
    {
      field: "id",
      headerName: "ID",
      flex: 0.2,
    },
    {
      field: "from_date",
      headerName: "From Date",
      flex: 0.5,
    },
    {
      field: "to_date",
      headerName: "end_date",
      flex: 0.8,
    },
    {
      field: "rate_amount",
      headerName: "Rate Amount",
      flex: 0.5,
    },

    {
      field: "no_days",
      headerName: "No-days",
      flex: 0.5,
    },
    {
      field: "total",
      headerName: "Total",
      flex: 0.5,
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 0.7,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          selectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "delete",
      headerName: "Delete",
      flex: 0.8,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deleteDsa = useMutation((id) => api.delete(`dsa/${id}`), {
          onSuccess: () => {
            setNotification("dsa record deleted successfully");
            queryClient.invalidateQueries({ queryKey: "dsa" });
          },
        });
        const onDeleteDsa = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              "Are you sure you want to delete the selected  dsa record?"
            )
          ) {
            return;
          }
          deleteDsa.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onDeleteDsa}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];
  return (
    <>
      <Header title="List of DSA records" heading="h5" />
      {isLoading && <CircularProgress />}
      {isFetched && (
        <DataGrid columns={columns} hideFooter autoHeight rows={dsaData} />
      )}
    </>
  );
}

export default DasList;
