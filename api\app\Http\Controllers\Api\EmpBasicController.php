<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\EmpBasic;
use App\Http\Requests\StoreEmpBasicRequest;
use App\Http\Requests\UpdateEmpBasicRequest;
use App\Http\Resources\EmpBasicResource;
use App\Models\EmpDetail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EmpBasicController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection 
     */
    public function index()
    {
        $query = EmpBasic::query();

        if (request()->input('name')) {
            //$query->where('first_name', 'LIKE', '%' . strtolower(request()->input('name')) . '%');
            $query->whereRaw("first_name LIKE ?", [strtolower(request()->input('name')) . '%']);
        }

        if (request()->input('middle_name')) {
            $query->whereRaw("middle_name LIKE ?", [strtolower(request()->input('middle_name')) . '%']);
        }

        $empBasic = $query->get();

        return EmpBasicResource::collection($empBasic);
    }

    public function empId()
    {
        $lastUser = DB::table('emp_basics')->select('id')->latest()->first();
        return response(['id' => $lastUser], 200);
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreEmpBasicRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreEmpBasicRequest $request)
    {
        $data = $request->validated();
        $empBasic = EmpBasic::create($data);
        return response($empBasic, 201);
    }

    private function loadAllRelatedData(EmpBasic $empBasic)
    {
        return EmpBasic::with([
            'empBank',
            'empDetail',
            'empAllowance',
            'empLoan',
            'empSponser',
            'empStatus',
            'empEducation',
            'empWorkExperiance',
            'empEmergencyContact',
            'language',
            'familyInfo',
            'sponsoredEmployee',
            'award',
            'discipline'
        ])->find($empBasic->id);
    }
    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EmpBasic  $empBasic
     * @return \App\Http\Resources\EmpBasicResource
     */
    public function show(EmpBasic $empBasic)
    {
        $shouldLoadAllData = request()->input('all');

        if ($shouldLoadAllData) {
            $emp = $this->loadAllRelatedData($empBasic);
            //Log::info('EmpBasicController@show', [$emp]);
            return new EmpBasicResource($emp);
        }
        //Log::info('EmpBasicController@show', ['showAll' => $shouldLoadAllData]);
        return new EmpBasicResource($empBasic);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateEmpBasicRequest  $request
     * @param  \App\Models\EmpBasic  $empBasic
     * @return \App\Http\Resources\EmpBasicResource
     */
    public function update(UpdateEmpBasicRequest $request, EmpBasic $empBasic)
    {
        $data = $request->validated();
        $empBasic->update($data);

        return new EmpBasicResource($empBasic);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EmpBasic  $empBasic
     * @return \Illuminate\Http\Response
     */
    public function destroy(EmpBasic $empBasic)
    {
        $empBasic->delete();

        return response('', 204);
    }
}