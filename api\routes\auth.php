<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthApi\AuthController;
use App\Http\Controllers\AuthApi\AuthUser;
use App\Http\Controllers\Api\ChangePasswordController; // Ensure this class exists in the specified namespace
use App\Http\Controllers\Api\UserPermissionController;
use App\Http\Controllers\Api\UserController;

Route::get('user', [AuthUser::class, 'user']);
Route::post('change-password', [ChangePasswordController::class, 'changePassword']);
Route::post('reset-password/{userId}', [ChangePasswordController::class, 'resetPassword']);
Route::apiResource('users-permission', UserPermissionController::class);
Route::delete('/logout', [AuthController::class, 'logout']);
Route::apiResource('/users', UserController::class);