import React from 'react'
import {
    Box,
    TextField,
  } from "@mui/material";
  import Header from "../../../components/Header";
function FinancialInfoUntaxIncome() {
  return (
    <Box>
    <Header
      heading="h6"
      title="Financial Information Untaxable Incomes"
    />
    <TextField
      label="Total Medical Cost"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Sport Contribution"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Labor Union"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Credit Asociation"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Social Support"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Social Support"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Social Support"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Transport Deduction"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Loan"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Additional Deduction"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Additinal Ded Reason"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Other Loan Ded"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Other Loan Reason:"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Other Loan Reason:"
      value="0"
      variant="standard"
      InputProps={{
        readOnly: true,
      }}
      disabled
      size="small"
      sx={{ mr: 1 }}
    />
    <TextField
      label="Leave With Out Pay Ded:"
      value="0"
      variant="standard"
      // InputProps={{
      //   readOnly: true,
      // }}
      // disabled
      size="small"
      sx={{ mr: 1 }}
    />
  </Box>
  )
}

export default FinancialInfoUntaxIncome