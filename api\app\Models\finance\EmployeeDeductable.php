<?php

namespace App\Models\finance;

use App\Models\LoanPaymentHistoriy;
use App\Models\LoanPaymentHistory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\EmpBasic;
use Illuminate\Support\Facades\Log;

class EmployeeDeductable extends Model
{
    use HasFactory;
    protected $guarded = [];
    public function empBasic()
    {
        return $this->belongsTo(EmpBasic::class);
    }

    public static function expectedPaymentEachMonth($empBasicId)
    {
        $loans = self::where('emp_basic_id', $empBasicId)->get();
        Log::info('loanstable', ['loans' => $loans]);
        $paymentPerMonth = [];

        foreach ($loans as $loan) {
            $loanType = $loan->loan_type;
            $loanAmount = $loan->loan_amount;
            $cyclePayment = $loan->loan_period;

            if (!isset($paymentPerMonth[$loanType])) {
                if ($loan->repayment_status) {
                    $paymentPerMonth[$loanType] = [
                        'each_payment' => 0,
                        'id' => $loan->id,
                        'repayment_status' => $loan->repayment_status,
                    ];
                } else {
                    $paymentPerMonth[$loanType] = [
                        'id' => $loan->id,
                        'each_payment' => round($loanAmount / $cyclePayment, 2),
                        'repayment_status' => $loan->repayment_status,
                    ];
                }
            }
        }
        return $paymentPerMonth;
    }

    public static function updateLoan($empBasicId, $paymentPerMonth)
    {
        $loans = self::where('emp_basic_id', $empBasicId)->get();

        foreach ($loans as $loan) {
            if (array_key_exists($loan['loan_type'], $paymentPerMonth)) {
                $remainingAmount = max(0, $loan['remaining_amount'] - $paymentPerMonth[$loan['loan_type']]['each_payment']);
                $remainingCycle = max(0, $loan['remaining_repayment_period'] - 1);
                $loan->where('loan_type', $loan['loan_type'])->update([
                    'remaining_amount' => $remainingAmount,
                    'remaining_repayment_period' => $remainingCycle
                ]);
                if ($remainingCycle === 0) {
                    $loan->where('loan_type', $loan['loan_type'])->update([
                        'repayment_status' => 1
                    ]);
                }
            }
        }
    }

    public  static function updateLoanHistory($paymentPerMonth, $paydate)
    {
        foreach ($paymentPerMonth as $key => $value) {
            if (!$value['repayment_status']) {
                $loanHistory = new LoanPaymentHistory();
                $loanHistory->employee_loan_id = $value['id'];
                $loanHistory->payment_date = $paydate;
                $loanHistory->loan_type = $key;
                $loanHistory->save();
            }
        }
    }
}
