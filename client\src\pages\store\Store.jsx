import { Box, Grid } from "@mui/material";
import React from "react";
import Header from "../../components/Header";
import LinkCard from "../../components/LinkCard";

function Store() {
  return (
    <Box m="10px">
      <Header
        title="Store Page"
        subtitle="Store management"
      />
      <Box maxWidth="85%" ml="50px">
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Category"
              subtitle="Manage item categories"
              to="/settings/category"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Items"
              subtitle="Manage store items"
              to="/store/items"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Stock"
              subtitle="Manage stock levels"
              to="/store/stock"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Requests"
              subtitle="Manage item requests"
              to="/store/requests"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <LinkCard
              title="Approval"
              subtitle="Approve item requests"
              to="/store/approval"
            />
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}

export default Store;
