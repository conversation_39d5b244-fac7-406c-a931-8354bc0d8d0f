import React from "react";
import { Box, Grid, <PERSON><PERSON>ield, Button } from "@mui/material";

import { useState } from "react";

import { useStateContext } from "../../../context/ContextProvider";
import { useEffect } from "react";
import api from "../../../api/config/axiosConfig";
function UpdateSponser({id}) {
  const { errors, setErrors, setNotification, user } = useStateContext();
  const [isdata, setIsData] = useState(false);
  const [formData, setFormData] = useState({
    emp_basic_id:'',
    name: "",
    age: "",
    gender: "",
    company_name: "",
    position: "",
    city: "",
    sub_city: "",
    kebele: "",
    house_no: "",
    id_no: "",
    office_tel: "",
    mobile: "",
    property_type: "",
    position2: "",
    certificate_type: "",
    effective_date: "",
    expire_date: "",
    user_id: user.id,
  });

    useEffect(() => {
      setFormData({
        ...formData,
        user_id: user.id,
      });
    }, [user]);

  if(id){
    useEffect(()=>{
     api
       .get(`emp_basics/${id}?all=true`, { params: { all: 'sponser' } })
       .then(({ data }) => {
         if (data.data.emp_sponser) {
           setFormData(data.data.emp_sponser);
           setIsData(true);
         }
       }); 
    },[id])
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if(isdata){
      api.put(`emp_sponser/${formData.id}`,formData).then(()=>{
        setNotification('successfully updated')
      })
      .catch((err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      });
    }else{
      const data=new FormData(e.currentTarget)
      const payload={
        emp_basic_id:id,
        name: data.get('name'),
        age: data.get('age'),
        gender: data.get('gender'),
        company_name: data.get('company_name'),
        position: data.get('position'),
        city: data.get('city'),
        sub_city: data.get('sub_city'),
        kebele: data.get('kebele'),
        house_no: data.get('house_no'),
        id_no: data.get('id_no'),
        office_tel: data.get('office_tel'),
        mobile: data.get('mobile'),
        property_type: data.get('property_type'),
        position2: data.get('position2'),
        certificate_type: data.get('certificate_type'),
        effective_date: data.get('effective_date'),
        expire_date: data.get('expire_date'),
        user_id:user.id,
      }

      api.post('emp_sponser',payload).then(()=>{
        setNotification('created successfully')
      })
      .catch((err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      });
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.name == ""}
            margin="normal"
            label="Full Name"
            name="name"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.name}
            helperText={errors.name ? errors.name[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.age == ""}
            margin="normal"
            label="Age"
            name="age"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.age}
            helperText={errors.age ? errors.age[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.gender == ""}
            margin="normal"
            label="Gender"
            name="gender"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.gender}
            helperText={errors.gender ? errors.gender[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.company_name == ""}
            margin="normal"
            label="Company Name"
            name="company_name"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.company_name}
            helperText={errors.company_name ? errors.company_name[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.position == ""}
            margin="normal"
            label="Position"
            name="position"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.position}
            helperText={errors.position ? errors.position[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.city == ""}
            margin="normal"
            label="City"
            name="city"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.city || ""}
            helperText={errors.city ? errors.city[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.sub_city == ""}
            margin="normal"
            label="Sub City"
            name="sub_city"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.sub_city || ""}
            helperText={errors.sub_city ? errors.sub_city[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.kebele == ""}
            margin="normal"
            label="Kebele"
            name="kebele"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.kebele || ""}
            helperText={errors.kebele ? errors.kebele[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.house_no == ""}
            margin="normal"
            label="House Number"
            name="house_no"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.house_no || ""}
            helperText={errors.house_no ? errors.house_no[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.id_no == ""}
            margin="normal"
            label="Id Number"
            name="id_no"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.id_no}
            helperText={errors.id_no ? errors.id_no[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.office_tel == ""}
            margin="normal"
            label="Offical Telephone"
            name="office_tel"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.office_tel}
            helperText={errors.office_tel ? errors.office_tel[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.mobile == ""}
            margin="normal"
            label="Phone Number"
            name="mobile"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.mobile}
            helperText={errors.mobile ? errors.mobile[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.property_type == ""}
            margin="normal"
            label="Property Type"
            name="property_type"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.property_type}
            helperText={errors.property_type ? errors.property_type[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.position2 == ""}
            margin="normal"
            label="Other Position"
            name="position2"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.position2}
            helperText={errors.position2 ? errors.position2[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.certificate_type == ""}
            margin="normal"
            label="Certeficate Type"
            name="certificate_type"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.certificate_type}
            helperText={
              errors.certificate_type ? errors.certificate_type[0] : null
            }
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.effective_date == ""}
            margin="normal"
            label="Effective Date"
            name="effective_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.effective_date}
            helperText={errors.effective_date ? errors.effective_date[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.expire_date == ""}
            margin="normal"
            label="Expired Date"
            name="expire_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.expire_date}
            helperText={errors.expire_date ? errors.expire_date[0] : null}
          />
        </Grid>
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
        {isdata?<div>update</div>:<div>create</div>}
        </Button>
      </Box>
    </Box>
  );
}

export default UpdateSponser;
