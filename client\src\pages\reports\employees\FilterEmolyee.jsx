import {
  Box,
  Checkbox,
  FormControl,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  ListItemText,
  Grid,
} from "@mui/material";
import React, { useState } from "react";
import DateDiffrenceComponent from "../../../components/DateDiffrenceComponent";
import SearchBar from "../../../components/SearchBar";
import YearDropDown from "../../../components/YearDropDown";
import GenderDropDown from "../../../components/GenderDropDown";
import RegionDropDown from "../../../components/RegionDropDown";
import BankCodeDropDown from "../../../components/BankCodeDropDown";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
const FilterEmolyee = ({
  passFromDate,
  passToDate,
  passProjectId,
  passYear,
  passGender,
  passRegion,
  passBank,
}) => {
  const [selectedComponents, setSelectedComponents] = useState([]);

  const handleChange = (event) => {
    const {
      target: { value },
    } = event;
    setSelectedComponents(value);
  };

  const handleDateChanges = (fromDate, toDate) => {
    passFromDate(fromDate);
    passToDate(toDate);
  };

  const handleClientSelect = ({ value, clicked }) => {
    passProjectId(value.id);
  };

  const handleSelectedYear = (year) => {
    passYear(year);
  };
  const handleSelectedGender = (gender) => {
    passGender(gender);
  };
  const handleSelectedRegion = (region) => {
    passRegion(region);
  };

  const handleSelectedBank = ({value: bankCode, clicked}) => {
    passBank(bankCode);
  };

  const componentList = [
    {
      name: 'DateDiffrence',
      component: (
        <DateDiffrenceComponent
          labelOne="fromDate"
          labelTwo="toDate"
          onDateChange={handleDateChanges}
        />
      ),
    },
    {
      name: 'Gender',
      component: <GenderDropDown onSelectedGender={handleSelectedGender} />,
    },
    {
      name: 'Region',
      component: <RegionDropDown onSelectedRegion={handleSelectedRegion} />,
    },
    {
      name: 'fullYear',
      component: <YearDropDown onSelectedYear={handleSelectedYear} />,
    },
    {
      name: 'Project(Clients)',
      component: (
        <SearchBar onClientSelect={handleClientSelect} variant="outlined" />
      ),
    },
    {
      name: 'Bank Name',
      component: <BankCodeDropDown onSelectedCenter={handleSelectedBank} />,
    },
  ];

  return (
    <div style={{ display: "flex" }}>
      <div>
        <FormControl sx={{ m: 1, width: 300 }}>
          <InputLabel id="demo-multiple-checkbox-label">
            Select Components
          </InputLabel>
          <Select
            labelId="demo-multiple-checkbox-label"
            id="demo-multiple-checkbox"
            multiple
            value={selectedComponents}
            onChange={handleChange}
            input={<OutlinedInput label="Select Filtering Methods" />}
            renderValue={(selected) => selected.join(", ")}
            MenuProps={MenuProps}
          >
            {componentList.map((component) => (
              <MenuItem key={component.name} value={component.name}>
                <Checkbox
                  checked={selectedComponents.indexOf(component.name) > -1}
                />
                <ListItemText primary={component.name} />
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </div>
      <Grid container spacing={1}>
        {componentList.map((component) => {
          if (selectedComponents.includes(component.name)) {
            return (
              <Grid item xs={12} sm={6} md={4} key={component.name}>
                {component.component}
              </Grid>
            );
          }
          return null;
        })}
      </Grid>
    </div>
  );
};

export default FilterEmolyee;
