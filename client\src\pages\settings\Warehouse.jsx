import { AddOutlined } from '@mui/icons-material';
import {
  Button,
  Typography,
  useTheme,
  Box,
  IconButton,
  Grid,
  TextField,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import api from '../../api/config/axiosConfig';
import Header from '../../components/Header';
import { useStateContext } from '../../context/ContextProvider';
import { tokens } from '../../utils/theme';
import _CircularProgress from '../../components/_CircularProgress';

function Warehouse() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const queryClient = useQueryClient();
  const [clicked, setClicked] = useState(false);
  const [selectedRow, setSelectedRow] = useState({});
  const { setNotification, errors, setErrors } = useStateContext();

  const { data: warehouses, isLoading } = useQuery(
    ['warehouses'],
    async () =>
      await api.get('store/warehouses').then(({ data }) => {
        return data;
      })
  );

  const columns = [
    {
      field: 'id',
      headerName: 'Id',
    },
    {
      field: 'name',
      headerName: 'Warehouse Name',
      flex: 1,
    },
    {
      field: 'location',
      headerName: 'Location',
      flex: 1,
    },
    {
        field: 'capacity',
        headerName: 'Capacity',
        flex: 1,
      },
    {
      field: 'edit',
      headerName: 'Edit',
      flex: 1,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          setClicked(true);
          setSelectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: 'delete',
      headerName: 'Delete',
      flex: 1,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deleteWarehouse = useMutation(
          (id) => api.delete(`store/warehouses/${id}`),
          {
            onSuccess: () => {
              setNotification('Warehouse Deleted Successfully ');
            },
            onSettled: () => {
              queryClient.invalidateQueries(['warehouses']);
            },
          }
        );
        const onDelete = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              'Are you sure you want to delete the selected warehouse?'
            )
          ) {
            return;
          }
          deleteWarehouse.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onDelete}>
              <Typography color={colors.grey[100]} sx={{ ml: '5px' }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];

  const handleClick = (e) => {
    e.preventDefault();
    setClicked(true);
  };

  const handleChange = (e) => {
    setSelectedRow({
      ...selectedRow,
      [e.target.name]: e.target.value,
    });
  };

  const postWarehouse = useMutation((data) => api.post('store/warehouses', data), {
    onSuccess: () => {
      queryClient.invalidateQueries(['warehouses']);
      setNotification('Warehouse created successfully');
      setClicked(false);
    },
    onError: (err) => {
      setClicked(true);
      if (err.response && err.response.status === 422) {
        if (err.response.data.errors) {
          setErrors(err.response.data.errors);
        }
      }
    },
  });
  const onSubmit = (e) => {
    e.preventDefault();
    const payload = {
      name: selectedRow.name,
      location: selectedRow.location,
      capacity: selectedRow.capacity,
    };
    if (selectedRow.id) {
      api
        .put(`store/warehouses/${selectedRow.id}`, payload)
        .then(() => {
          setNotification('Warehouse updated successfully');
          queryClient.invalidateQueries(['warehouses']);
          setClicked(false);
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
          setClicked(true);
        });
    } else {
      postWarehouse.mutate(payload);
    }
  };

  return (
    <Box m="10px">
      <Header title="Warehouse Table" subtitle="List of warehouses" />
      <Box display="flex" justifyContent="center" alignItems="center">
        <IconButton
          sx={{
            boxShadow: '2px 2px 4px rgba(0,0,0,0.25)',
            background: colors.primary[400],
          }}
          onClick={handleClick}
        >
          <AddOutlined color={colors.grey[100]} />
        </IconButton>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={clicked ? 9 : 12}>
          <Box
            m="10px 0 0 0"
            height="67vh"
            sx={{
              '& .MuiDataGrid-root': {
                border: 'none',
              },
              '& .MuiDataGrid-cell': {
                borderBottom: 'none',
              },
              '& .name-column--cell': {
                color: colors.greenAccent[300],
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: colors.blueAccent[700],
                borderBottom: 'none',
              },
              '& .MuiDataGrid-virtualScroller': {
                backgroundColor: colors.primary[400],
              },
              '& .MuiDataGrid-footerContainer': {
                borderTop: 'none',
                backgroundColor: colors.blueAccent[700],
              },
              '& .MuiCheckBox-root': {
                color: `${colors.greenAccent[200]}!important`,
              },
              '& .MuiDataGrid-toolbarContainer .MuiButton-text': {
                color: `${colors.grey[100]}!important`,
              },
            }}
          >
            {isLoading ? (
              <_CircularProgress />
            ) : (
              <DataGrid columns={columns} rows={warehouses?.data || []} />
            )}
          </Box>
        </Grid>

        {clicked ? (
          <Grid item xs={12} sm={3} component="form" onSubmit={onSubmit}>
            <Typography variant="h5" color={colors.grey[100]} align="center">
              {selectedRow.id ? 'Edit Warehouse' : 'Add Warehouse'}
            </Typography>
            <TextField
              error={!errors.name == ''}
              id="name"
              margin="normal"
              fullWidth
              label="Warehouse Name"
              name="name"
              InputLabelProps={{
                color: 'success',
              }}
              value={selectedRow.name || ''}
              onChange={handleChange}
              helperText={
                errors.name ? errors.name[0] : null
              }
            />

            <TextField
              id="location"
              margin="normal"
              fullWidth
              label="Location"
              name="location"
              InputLabelProps={{
                color: 'success',
              }}
              value={selectedRow.location || ''}
              onChange={handleChange}
            />
             <TextField
              id="capacity"
              margin="normal"
              fullWidth
              label="Capacity"
              name="capacity"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              value={selectedRow.capacity || ''}
              onChange={handleChange}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 2, py: 2 }}
              color="success"
              size="small"
            >
              submit
            </Button>

            <Button
              onClick={() => {
                setClicked(false);
                setSelectedRow({});
              }}
              fullWidth
              variant="contained"
              sx={{ mt: 2, py: 2 }}
              color="error"
              size="small"
            >
              cancel
            </Button>
          </Grid>
        ) : undefined}
      </Grid>
    </Box>
  );
}

export default Warehouse;
