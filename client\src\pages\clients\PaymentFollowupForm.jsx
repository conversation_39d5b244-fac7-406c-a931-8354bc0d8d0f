import { Box, Button, Grid, MenuItem, TextField } from '@mui/material';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import api from '../../api/config/axiosConfig';
import { useStateContext } from '../../context/ContextProvider';
import {
  collectionschedule,
  scheduleReminder,
} from '../../helper/scheduleHelper';

function PaymentFollowupForm({ clientId, editData, setEditData }) {
  const { setNotification, user, setErrors, errors } = useStateContext();
  const queryClient = useQueryClient();

  const [followup, setFollowup] = useState({
    collection_schedule: 'monthly',
    start_date: new Date().toISOString().slice(0, 10),
    end_date: new Date().toISOString().slice(0, 10),
    message: '',
    amount: '',
    reminder: -5,
    contract_length: '',
  });
  const handleChange = (e) => {
    console.log(e.target.value);
    setFollowup({
      ...followup,
      [e.target.name]: e.target.value,
    });
  };

  useEffect(() => {
    if (editData) {
      setFollowup(editData);
    }
  }, [editData]);

  const createMutation = useMutation(
    (payload) => api.post('payment-follow-up', payload),
    {
      onSuccess: () => {
        setNotification('Payment follow up record created successful');
        queryClient.invalidateQueries('PaymentByClient');
        setErrors({});
        setFollowup({
          collection_schedule: 'monthly',
          start_date: new Date().toISOString().slice(0, 10),
          end_date: new Date().toISOString().slice(0, 10),
          message: '',
          amount: '',
          contract_length: '',
          reminder: -5,
        });
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );
  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    const payload = {
      client_id: clientId,
      collection_schedule: data.get('collection_schedule'),
      start_date: data.get('start_date'),
      end_date: data.get('end_date'),
      reminder: data.get('reminder'),
      message: data.get('message'),
      amount: data.get('amount'),
      contract_length: data.get('contract_length'),
      user_id: user.id,
    };

    if (editData && editData.id) {
      api.put(`payment-follow-up/${editData.id}`, payload).then(() => {
        setNotification('Payment follow up recorded updated successfully');
        setEditData({});
        setFollowup({
          collection_schedule: 'monthly',
          start_date: new Date().toISOString().slice(0, 10),
          end_date: new Date().toISOString().slice(0, 10),
          message: '',
          amount: '',
          contract_length: '',
          reminder: -5,
        });
        queryClient.invalidateQueries('PaymentByClient');
      });
    } else {
      createMutation.mutate(payload);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={1}>
        <Grid item xs={12} sm={3}>
          <TextField
            error={errors.start_date}
            margin="normal"
            label="Starting Date"
            name="start_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={followup.start_date}
            helperText={errors.start_date && errors.start_date[0]}
          />
        </Grid>

        <Grid item xs={12} sm={3}>
          <TextField
            error={errors.end_date}
            margin="normal"
            label="End Date"
            name="end_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={followup.end_date}
            helperText={errors.end_date && errors.end_date[0]}
          />
        </Grid>

        <Grid item xs={12} sm={3}>
          <TextField
            error={errors.collection_schedule}
            margin="normal"
            label="Collection Schedule"
            name="collection_schedule"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={
              followup.collection_schedule
                ? followup.collection_schedule
                : 'monthly'
            }
            helperText={
              errors.collection_schedule && errors.collection_schedule[0]
            }
          >
            {collectionschedule.map((schedule) => (
              <MenuItem key={schedule.value} value={schedule.value}>
                {schedule.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>

        <Grid item xs={12} sm={3}>
          <TextField
            error={errors.reminder}
            margin="normal"
            label="Schedule Reminder"
            name="reminder"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={followup.reminder ? followup.reminder : -5}
            helperText={errors.reminder && errors.reminder[0]}
          >
            {scheduleReminder.map((schedule) => (
              <MenuItem key={schedule.value} value={schedule.value}>
                {schedule.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>

        <Grid item xs={12} sm={3}>
          <TextField
            error={errors.amount}
            margin="normal"
            label="collection Amount"
            name="amount"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={followup.amount}
            helperText={errors.amount && errors.amount[0]}
          />
        </Grid>

        <Grid item xs={12} sm={3}>
          <TextField
            error={errors.contract_length}
            margin="normal"
            label="Contract Length In Months"
            name="contract_length"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={followup.contract_length}
            helperText={errors.contract_length && errors.contract_length[0]}
          />
        </Grid>

        <Grid item xs={12} sm={3}>
          <TextField
            error={errors.message}
            margin="normal"
            label="Message"
            name="message"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={followup.message}
            helperText={errors.message && errors.message[0]}
          />
        </Grid>
      </Grid>

      <span style={{ float: 'inline-start' }}>
        <Button type="submit" variant="contained" color="success">
          {editData.id ? <>update</> : <>create</>}
        </Button>
      </span>
    </Box>
  );
}

export default PaymentFollowupForm;
