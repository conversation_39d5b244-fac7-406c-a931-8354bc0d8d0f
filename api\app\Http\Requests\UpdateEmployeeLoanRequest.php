<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateEmployeeLoanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'emp_basic_id'=>'required|numeric',
            'loan_type'=>'required|string',
            'loan_amount'=>'required|numeric',
            'loan_period'=>'required',
            'loan_date'=>'required|date',
            'paid_amount'=>'required|numeric',
            'loan_paid'=>'required|boolean',
            'reason'=>'required|string'
        ];
    }
}
