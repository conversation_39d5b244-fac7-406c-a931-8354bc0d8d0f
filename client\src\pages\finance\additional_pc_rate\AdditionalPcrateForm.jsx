import { <PERSON>, Grid, <PERSON><PERSON>ield, <PERSON>u<PERSON><PERSON>, <PERSON><PERSON> } from "@mui/material";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import React, { useEffect } from "react";
import api from "../../../api/config/axiosConfig";
import { useState } from "react";
import { useStateContext } from "../../../context/ContextProvider";

function AdditionalPcrateForm({ formData, editData, setEditData }) {
  const queryClient = useQueryClient();
  const { data: pc_rate_type } = useQuery(
    ["pc_rate_type"],
    async () =>
      await api.get("pc_rate_type").then(({ data }) => {
        return data;
      }),
    {
      refetchOnWindowFocus: false,
      staleTime: 60000,
    }
  );
  const [pcrate, setPcRate] = useState({
    from_date: "",
    to_date: "",
    pc_type: "",
    no_pc: "",
    pc_rate_amount: "",
  });

  useEffect(() => {
    if (editData) {
      setPcRate(editData);
    }
  }, [editData]);

  const { setNotification, user } = useStateContext();
  const handleChange = (e) => {
    setPcRate({
      ...pcrate,
      [e.target.name]: e.target.value,
    });
  };
  const createPcRate = useMutation((data) => api.post("addpcrate", data), {
    onSuccess: () => {
      setNotification("Additonal pc rate inserted successfully");
      queryClient.invalidateQueries({ queryKey: "addpcrate" });
    },
  });
  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);

    const payload = {
      emp_basic_id: formData.emp_basic_id,
      client_id: formData.client_id,
      from_date: data.get("from_date"),
      to_date: data.get("to_date"),
      pc_type: data.get("pc_type"),
      penality_rate: data.get("penality_rate"),
      pc_rate_amount: data.get("pc_rate_amount"),
      no_pc: data.get("no_pc"),
      user_id: user.id,
    };

    if (editData && editData.id) {
      api.put(`addpcrate/${editData.id}`, payload).then(() => {
        setNotification("Additional pcrate record updated successfully");
        setEditData({});
        queryClient.invalidateQueries({ queryKey: "addpcrate" });
      });
    } else {
      createPcRate.mutate(payload);
    }
    
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="From Date"
            name="from_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={pcrate.from_date || new Date().toISOString().slice(0, 10)}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="To  Date"
            name="to_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={pcrate.to_date || new Date().toISOString().slice(0, 10)}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Pc Rate Type"
            name="pc_type"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={pcrate.pc_type ? pcrate.pc_type : ""}
          >
            {pc_rate_type ? (
              pc_rate_type.map((rate) => (
                <MenuItem key={rate.id} value={rate.pc_rate_type}>
                  {rate.pc_rate_type}
                </MenuItem>
              ))
            ) : (
              <MenuItem value="">select pc type</MenuItem>
            )}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="pc rate amount"
            name="pc_rate_amount"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={pcrate.pc_rate_amount ? pcrate.pc_rate_amount : ""}
          >
            {pc_rate_type ? (
              pc_rate_type.map((value) => (
                <MenuItem key={value.id} value={value.pc_rate_amount}>
                  {value.pc_rate_amount}
                </MenuItem>
              ))
            ) : (
              <MenuItem value="">select pcrate amount</MenuItem>
            )}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="no_pc"
            name="no_pc"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={pcrate.no_pc}
          />
        </Grid>
      </Grid>
      <span style={{ float: "inline-start" }}>
        <Button type="submit" variant="contained" color="success">
          {editData.id ? <>update</> : <>create</>}
        </Button>
      </span>
    </Box>
  );
}

export default AdditionalPcrateForm;
