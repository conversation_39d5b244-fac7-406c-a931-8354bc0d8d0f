import {
  Box,
  Checkbox,
  FormControl,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  ListItemText,
  Grid,
} from "@mui/material";
import React, { useState } from "react";
import DateDiffrenceComponent from "./DateDiffrenceComponent";
import TaxCenterDropDown from "./TaxCenterDropDown";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
const FilterTax = ({ passFromDate, passToDate, passCenter }) => {
  const [selectedComponents, setSelectedComponents] = useState([]);
  const handleChange = (event) => {
    const {
      target: { value },
    } = event;
    setSelectedComponents(value);
  };
  const handleDateChanges = (fromDate, toDate) => {
    passFromDate(fromDate);
    passToDate(toDate);
  };
  const handleSelectedCenter = (center) => {
    passCenter(center);
  };
  const componentList = [
    {
      name: "DateDiffrence",
      component: (
        <DateDiffrenceComponent
          labelOne="fromDate"
          labelTwo="toDate"
          onDateChange={handleDateChanges}
        />
      ),
    },
    {
      name: "TaxCenter",
      component: <TaxCenterDropDown onSelectedCenter={handleSelectedCenter} />,
    },
  ];
  return (
    <div style={{ display: "flex" }}>
    <div>
      <FormControl sx={{ m: 1, width: 300 }}>
        <InputLabel id="demo-multiple-checkbox-label">
          Select Filtering Components
        </InputLabel>
        <Select
          labelId="demo-multiple-checkbox-label"
          id="demo-multiple-checkbox"
          multiple
          value={selectedComponents}
          onChange={handleChange}
          input={<OutlinedInput label="Select Filtering Methods" />}
          renderValue={(selected) => selected.join(", ")}
          MenuProps={MenuProps}
        >
          {componentList.map((component) => (
            <MenuItem key={component.name} value={component.name}>
              <Checkbox
                checked={selectedComponents.indexOf(component.name) > -1}
              />
              <ListItemText primary={component.name} />
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </div>
    <Grid container spacing={1}>
      {componentList.map((component) => {
        if (selectedComponents.includes(component.name)) {
          return (
            <Grid item xs={12} sm={6} md={4} key={component.name}>
              {component.component}
            </Grid>
          );
        }
        return null;
      })}
    </Grid>
  </div>
  )
};

export default FilterTax;
