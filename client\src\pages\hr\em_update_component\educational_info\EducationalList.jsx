import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import React from "react";
import api from "../../../../api/config/axiosConfig";
import EditRenderCell from "../../../../components/EditRenderCell";
import DeleteRenderCell from "../../../../components/DeleteRenderCell";
import { useStateContext } from "../../../../context/ContextProvider";
import Header from "../../../../components/Header";
import { DataGrid } from "@mui/x-data-grid";
import { CircularProgress } from "@mui/material";

const EducationalList = ({ id, selectedRow }) => {
  const queryClient = useQueryClient();
  const { setNotification } = useStateContext();
  const {
    data: education,
    isLoading,
    isFetched,
  } = useQuery(
    ["education", id],
    () =>
      api
        .get(`emp_basics/${id}`, { params: { all: "fetch_education" } })
        .then(({ data }) => {
          if (data.data.emp_education) {
            return data.data.emp_education;
          }
        }),
    {
      enabled: !!id,
      staleTime: 600000,
      refetchOnWindowFocus: false,
    }
  );
  const deleteRow = useMutation((id) => api.delete(`emp_education/${id}`), {
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: "education" });
      setNotification("Educational  record is deleted Successfully");
    },
  });
  const handleDelete = (id) => {
    if (
      !window.confirm("Are you sure you want to delete the selected  record?")
    ) {
      return;
    }
    deleteRow.mutate(id);
  };
  const columns = [
    {
      field: "id",
      headerName: "Id",
    },
    {
      field: "institution",
      headerName: "Institution",
    },
    {
      field: "level",
      headerName: "Level",
    },
    {
      field: "grade",
      headerName: "Grade",
    },
    {
      field: "certefication",
      headerName: "Certefication",
    },
    {
      field: "start_date",
      headerName: "startDate",
    },
    {
      field: "end_date",
      headerName: "endDate",
    },
    {
      field: "edit",
      headerName: "Edit",
      filterable: false,
      sortable: false,
      hide: false,
      renderCell: (params) => (
        <EditRenderCell
          onClick={(e) => {
            e.stopPropagation();
            selectedRow(params.row);
          }}
          title="Edit"
        />
      ),
    },

    {
      field: "delete",
      headerName: "Delete",
      filterable: false,
      sortable: false,
      hide: false,
      renderCell: (params) => (
        <DeleteRenderCell handleDelete={() => handleDelete(params.row.id)} />
      ),
    },
  ];
  return (
    <div style={{ marginTop: "15px" }}>
      <Header title="List of Educational Information" heading="h5" />
      {!isFetched && <CircularProgress />}
      {isFetched && (
        <DataGrid columns={columns} rows={education} hideFooter autoHeight />
      )}
    </div>
  );
};

export default EducationalList;
