<?

namespace App;

use App\Models\ContractFollowUp;
use App\Models\PaymentFollowUp;
use App\Models\User;
use App\Notifications\ContractFollowUpNotification;
use App\Notifications\PaymentFollowupNotification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\ServiceProvider;

class ContractFollowUpScheduler 
{
    public function contract()
    {
        $results = ContractFollowUp::with(['client'])
            ->whereMonth('last_triggerd_date', '=', date('m', strtotime(now())))
            ->whereYear('last_triggerd_date', '=', date('Y', strtotime(now())))
            ->whereDate('end_date', '>=', now())
            ->get();
        foreach ($results as $result) {
            $overdue = false;
            if ($result['last_triggerd_date'] <= now()) {
                $dueDateLefts = Carbon::parse($result['last_schedule'])->diffInDays(now()) + 1;
                if (date($result['last_schedule']) <= date($result['last_triggerd_date'])) {
                    $overdue = true;
                }
                $users = User::whereIn('role', ['admin', 'hr'])->get();
                Notification::send($users, new ContractFollowUpNotification(
                    contract_id: $result['id'],
                    client_id: $result['client_id'],
                    daysLeftUntilDue: $dueDateLefts,
                    start_date: $result['start_date'],
                    end_date: $result['end_date'],
                    contract_document: $result['contract_document'],
                    contract_schedule: $result['contract_schedule'],
                    message: $result['message'],
                    last_triggerd_date: $result['last_triggerd_date'],
                    last_schedule: $result['last_schedule'],
                    overdue: $overdue,
                    company_name: $result->client->company_name,
                    triggering_date: $result['last_triggerd_date']
                ));
            }
        }
    }

    public function payment()
    {
        $results = PaymentFollowUp::with(['client'])->whereMonth('last_triggerd_date', '=', date('m', strtotime(now())))->whereYear('last_triggerd_date', '=', date('Y', strtotime(now())))->whereDate('end_date', '>=', now())->get();

        foreach ($results as $result) {
            $overdue = false;
            if ($result['last_triggerd_date'] <= now()) {
                $dueDateLefts = Carbon::parse($result['last_schedule'])->diffInDays(now()) + 1;
                if (date($result['last_schedule']) <= date($result['last_triggerd_date'])) {
                    $overdue = true;
                }
                $users = User::whereIn('role', ['admin', 'hr'])->get();
                Notification::send($users, new PaymentFollowupNotification(payment_id: $result['id'], client_id: $result['client_id'], daysLeftUntilDue: $dueDateLefts, start_date: $result['start_date'], end_date: $result['end_date'], collection_schedule: $result['collection_schedule'], message: $result['message'], last_schedule: $result['last_schedule'], overdue: $overdue, company_name: $result->client->company_name, triggering_date: $result['last_triggerd_date']));
            }
        }
    }
}
