import api from "../config/axiosConfig";

export const getUsers = async (setUsers) => {
  api
    .get("users")
    .then(({ data }) => {
      //
      setUsers(data);
    })
    .catch((err) => {});
};

export const onDelete = (u, setNotification) => {
  api.delete(`/users/${u.id}`).then(() => {
    getUsers(u);
    setNotification("user is successfully deleted");
  });
};

export const getSingleUser = (id, setFormdata,setCheckedItems) => {
  api
    .get(`/users/${id}`)
    .then(({ data }) => {
      setFormdata(data.data);
      setCheckedItems(JSON.parse(data.data.userPermission));
    })
    .catch(() => {
    });
};

export const updateUsers = (formdata, setNotification,checkedItems, setErrors) => {
  api
    .put(`/users/${formdata.id}`, formdata)
    .then(() => {
      api.put(`/permissions/${formdata.permissionId}`,{user_id:formdata.id,roles:formdata.role,routes:JSON.stringify(checkedItems)}).then(()=>setNotification("user successfully updated"));
      
    })
    .catch((err) => {
      const res = err.response;
      if (res && res.status === 422) {
        console.log(res.data);
        setErrors(res.data.errors);
      }
    });
};
