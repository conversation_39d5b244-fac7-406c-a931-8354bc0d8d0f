import { <PERSON>, TextField, useTheme, Button } from "@mui/material";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import React, { useState } from "react";
import api from "../../api/config/axiosConfig";
import CustomPopOver from "../../components/CustomPopOver";
import { tokens } from "../../utils/theme";
import { useStateContext } from "../../context/ContextProvider";

function SetExchangeRate({ isopen, anchorEl, handleClose }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();

  const queryClient = useQueryClient();
  const { data: exchange } = useQuery(
    ["exchange"],
    async () =>
      await api.get("exchange_rate").then(({ data }) => {
        setExchangeRate(data.exchange_rate);
        return data;
      }),
    {
      staleTime: 600000,
      refetchOnWindowFocus: false,
    }
  );
  const [exchangeRate, setExchangeRate] = useState("");
  const createExchangerate = useMutation(
    (data) => api.post("exchange_rate", data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(["exchange"]);
        setNotification("Exchange rate created successfully");
      },
    }
  );
  const handleSubmit = (e) => {
    e.preventDefault();
    if (exchange.id) {
      api
        .put(`exchange_rate/${exchange.id}`, { exchange_rate: exchangeRate })
        .then(() => {
          queryClient.invalidateQueries(["exchange"]);
          setNotification("Exchange rate updated successfully");
        });
      handleClose();
    } else {
      createExchangerate.mutate({ exchange_rate: exchangeRate });
      handleClose();
    }
  };
  return (
    <CustomPopOver
      anchorEl={anchorEl}
      isopen={isopen}
      handleClose={handleClose}
    >
      <Box
        component="form"
        onSubmit={handleSubmit}
        sx={{
          background: colors.primary[400],
          color: colors.grey[100],
          padding: 2,
        }}
      >
        <TextField
          label="Set Exchange Rate"
          name="exchange_rate"
          onChange={(e) => setExchangeRate(e.target.value)}
          value={exchangeRate}
          type="number"
          InputLabelProps={{
            color: "success",
          }}
        />
        <Box sx={{ pt: 1, display: "flex", justifyContent: "space-between" }}>
          <Button sx={{ color: colors.grey[100] }}>cancel</Button>
          <Button type="submit" sx={{ color: colors.grey[100] }}>
            submit
          </Button>
        </Box>
      </Box>
    </CustomPopOver>
  );
}

export default SetExchangeRate;
