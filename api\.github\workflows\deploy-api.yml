name: Deploy Edomias API to cPanel

on:
  push:
    branches:
      - main # or your main branch name

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Set up PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1' # Use a PHP version compatible with your Laravel project
        extensions: mbstring, pdo_mysql, bcmath, dom, fileinfo, json, openssl, tokenizer
        ini-values: post_max_size=256M, upload_max_filesize=256M
        tools: composer

    - name: Install Composer dependencies
      run: composer install --no-dev --prefer-dist
      working-directory: ./edomias-api

    - name: Copy .env.example to .env
      run: cp .env.example .env
      working-directory: ./edomias-api

    - name: Generate application key
      run: php artisan key:generate
      working-directory: ./edomias-api

    - name: Run migrations (Optional - use with caution)
      run: php artisan migrate --force
      working-directory: ./edomias-api
      env:
        APP_ENV: production # Ensure this is set for production environment
        DB_DATABASE: ${{ secrets.DB_DATABASE }}
        DB_USERNAME: ${{ secrets.DB_USERNAME }}
        DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
      # Uncomment the following line if you want to manually approve migrations
      # if: github.ref == 'refs/heads/main' && contains(github.event.commits[0].message, '[migrate]')

    - name: Deploy to cPanel
      uses: SamKirkland/FTP-Deploy-Action@4.3.0
      with:
        server: ${{ secrets.FTP_SERVER }}
        username: ${{ secrets.FTP_USERNAME }}
        password: ${{ secrets.FTP_PASSWORD }}
        local-dir: ./edomias-api/ # The root directory of your Laravel project
        server-directory: public_html/edomias-api/ # Adjust this to your cPanel deployment path
        protocol: ftp # or ftps, sftp if supported by your cPanel
        state-name: .ftp-deploy-state-api.json
        dry-run: false
