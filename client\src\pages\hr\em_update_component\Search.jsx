import {
  Box,
  Button,
  Card,
  CircularProgress,
  Grid,
  InputAdornment,
  List,
  ListItem,
  ListItemText,
  TextField,
  useTheme,
  Typography,
} from '@mui/material';

import { SearchOutlined } from '@mui/icons-material';
import debounce from 'lodash.debounce';
import { useCallback, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import api from '../../../api/config/axiosConfig';
import _CircularProgress from '../../../components/_CircularProgress';
import Header from '../../../components/Header';
import { tokens } from '../../../utils/theme';

const ListTile = ({ name, position, to }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  return (
    <Grid item xs={12} sm={6} component={Link} to={`/employees/${to}`}>
      <List
        sx={{ boxShadow: '0 1px 1px rgba(0, 0, 0, 0.2)', borderRadius: 1.5 }}
        style={{
          color: colors.grey[100],
          background: colors.primary[400],
          padding: '0px ',
        }}
      >
        <ListItem>
          <ListItemText
            primary={name}
            secondary={position}
            primaryTypographyProps={{ fontWeight: 'bold' }}
            secondaryTypographyProps={{
              fontSize: '0.8rem',
              //color: 'rgba(255,255,255,0.7)',
            }}
          />
        </ListItem>
      </List>
    </Grid>
  );
};
function Search() {
  const [result, setResult] = useState([]);
  const [loading, setLoading] = useState(false);
  const [query, setQuery] = useState('');

  const handleQueryChange = useCallback(
    debounce((query) => {
      if (query) {
        setLoading(true);

        const [firstName, middleName] = query.split(' ');
        //console.log(firstName, ':', middleName);

        api
          .get('emp_basics', {
            params: { name: firstName, middle_name: middleName },
          })
          .then(({ data }) => {
            setResult(data.data);
            console.log(data.data);
            setLoading(false);
          })
          .catch((err) => {
            setError(err);
            setLoading(false);
          });
      } else {
        setResult([]);
      }
    }, 300),
    []
  );
  // useEffect(() => {
  //   handleQueryChange(query);
  // }, [query, handleQueryChange]);

  return (
    <Box m="20px">
      <Header title="Employee Search" subtitle="search the employee first" />
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        sx={{ maxWidth: '30%' }}
      >
        <TextField
          label="Search"
          name="query"
          InputLabelProps={{
            color: 'success',
          }}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <SearchOutlined />
              </InputAdornment>
            ),
          }}
          fullWidth
          type="search"
          variant="standard"
          value={query}
          onChange={(val) => setQuery(val.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              //console.log(query);
              handleQueryChange(query);
            }
          }}
        />
      </Box>
      {loading && <CircularProgress color="secondary" sx={{ mt: '10px' }} />}

      {!loading && query && result.length === 0 && (
        <div style={{ marginTop: '10px' }}>
          No results found press Enter to Search
        </div>
      )}

      {!loading && result.length > 0 && (
        <Grid container maxWidth="50%" spacing={1} marginTop={1}>
          {result.map((res) => (
            <ListTile
              key={res.id}
              name={`${res.first_name} ${res.middle_name} ${res.last_name}`}
              position={
                <>
                  <Typography
                    variant="body2"
                    component="span"
                    sx={{
                      color:
                        res.assigned_client === 'Not Assigned'
                          ? 'red'
                          : 'green',
                    }}
                  >
                    {res.assigned_client}
                  </Typography>
                  <br />
                  <Typography variant="body2" component="span">
                    Position: {res.position}
                  </Typography>
                </>
              }
              to={res.id}
            />
          ))}
        </Grid>
      )}
    </Box>
  );
}

export default Search;
