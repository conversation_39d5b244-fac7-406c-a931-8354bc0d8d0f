<?php

namespace App\Http\Controllers;

use App\Models\ContractUpdated;
use Illuminate\Http\Request;

class ContractUpdatedController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ContractUpdated  $contractUpdated
     * @return \Illuminate\Http\Response
     */
    public function show(ContractUpdated $contractUpdated)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\ContractUpdated  $contractUpdated
     * @return \Illuminate\Http\Response
     */
    public function edit(ContractUpdated $contractUpdated)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\ContractUpdated  $contractUpdated
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ContractUpdated $contractUpdated)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ContractUpdated  $contractUpdated
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractUpdated $contractUpdated)
    {
        //
    }
}
