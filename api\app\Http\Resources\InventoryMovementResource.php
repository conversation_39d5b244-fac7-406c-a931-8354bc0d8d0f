<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class InventoryMovementResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'item' => new StoreItemResource($this->whenLoaded('item')),
            'movement_type' => $this->movement_type,
            'quantity' => $this->quantity,
            'notes' => $this->notes,
            'creator' => new UserResource($this->whenLoaded('creator')),
            'request' => new RequestResource($this->whenLoaded('request')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}