import { CloseOutlined } from "@mui/icons-material";
import { Al<PERSON>, IconButton, Snackbar } from "@mui/material";
import React from "react";
import { useState } from "react";

function Toast({ message, duration = 5000, onClose }) {
  const [open, setOpen] = useState(true);
  const handleClose = (e, reason) => {
    if (reason == "clickaway") {
      return;
    }
    setOpen(false);
    onClose();
  };
  return (
    <Snackbar
      color="success"
      variant="filled"
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      open={open}
      autoHideDuration={duration}
      onClose={handleClose}
      action={
        <IconButton
          size="small"
          variant="filled"
          color="success"
          onClick={handleClose}
        >
          <CloseOutlined fontSize="small" />
        </IconButton>
      }
    >
      <Alert onClose={handleClose} severity="success" sx={{ width: '100%' }} variant="filled">
        {message}
      </Alert>
    </Snackbar>
  );
}

export default Toast;
