import { Box, Grid } from "@mui/material";
import React from "react";
import { useState } from "react";
import { useParams } from "react-router-dom";
import Header from "../../../components/Header";
import LeftSide from "../../../components/LeftSide";
import RightSide from "../../../components/RightSide";
import TransferdList from "./TransferdList";
import TransferEmp from "./TransferEmp";
import EmpLocation from "../assign/EmpLocation";

function Transfer() {
  const links = ["Transfer Employee", "List Of Transferd Employee", 'Find Employee'];
  const components = [TransferEmp, TransferdList, EmpLocation];
  const [selectedLinkIndex, setSelectedLinkIndex] = useState(0);
  const handleLinkClick = (index) => {
    setSelectedLinkIndex(index);
  };
  return (
    <Box margin="10px">
      <Header title="Transfer Employee" subtitle="transfer employee here" />
      <Grid container spacing={2}>
        <LeftSide
          links={links}
          handleLinkClick={handleLinkClick}
          activeLink={setSelectedLinkIndex}
          m={12}
        />
        <RightSide
          components={components}
          selectedLinkIndex={selectedLinkIndex}
        />
      </Grid>
    </Box>
  );
}

export default Transfer;
