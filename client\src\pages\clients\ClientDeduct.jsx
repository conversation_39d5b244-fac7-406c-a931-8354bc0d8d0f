import { Box, Grid, Paper, useTheme } from "@mui/material";
import React from "react";
import Header from "../../components/Header";
import SearchBar from "../../components/SearchBar";
import { tokens } from "../../utils/theme";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import api from "../../api/config/axiosConfig";
import DeductForm from "./DeductForm";
import ListOfClientDeduct from "./ListOfClientDeduct";

function ClientDeduct() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [clicked, setClicked] = useState(false);
  const [clientId, setClientId] = useState("");
  const [editData, setEditData] = useState({});
  const { data: client, isFetched } = useQuery(
    ["client"],
    () => api.get("client").then(async ({ data }) => await data.data),
    { refetchOnWindowFocus: false, staleTime: 60000 }
  );
 
  const onSelectedRow = (value) => {
    setEditData(value);
  };
  const handleClientSelect = ({ value, clicked }) => {
    setClientId(value.id);
    setClicked(clicked);
  };
  return (
    <Box m={2}>
      <Header title="Client Deduct" />
      <Grid container spacing={2}>
        <Grid item xs={12} sm={4}>
          <Paper
            sx={{
              padding: "1rem",
              color: colors.grey[100],
              background: colors.primary[400],
            }}
          >
            <SearchBar client={client} onClientSelect={handleClientSelect} />
          </Paper>
        </Grid>
        <Grid item xs={12} sm={8}>
          {clicked && (
            <>
              <Paper
                sx={{
                  padding: "1rem",
                  color: colors.grey[100],
                  background: colors.primary[400],
                }}
              >
                <DeductForm clientId={clientId} editData={editData} />
              </Paper>
              <Paper
                sx={{
                  padding: "1rem",
                  color: colors.grey[100],
                  background: colors.primary[400],
                }}
              >
                <ListOfClientDeduct
                  clientId={clientId}
                  selectedRow={onSelectedRow}
                />
              </Paper>
            </>
          )}
        </Grid>
      </Grid>
    </Box>
  );
}

export default ClientDeduct;
