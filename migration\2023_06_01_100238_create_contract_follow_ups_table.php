<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contract_follow_ups', function (Blueprint $table) {
            $table->id();
            // $table->unsignedInteger('payment_follow_up_id');
            // $table->foreign('payment_follow_up_id')->references('id')->on('payment_follow_ups')->onDelete('cascade');
            $table->integer('client_id');
            $table->date('start_date')->format('Y-m-d');
            $table->date('end_date')->format('Y-m-d');
            $table->date('last_schedule')->format('Y-m-d');
            $table->date('last_triggerd_date')->format('Y-m-d');

            $table->string('contract_document')->nullable();
            $table->string('contract_schedule')->default('monthly');

            $table->integer('reminder')->default(-5);
            $table->integer('amount')->nullable()->default(0);
            $table->longText('message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contract_follow_ups');
    }
};
