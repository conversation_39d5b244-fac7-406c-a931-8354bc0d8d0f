# HR Module Summary

## Frontend (React)

The HR module frontend is located in `client/src/pages/hr` and is built using React and Vite. It consists of the following main components:

*   **Assign:** Manages employee assignments to clients or projects.
*   **em\_update\_component:**  Handles updating various employee-related information, such as basic info, bank details, loans, sponsorships, and status.
*   **Leave:** Manages employee leave requests and summaries.
*   **registration\_component:** Handles employee registration and related information such as address, bank details and allowances.
*   **transfer\_employee:** Manages employee transfers.

## Backend (Laravel)

The HR module backend is built using Laravel and provides API endpoints for managing employee data. The main models and controllers are:

*   **Models:**
    *   `EmpBasic`: Stores basic employee information.
    *   `EmployeeLoan`: Manages employee loans.
    *   `EmployeeSponser`: Manages employee sponsorships.
    *   `SponsoredEmployee`: Manages sponsored employees.
    *   `finance/EmployeeDeductable`: Manages employee deductions.
*   **Controllers:**
    *   `Api/EmpBasicController`: Handles basic employee data.
    *   `Api/EmployeeLoanController`: Handles employee loans.
    *   `Api/EmployeeSponserController`: Handles employee sponsorships.
    *   `Api/SponsoredEmpController`: Handles sponsored employees.
    *   `Api/finance/EmployeeDeductableController`: Handles employee deductions.
*   **Routes:**
    *   `/emp_basics`: Manages basic employee information.
    *   `/emp_details`: Manages employee details.
    *   `/emp_banks`: Manages employee bank information.
    *   `/emp_allowance`: Manages employee allowances.
    *   `/emp_loan`: Manages employee loans.
    *   `/emp_sponser`: Manages employee sponsors.
    *   `/emp_status`: Manages employee status.
    *   `/emp_education`: Manages employee education.
    *   `/emp_work_experiance`: Manages employee work experience.
    *   `/emp_emergency_contact`: Manages employee emergency contacts.
    *   `/family_info`: Manages employee family information.
    *   `/sponsord_emp`: Manages sponsored employees.
    *   `/award`: Manages employee awards.
    *   `/discipline`: Manages employee discipline.
    *   `/transfer`: Manages employee transfers.
    *   `/leave`: Manages employee leaves.
    *   `/emp_deductable`: Manages employee deductibles.

## Data Flow

The frontend components interact with the backend API endpoints to perform CRUD (Create, Read, Update, Delete) operations on employee data. For example, the `registration_component` likely uses the `/emp_basics` and related routes to create new employee records. The `Leave` component uses the `/leave` route to manage leave requests.

## Architecture

The HR module follows a typical Model-View-Controller (MVC) architecture. The Laravel backend provides the API endpoints and data models, while the React frontend provides the user interface and interacts with the API.