1. hire requist form yes/no (when, who, how much, level of salary)
2. ad for the position posted online
3. collect the applications filter the applicants based on the requirment
4. filtered applicants data are filled in the system and give notification to <PERSON> that this applicats for the role are sready for exam or interviw
5. GM sets date for intrview or exam 
6. hr is notify ed of the date contacts the applicats
7. exam score is filled in the system and ready for evaluation notify the approprate exam commite
8. com<PERSON>ie sees the score and choose the applicat from the list and notifys hr
9. hr hires that applicant fills the remaining data for the employee

10. for new hires hr is notifyed to evalute his/her work ethics with in 55 days of hairing that persion
11. then the commite sees the revaluatin data and decides to make him permanent or not.
12. commite list must be avaliable from the first time of the postion posting

1. overtime request form move finace overtime to operation overtime.
2. request must be approvid before it is added to payroll.

3.for bonus and insentives giving there must be a kpi dashbord
  - attendance 
  - overtime
  - department rating

4. search empoyee by id for hr
