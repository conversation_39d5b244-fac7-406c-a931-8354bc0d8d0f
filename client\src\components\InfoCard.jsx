import { Card, CardContent, Paper, Typography, useTheme } from "@mui/material";
import React from "react";
import { tokens } from "../utils/theme";

function InfoCard({ title, content, isDanger, onClick, isavailableLeft }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  return (
    <Card
      variant="elevation"
      style={{
        color: colors.grey,
        background: colors.primary[400],
        padding: 5,
        cursor: "pointer",
      }}
      onClick={onClick}
    >
      {isavailableLeft ? (
        <CardContent>
          <Typography
            variant="h4"
            component="div"
            gutterBottom
            sx={{ display: "flex", justifyContent: "center" }}
            color={isDanger ? "red" : "orange"}
          >
            {title}
          </Typography>
          <Typography
            color={isDanger ? "red" : "orange"}
            variant="body1"
            sx={{ display: "flex", justifyContent: "center" }}
          >
            {content}
          </Typography>
        </CardContent>
      ) : (
        <CardContent>
          <Typography
            variant="h4"
            component="div"
            gutterBottom
            sx={{ display: "flex", justifyContent: "center" }}
            color={isDanger ? "red" : colors.greenAccent[600]}
          >
            {title}
          </Typography>
          <Typography
            color={isDanger ? "red" : colors.grey[100]}
            variant="body1"
            sx={{ display: "flex", justifyContent: "center" }}
          >
            {content}
          </Typography>
        </CardContent>
      )}
    </Card>
  );
}

export default InfoCard;
