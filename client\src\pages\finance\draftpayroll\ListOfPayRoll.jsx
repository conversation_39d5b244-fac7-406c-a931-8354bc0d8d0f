import React, { useState } from "react";
import Header from "../../../components/Header";
import { DataGrid } from "@mui/x-data-grid";
import { useQuery } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";
import { Box, CircularProgress, useTheme, Typography } from "@mui/material";
import { tokens } from "../../../utils/theme";
function ListOfPayRoll({ clientId }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [month, setMonth] = useState("");
  const [year, setYear] = useState("");
  const [columnTotals, setColumnTotals] = useState([]);

  const [companyName, setCompanyName] = useState("");
  const {
    data: draft,
    isLoading,
    isFetched,
  } = useQuery(
    ["getpayroll", clientId],
    () =>
      api
        .get("getdraft", { params: { client_id: clientId } })
        .then(({ data }) => {
          console.log(data)
          data.empPayroll.map((row) => {
            setMonth(row.month);
            setYear(row.year);
            setCompanyName(row.company_name);
          });
          setColumnTotals(data.summary);
          return data.empPayroll.map((row, index) => ({
            ...row,
            id: index + 1,
            fullname: `${row.first_name} ${row.middle_name} ${row.last_name}`,
          }));
        }),
    {
      enabled: !!clientId,
    }
  );
  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      flex: 0.3,
    },
    {
      field: 'fullname',
      headerName: 'FullName',
      width: '200',
    },
    {
      field: 'basic_salary',
      headerName: 'BasicSalary',
    },
    {
      field: 'total_pcrate_salary',
      headerName: 'PcRateSalary',
    },
    {
      field: 'total_add_pcrate_salary',
      headerName: 'Total Addtional PcrateSalary',
      width: '150',
    },
    {
      field: 'over_time',
      headerName: 'overtime',
    },
    {
      field: 'total_additional_hours',
      headerName: 'Total Additional Hours',
      width: '130',
    },
    {
      field: 'absence_deduction_summry',
      headerName: 'absence Deduction summry',
      width: '130',
    },
    {
      field: 'emp_client_additional',
      headerName: 'Client Additional',
    },
    {
      field: 'taxable_position_allowance',
      headerName: 'Taxable Position Allowance',
    },
    {
      field: 'taxable_housing_allowance',
      headerName: 'Taxable Housing Allowance',
    },
    {
      field: 'transport_allowance',
      headerName: 'Transport Allowance',
    },
    {
      field: 'trans_ded',
      headerName: 'Transport Deduction',
    },
    {
      field: 'total_add_Deduction',
      headerName: 'Total Add Deduction',
    },
    {
      field: 'total_taxable_dsa_salary',
      headerName: 'Taxable Dsa Allowance',
    },

    {
      field: 'pensionEmp',
      headerName: 'pension Emp',
    },
    {
      field: 'pensionComp',
      headerName: 'pension Comp',
    },
    {
      field: 'providentEmp',
      headerName: 'provident Emp',
    },
    {
      field: 'providentComp',
      headerName: 'provident Comp',
    },
    {
      field: 'medical_cost',
      headerName: 'MedicalCost',
    },
    {
      field: 'loan',
      headerName: 'Company Loan',
    },
    {
      field: 'labor_loan',
      headerName: 'Labor Loan',
    },
    {
      field: 'costsharing_loan',
      headerName: 'Cost Sharing Loan',
    },
    {
      field: 'total_other_loan',
      headerName: 'Loan',
    },

    {
      field: 'additional_nontaxable_allowance',
      headerName: 'NonTax Additional Allowance',
      width: '130',
    },
    {
      field: 'nontax_transport_allowance',
      headerName: 'NonTax Transport Allowance',
      width: '120',
    },
    {
      field: 'nontax_desert_allowance',
      headerName: 'NonTax Desert Allowance',
      width: '120',
    },
    {
      field: 'nontax_mobile_allowance',
      headerName: 'NonTax MobileAllowance',
      width: '120',
    },
    {
      field: 'nontax_cleaning_allowance',
      headerName: 'NonTax Cleaning Allowance',
      width: '120',
    },
    {
      field: 'nontax_other_allowance',
      headerName: 'NonTax Other Allowance',
    },
    {
      field: 'nontax_position_allowance',
      headerName: 'NonTax Position Allowance',
      width: '120',
    },
    {
      field: 'nontax_dsa_allowance',
      headerName: 'NonTax Dsa Allowance',
    },
    {
      field: 'sum_taxable_income',
      headerName: 'Sum Taxable Income',
      cellClassName: 'custom-tax',
      headerClassName: 'custom-tax',
    },
    {
      field: 'total_deduction_summry',
      headerName: 'Total Deduction Summry',
      width: '120',
      cellClassName: 'custom-ded',
      headerClassName: 'custom-ded',
    },
    {
      field: 'net_payment',
      headerName: 'net_payment',
      cellClassName: 'custom-pay',
      headerClassName: 'custom-pay-header',
    },
  ];
  const cols = [
    {
      field: "basicSalarySummary",
      headerName: "basic Salary",
    },
    {
      field: "pcratesalarySummary",
      headerName: "pcrate salary",
    },
    {
      field: "addPcratSalarySummary",
      headerName: "additional PcratSalary",
    },
    {
      field: "overtimeSummary",
      headerName: "overtime",
    },
    {
      field: "additionalHourSummary",
      headerName: "additional Hour",
    },
    {
      field: "absenceDeductionSummary",
      headerName: "absence Deduction",
    },
    // {
    //   field: "absence_deduction_summry",
    //   headerName: "absence deduction",
    // },
    {
      field: "taxableOtherAllowanceSummary",
      headerName: "taxable OtherAllowance",
      width: "110",
    },
    {
      field: "transportAllowanceSummary",
      headerName: "transport Allowance",
    },
    {
      field: "transportAllowanceDeductionSummary",
      headerName: "transport Allowance Deduction",
      width: "120",
    },
    {
      field: "totalAddeductionSummary",
      headerName: "total Addeduction ",
    },
    {
      field: "taxableDsaSalarySummary",
      headerName: "taxable Dsa Salary",
    },
    {
      field: "taxableIncome",
      headerName: "taxable Income",
    },
    {
      field: "pensionEmpSummary",
      headerName: "pension Emp",
    },
    {
      field: "pensionCompSummary",
      headerName: "pension Comp",
    },
    {
      field: "providentEmpSummary",
      headerName: "provident Emp",
    },
    {
      field: "providentCompSummary",
      headerName: "provident Comp",
    },
    {
      field: "totalOtherLoanSummary",
      headerName: "total OtherLoan",
    },
    {
      field: "totalDeductionSummary",
      headerName: "total Deduction",
    },
    {
      field: "additionalNontaxableSummary",
      headerName: "additional Nontaxable",
    },
    {
      field: "nontaxTransportAllowanceSummary",
      headerName: "nontax Transport Allowance",
    },
    {
      field: "nontaxMobileAllowanceSummary",
      headerName: "nontax Mobile Allowance",
    },
    {
      field: "nontaxCleaningAllowanceSummary",
      headerName: "nontax Cleaning Allowance",
    },
    {
      field: "nontaxOtherAllowanceSummary",
      headerName: "nontax Other Allowance",
    },
    {
      field: "nontaxDsaAllowanceSummary",
      headerName: "nontax Dsa Allowance",
    },
    {
      field: "nontaxPositionAllowanceSummary",
      headerName: "nontax Position Allowance",
    },
    {
      field: "nontaxDesertAllowanceSummary",
      headerName: "nontax Desert Allowance",
    },
    {
      field: "netPaySummary",
      headerName: "netPayment",
      cellClassName: "custom-pay",
    },
  ];
  return (
    <Box m="20px">
      {isLoading && <CircularProgress />}
      {isFetched && (
        <>
          <Header
            title={`DRAFT PAYROLL FOR THE MONTH OF ${month} ${year}`}
            subtitle={`Location: ${companyName}`}
          />
          <DataGrid
            autoHeight
            columns={columns}
            rows={draft}
            sx={{
              width: "100%",
              marginBottom: 5,
              "& .MuiDataGrid-columnHeaderTitle": {
                whiteSpace: "normal",
                wordWrap: "break-word",
                overflowWrap: "normal",
                lineHeight: "normal",
              },
              "& .MuiDataGrid-columnHeader": {
                height: "unset !important",
                paddingX: "20px",
              },
            }}
          />
          <Typography
            sx={{
              color: colors.grey[100],
              m: 0.5,
            }}
          >
            Draft Payroll summary
          </Typography>
          <Box
            width="100%"
            sx={{
              boxShadow: 2,
              border: 2,
              borderColor: colors.primary[400],
              "& .MuiDataGrid-cell:hover": {
                color: "primary.main",
              },
              "& .MuiDataGrid-columnHeaderTitle": {
                whiteSpace: "normal",
                wordWrap: "break-word",
                overflowWrap: "normal",
                lineHeight: "normal",
                width: "200",
              },
              "& .MuiDataGrid-columnHeader": {
                // Forced to use important since overriding inline styles
                height: "unset !important",
                paddingX: "10px",
                // width:'unset !important',
              },
            }}
          >
            <DataGrid
              rows={columnTotals}
              columns={cols}
              autoHeight
              hideFooter
              getRowClassName={() => "row"}
              headerHeight={80}
            />
          </Box>
        </>
      )}
    </Box>
  );
}

export default ListOfPayRoll;
