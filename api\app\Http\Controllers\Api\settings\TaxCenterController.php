<?php

namespace App\Http\Controllers\Api\settings;

use App\Http\Controllers\Controller;
use App\Models\settings\TaxCenter;
use Illuminate\Http\Request;

class TaxCenterController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(TaxCenter::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validated=$request->validate([
            'tax_center_name'=>'required|string',
            'region' => 'nullable|string',
        ]);
        $data = TaxCenter::create($validated);

        return response($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        return response(TaxCenter::find($id));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, TaxCenter $taxCenter)
    {
        $validated=$request->validate([
            'tax_center_name'=>'required|string',
            'region' => 'nullable|string',
        ]);
        // $taxCenter->update(
        //     [
        //         'tax_center_name' => $request->input('tax_center_name'),
        //     ]
        // );
        $taxCenter->update($validated);

        return response($taxCenter);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(TaxCenter $taxCenter)
    {
        $taxCenter->delete();
        return response('', 204);
    }
}
