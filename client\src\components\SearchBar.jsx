import {TextField, useTheme, Autocomplete } from "@mui/material";
import { useState } from "react";
import Minisearch from "minisearch";
import { tokens } from "../utils/theme";
import { useClientData } from "../api/userApi/clinetHook";

function SearchBar({ onClientSelect, variant}) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [selected, setSelected] = useState(0);
  const [clientId, setClientId] = useState("");
  const [results, setResults] = useState([]);
  const { data: client, isFetched } = useClientData();

  const miniSearch = new Minisearch({
    fields: ["company_name"],
    storeFields: ["id", "company_name"],
  });

  if (client) {
    miniSearch.addAll(client);
  }

  const handleChange = (e, value) => {
    setClientId(value ? value.company_name : '');
    const res = miniSearch.search(value ? value.company_name : '', {
      fuzzy: 0.3,
    });
    setResults(res);
  };
  const handleClientSelect = (value) => {
    onClientSelect({ value: value, clicked: true });
    setResults([]);
  };

  if (!client) {
    return 'loading ...';
  }

 return (
   <Autocomplete
     options={results.length > 0 ? results : client}
     getOptionLabel={(option) => option.company_name}
     onChange={(e, value) => {
       setClientId(value ? value.company_name : '');
       handleClientSelect(value);
     }}
     renderInput={(params) => (
       <TextField
         {...params}
         margin="normal"
         label="Search Site here"
         name="client_id"
         fullWidth
         variant={variant ?? 'standard'}
         type="search"
         InputLabelProps={{
           color: 'success',
         }}
         onChange={handleChange}
         value={clientId}
       />
     )}
   />
 );
}

export default SearchBar;
