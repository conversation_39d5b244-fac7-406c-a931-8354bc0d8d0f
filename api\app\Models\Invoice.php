<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_number',
        'export_order_id',
        'amount',
        'currency',
        'issue_date',
        'due_date',
        'paid_status',
    ];

    public function exportOrder()
    {
        return $this->belongsTo(ExportOrder::class);
    }

    public function customer()
    {
        return $this->hasOneThrough(Customer::class, ExportOrder::class);
    }
}
