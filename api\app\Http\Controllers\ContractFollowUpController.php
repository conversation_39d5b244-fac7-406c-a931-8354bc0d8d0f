<?php

namespace App\Http\Controllers;

use App\Models\ContractFollowUp;
use App\Http\Requests\StoreContractFollowUpRequest;
use App\Http\Requests\UpdateContractFollowUpRequest;
use App\Http\Resources\ContractFollowUpResource;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ContractFollowUpController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index()
    {
        return ContractFollowUpResource::collection(ContractFollowUp::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreContractFollowUpRequest  $request
     * @return \App\Http\Resources\ContractFollowUpResource
     *
     * This function stores a new contract follow-up record in the database.
     * It validates the request, stores the contract document (if any), and
     * calculates the last schedule date and last triggered date based on the
     * contract schedule and reminder fields.
     */
    public function store(StoreContractFollowUpRequest $request)
    {
        // Validate the request and store it in the $data variable
        $data = $request->validated();

        // Set the $lastSchedule variable to the current date
        $lastSchedule = now();

        // Initialize the $filePath variable to an empty string
        $filePath = '';

        // Check if the request has a file called 'contract_document'
        if ($request->hasFile('contract_document')) {
            // Store the file in the 'public/contractFiles' directory
            $filePath = $request->file('contract_document')->store('public/contractFiles');
        }

        // Check if the request has a contract_schedule field
        if ($data['contract_schedule']) {
            // Parse the start_date field into a Carbon object
            $startDate = Carbon::parse($data['start_date']);

            // Switch on the contract_schedule field to calculate the lastSchedule
            switch ($data['contract_schedule']) {
                    // If it's monthly, add 1 month to the start date
                case 'monthly':
                    $lastSchedule = $startDate->addMonth();
                    break;
                    // If it's quarterly, add 3 months to the start date
                case 'quarterly':
                    $lastSchedule = $startDate->addMonths(3);
                    break;
                    // If it's yearly, add 1 year to the start date
                case 'yearly':
                    $lastSchedule->addYear();
                    break;
                    // If it's semister, add 6 months to the start date
                case 'semister':
                    $lastSchedule->addMonths(6);
                    break;
                    // If it's none of the above, leave the lastSchedule as it is
                default:
                    $lastSchedule;
            }
        }

        // Create a clone of the lastSchedule
        $lastSche = clone $lastSchedule;

        // Calculate the lastTriggeredDate based on the reminder field
        $lastTriggeredDate = intval($data['reminder']) < 0 ? $lastSche->subDays(abs(intval($data['reminder']))) : $lastSche->addDays(abs(intval($data['reminder'])));

        // Create a new ContractFollowUp instance with the validated data
        $result = ContractFollowUp::create([
            'client_id' => $data['client_id'],
            'contract_schedule' => $data['contract_schedule'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'last_schedule' => $lastSchedule,
            'last_triggerd_date' => $lastTriggeredDate,
            'reminder' => $data['reminder'],
            'message' => $data['message'],
            'amount' => $data['amount'],
            'contract_document' => $filePath ? $filePath : '',
            'user_id' => $data['user_id'],
        ]);

        // Return a ContractFollowUpResource instance with the newly created record
        return new ContractFollowUpResource($result);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ContractFollowUp  $contractFollowUp
     * @return  \App\Http\Resources\ContractFollowUpResource
     */
    public function show(ContractFollowUp $contractFollowUp)
    {
        return new ContractFollowUpResource($contractFollowUp);
    }

    /**
     * Get all contract follow-ups for a client
     *
     * @param int $client_id
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */

    public function showByClient($client_id)
    {
        return ContractFollowUpResource::collection(ContractFollowUp::where('client_id', '=', $client_id)->get());
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateContractFollowUpRequest  $request
     * @param  \App\Models\ContractFollowUp  $contractFollowUp
     * @return \App\Http\Resources\ContractFollowUpResource
     */
    public function update(UpdateContractFollowUpRequest $request, ContractFollowUp $contractFollowUp)
    {

        $data = $request->all();

        $filePath = '';
        $lastSchedule = '';
        $lastTriggeredDate = '';
        $originalSchedule = $contractFollowUp->getOriginal('last_schedule');
        $originalReminder = $contractFollowUp->getOriginal('last_triggerd_date');

        if ($contractFollowUp->isDirty('contract_schedule')) {
            $originalSchedule = $contractFollowUp->getOriginal('last_schedule');
            $newSchedule = $data['contract_schedule'];
            // Perform actions based on the change
            Log::info("Contract schedule changed from $originalSchedule to $newSchedule");

            $lastSchedule = now();

            if ($data['contract_schedule']) {
                // Parse the start_date field into a Carbon object
                $startDate = Carbon::parse($data['start_date']);

                // Switch on the contract_schedule field to calculate the lastSchedule
                switch ($data['contract_schedule']) {
                        // If it's monthly, add 1 month to the start date
                    case 'monthly':
                        $lastSchedule = $startDate->addMonth();
                        break;
                        // If it's quarterly, add 3 months to the start date
                    case 'quarterly':
                        $lastSchedule = $startDate->addMonths(3);
                        break;
                        // If it's yearly, add 1 year to the start date
                    case 'yearly':
                        $lastSchedule->addYear();
                        break;
                        // If it's semister, add 6 months to the start date
                    case 'semister':
                        $lastSchedule->addMonths(6);
                        break;
                        // If it's none of the above, leave the lastSchedule as it is
                    default:
                        $lastSchedule;
                }

                $lastSche = clone $lastSchedule;

                // Calculate the lastTriggeredDate based on the reminder field
                $lastTriggeredDate = intval($data['reminder']) < 0 ? $lastSche->subDays(abs(intval($data['reminder']))) : $lastSche->addDays(abs(intval($data['reminder'])));
            }
        }

        if ($contractFollowUp->isDirty('reminder')) {
            $originalReminder = $contractFollowUp->getOriginal('reminder');
            $newReminder = $data['reminder'];
            // Perform actions based on the change
            Log::info("Reminder changed from $originalReminder to $newReminder");

            $lastSche = clone $contractFollowUp->last_schedule;

            // Calculate the lastTriggeredDate based on the reminder field
            $lastTriggeredDate = intval($data['reminder']) < 0 ? $lastSche->subDays(abs(intval($data['reminder']))) : $lastSche->addDays(abs(intval($data['reminder'])));
        }

        // Check if the request has a file called 'contract_document'
        if ($request->hasFile('contract_document')) {
            // Store the file in the 'public/contractFiles' directory
            log::info('file found');
            $filePath = $request->file('contract_document')->store('public/contractFiles');
            $data['contract_document'] = $filePath;
        } else {
            $data['contract_document'] = $contractFollowUp->contract_document;
        }

        log::info($data);

        $contractFollowUp->update([
            'client_id' => $data['client_id'],
            'contract_schedule' => $data['contract_schedule'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'last_schedule' => $lastSchedule ? $lastSchedule : $originalSchedule,
            'last_triggerd_date' => $lastTriggeredDate ? $lastTriggeredDate : $originalReminder,
            'reminder' => $data['reminder'],
            'message' => $data['message'],
            'amount' => $data['amount'],
            'contract_document' => $filePath ? $filePath : '',
            'user_id' => $data['user_id']
        ]);


        return new ContractFollowUpResource($contractFollowUp);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\ContractFollowUp  $contractFollowUp
     * @return \Illuminate\Http\Response
     */
    public function destroy(ContractFollowUp $contractFollowUp)
    {
        $contractFollowUp->delete();

        return response('', 204);
    }

    public function deleteByClient($id)
    {
        ContractFollowUp::where('id', $id)->delete();

        return response('', 204);
    }
}
