import { Box, Grid, TextField, Button, MenuItem } from '@mui/material';
import React, { useEffect } from 'react';

import { useState } from 'react';
import api from '../../../api/config/axiosConfig';

import { useStateContext } from '../../../context/ContextProvider';

function EmergencyContact({ id }) {
  const { errors, setErrors, setNotification, user } = useStateContext();
  const [isemp, setisEmp] = useState(false);
  const [formData, setFormData] = useState({
    emp_basic_id: ' ',
    name: '',
    relationship: '',
    contact: '',
    mobile: '',
    user_id: user.id,
  });

  useEffect(() => {
    setFormData({
      ...formData,
      user_id: user.id,
    });
  }, [user]);

  if (id) {
    useEffect(() => {
      api
        .get(`emp_basics/${id}?all=true`, {
          params: { all: 'fetch_emergency_contact' },
        })
        .then(({ data }) => {
          if (data.data.emp_emergency_contact) {
            setFormData(data.data.emp_emergency_contact);
            setisEmp(true);
          }
        });
    }, [id]);
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (isemp) {
      api
        .put(`emp_emergency_contact/${formData.id}`, formData)
        .then(() => {
          setNotification('updated successfully');
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
        });
    } else {
      const data = new FormData(e.currentTarget);

      const payload = {
        emp_basic_id: id,
        name: data.get('name'),
        relationship: data.get('relationship'),
        contact: data.get('contact'),
        mobile: data.get('mobile'),
        user_id: user.id,
      };
      
      api
        .post(`emp_emergency_contact`, payload)
        .then(() => {
          setNotification('created Successfully');
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
        });
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.name == ''}
            margin="normal"
            label="Full Name"
            name="name"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={formData.name}
            helperText={errors.name ? errors.name[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.relationship == ''}
            margin="normal"
            label="Relationship"
            name="relationship"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={formData.relationship}
            helperText={errors.relationship ? errors.relationship[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.contact == ''}
            margin="normal"
            label="Contact"
            name="contact"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={formData.contact}
            helperText={errors.contact ? errors.contact[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.mobile == ''}
            margin="normal"
            label="Mobile"
            name="mobile"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={formData.mobile}
            helperText={errors.mobile ? errors.mobile[0] : null}
          />
        </Grid>
      </Grid>
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
          {isemp ? <div>update</div> : <div>create</div>}
        </Button>
      </Box>
    </Box>
  );
}

export default EmergencyContact;
