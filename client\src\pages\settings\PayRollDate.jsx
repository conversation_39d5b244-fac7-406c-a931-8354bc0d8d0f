import { Box, Button, TextField, Typography, useTheme } from '@mui/material';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import api from '../../api/config/axiosConfig';
import CustomPopOver from '../../components/CustomPopOver';
import { useStateContext } from '../../context/ContextProvider';
import { tokens } from '../../utils/theme';
function PayRollDate({ isopen, anchorEl, handleClose }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const [formDataInit, setFormDataInit] = useState(false);

  const queryClient = useQueryClient();
  // const [formData, setFormData] = useState({
  //   start_date: new Date(),
  //   end_date: new Date(),
  // });

  const [formData, setFormData] = useState({
    start_date: '',
    end_date: '',
  });

  const { data: payroll, isLoading } = useQuery(
    ['payrolldate'],
    () =>
      api.get('payroll_date').then(({ data }) => {
        setFormData(data);
        console.log(data);
        return data;
      }),
    {
      staleTime: 600000,
      // refetchOnWindowFocus: false,
    }
  );

  const updatePayrolldate = useMutation(
    (data) => api.put(`payroll_date/${formData.id}`, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['payrolldate']);
        setNotification('payroll date updated successfully');
      },
    }
  );
  const createPayrollDate = useMutation(
    (data) => api.post('payroll_date', data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['payrolldate']);
        setNotification('payroll date created successfully');
      },
    }
  );
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (payroll.id) {
      updatePayrolldate.mutate(formData);
      handleClose();
      setFormDataInit(false);
    } else {
      createPayrollDate.mutate(formData);
      handleClose();
      setFormDataInit(false);
    }
  };

  // if (isopen && !formDataInit) {
  //   setFormData(payroll);
  //   setFormDataInit(true);
  // }

  useEffect(() => {
    if (isopen) {
      setFormData(payroll);
    }
  }, [isopen]);

  return (
    <CustomPopOver
      isopen={isopen}
      handleClose={handleClose}
      anchorEl={anchorEl}
    >
      {isLoading ? (
        <Typography>Loading...</Typography>
      ) : (
        <Box
          sx={{ background: colors.primary[400], color: colors.grey[100] }}
          component="form"
          onSubmit={handleSubmit}
        >
          <Box sx={{ p: 2 }} display="flex" justifyContent="space-between">
            <TextField
              label="start date"
              name="start_date"
              value={formData.start_date}
              onChange={handleChange}
              type="date"
            />
            <TextField
              label="end date"
              name="end_date"
              value={formData.end_date}
              onChange={handleChange}
              type="date"
              sx={{ ml: '8px' }}
            />
          </Box>
          <Box sx={{ py: 1, display: 'flex', justifyContent: 'space-between' }}>
            <Button
              onClick={handleClose}
              sx={{ ml: '5px', color: colors.grey[100] }}
            >
              cancel
            </Button>
            <Button type="submit" sx={{ mr: '5px', color: colors.grey[100] }}>
              submit
            </Button>
          </Box>
        </Box>
      )}
    </CustomPopOver>
  );
}

export default PayRollDate;
