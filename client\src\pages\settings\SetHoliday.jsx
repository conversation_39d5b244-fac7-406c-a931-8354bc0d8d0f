import React, { useState } from "react";
import CustomPopOver from "../../components/CustomPopOver";
import { Box, TextField, Button, useTheme } from "@mui/material";
import { tokens } from "../../utils/theme";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import api from "../../api/config/axiosConfig";
import { useStateContext } from "../../context/ContextProvider";
function SetHoliday({ isopen, anchorEl, handleClose }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [holiday, setHoliday] = useState("");
  const { setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const { data: holiydate } = useQuery(
    ["holiday"],
    async () =>
      await api.get("holiday").then(({ data }) => {
        setHoliday(data.holiy_day);
        return data;
      })
  );
  const createHoliday =useMutation((data)=>api.post('holiday',data),{
    onSuccess:()=>{
      setNotification("holiyday created successfully");
      queryClient.invalidateQueries(["holiyday"]);
    }
  })
  const handleSubmit = (e) => {
    e.preventDefault();
    const payload = {
      holiy_day: holiday,
    };
    if(holiydate.id){

      api.put(`holiday/${holiydate.id}`, payload).then(() => {
        setNotification("holiyday updated successfully");
        queryClient.invalidateQueries(["holiyday"]);
      });
      handleClose()
    }else{
      createHoliday.mutate(payload)
      handleClose()
    }
    // api.post("holiday", payload);
  };
  return (
    <CustomPopOver
      anchorEl={anchorEl}
      isopen={isopen}
      handleClose={handleClose}
    >
      <Box
        component="form"
        onSubmit={handleSubmit}
        sx={{
          background: colors.primary[400],
          color: colors.grey[100],
          padding: 2,
        }}
      >
        <TextField
          label="set holidays"
          name="holiy_day"
          InputLabelProps={{
            color: "success",
          }}
          value={holiday}
          onChange={(val) => setHoliday(val.target.value)}
          type="date"
        />
        <Box sx={{ pt: 1, display: "flex", justifyContent: "space-between" }}>
          <Button onClick={handleClose} sx={{ color: colors.grey[100] }}>
            cancel
          </Button>
          <Button type="submit" sx={{ color: colors.grey[100] }}>
            submit
          </Button>
        </Box>
      </Box>
    </CustomPopOver>
  );
}

export default SetHoliday;
