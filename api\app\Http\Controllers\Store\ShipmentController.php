<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Models\Shipment;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ShipmentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return Shipment::with('exportOrder')->latest()->paginate();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'export_order_id' => 'required|exists:export_orders,id',
            'shipment_number' => 'required|string|unique:shipments|max:255',
            'container_number' => 'nullable|string|max:255',
            'vessel_name' => 'nullable|string|max:255',
            'departure_port' => 'nullable|string|max:255',
            'arrival_port' => 'nullable|string|max:255',
            'departure_date' => 'nullable|date',
            'arrival_date' => 'nullable|date',
            'tracking_number' => 'nullable|string|max:255',
            'status' => ['required', Rule::in(['pending', 'in_transit', 'arrived', 'cancelled'])],
        ]);

        return Shipment::create($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Shipment  $shipment
     * @return \Illuminate\Http\Response
     */
    public function show(Shipment $shipment)
    {
        return $shipment->load('exportOrder');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Shipment  $shipment
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Shipment $shipment)
    {
        $data = $request->validate([
            'export_order_id' => 'sometimes|required|exists:export_orders,id',
            'shipment_number' => 'sometimes|required|string|unique:shipments,shipment_number,' . $shipment->id . '|max:255',
            'container_number' => 'nullable|string|max:255',
            'vessel_name' => 'nullable|string|max:255',
            'departure_port' => 'nullable|string|max:255',
            'arrival_port' => 'nullable|string|max:255',
            'departure_date' => 'nullable|date',
            'arrival_date' => 'nullable|date',
            'tracking_number' => 'nullable|string|max:255',
            'status' => ['sometimes', 'required', Rule::in(['pending', 'in_transit', 'arrived', 'cancelled'])],
        ]);

        $shipment->update($data);

        return $shipment;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Shipment  $shipment
     * @return \Illuminate\Http\Response
     */
    public function destroy(Shipment $shipment)
    {
        $shipment->delete();

        return response()->noContent();
    }
}
