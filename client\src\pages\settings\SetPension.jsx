import { <PERSON>, TextField, useTheme, Button } from "@mui/material";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import React, { useState } from "react";
import api from "../../api/config/axiosConfig";
import CustomPopOver from "../../components/CustomPopOver";
import { tokens } from "../../utils/theme";
import { useStateContext } from "../../context/ContextProvider";

function SetPension({ isopen, anchorEl, handleClose }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const [pension, setPension] = useState({
    emp_pension: "1",
    comp_pension: "2",
  });
  const { data: pen } = useQuery(["pension"], () =>
    api.get("pension").then(({ data }) => {
      setPension(data);
      return data;
    })
  );

  const createPension = useMutation((data) => api.post("pension", data), {
    onSuccess: () => {
      queryClient.invalidateQueries(["pension"]),
        setNotification("Set pension created successfully");
    },
  });
  const handleChange = (e) => {
    setPension({
      ...pension,
      [e.target.name]: e.target.value,
    });
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (pen.id) {
      api.put(`pension/${pen.id}`, pension).then(() => {
        queryClient.invalidateQueries(["pension"]),
          setNotification("Set pension Updated successfully");
        handleClose();
      });
    } else {
      createPension.mutate(pension);
      handleClose();
    }
  };
  return (
    <CustomPopOver
      isopen={isopen}
      anchorEl={anchorEl}
      handleClose={handleClose}
    >
      <Box
        component="form"
        onSubmit={handleSubmit}
        sx={{
          background: colors.primary[400],
          color: colors.grey[100],
          padding: 2,
        }}
      >
        <TextField
          label="Set Emp Pension"
          name="emp_pension"
          onChange={handleChange}
          value={pension.emp_pension}
          type="number"
          inputProps={{
            color: "success",
          }}
        />
        <TextField
          label="Set Company Pension "
          name="comp_pension"
          onChange={handleChange}
          value={pension.comp_pension}
          type="number"
          inputProps={{
            color: "success",
          }}
          sx={{ ml: "8px" }}
        />
        <Box sx={{ pt: 1, display: "flex", justifyContent: "space-between" }}>
          <Button onClick={handleClose} sx={{ color: colors.grey[100] }}>
            cancel
          </Button>
          <Button type="submit" sx={{ color: colors.grey[100] }}>
            submit
          </Button>
        </Box>
      </Box>
    </CustomPopOver>
  );
}

export default SetPension;
