import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  TextField,
  Typography,
  useTheme,
} from '@mui/material';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import api from '../../../api/config/axiosConfig';
import _CircularProgress from '../../../components/_CircularProgress';
import { useStateContext } from '../../../context/ContextProvider';
import { tokens } from '../../../utils/theme';

const cardStyle = {
  padding: '10px',
  margin: '5px',
  // boxShadow: "0px 4px 6px rgba(0, 0, 0, 0.1)",
  borderRadius: '8px',
  display: 'flex',
  justifyContent: 'space-between',
};
const NotificationDetailPayment = () => {
  
  const queryClient = useQueryClient();
  const { id } = useParams();
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const currentMonth = new Date().getMonth() + 1;
  const [openForm, setOpenForm] = useState(false);
  const { setNotification, user } = useStateContext();

  const {
    data: payment,
    isLoading,
    isError,
  } = useQuery(
    [`notification_${id}`, id],
    () => api.get(`/markAsRead/${id}`).then(({ data }) => data),
    {
      onSuccess: ({ data }) => {
        console.log(data);
        queryClient.invalidateQueries({
          queryKey: ['notifications_unread', 'notify'],
        });

        // api
        //   .get(`payment-follow-up/${data.message.contract_id}`)
        //   .then(({ data }) => {
        //     const last = new Date(data.data.last_triggerd_date).getMonth() + 1;
        //     if (last === currentMonth) {
        //       api.get(`followup/due/${data.data.id}`);
        //     }
        //   });
      },
      enabled: !!id,
    }
  );

  const createPayment = useMutation(
    (data) => api.post('collected_payments', data),
    {
      onSuccess: () => {
        setNotification('payment record created successfully.');
      },
      onError: () => {
        setNotification('error creating payment record.');
      },
    }
  );

  if (isLoading) {
    return <_CircularProgress />;
  }

  const handleClickOpen = () => {
    setOpenForm(true);
  };

  const handleClose = () => {
    setOpenForm(false);
  };

  const handleSubmit = (e) => {
    event.preventDefault();
    const data = new FormData(e.currentTarget);
    // Handle form submission logic here
    const payload = {
      contract_id: payment.data.message.contract_id,
      client_id: payment.data.message.client_id,
      collector_name: data.get('name'),
      amount: data.get('amount'),
      collection_date: data.get('date'),
      collection_type: data.get('type'),
      comment: data.get('comment'),
      user_id: user.id,
    };

    //console.log(payload);
    createPayment.mutate(payload);

    handleClose();
  };

  return (
    <Container maxWidth="md">
      <Card
        variant="elevation"
        style={{
          color: colors.grey,
          background:
            theme.palette.mode == 'dark' ? colors.primary[400] : undefined,
        }}
      >
        <CardContent>
          {payment.data.message.overdue && (
            <Alert severity="error" variant="filled">
              <Typography variant="h4">Payment FollowUp Overdue</Typography>
            </Alert>
          )}
          <div className="info-container">
            <div style={{ marginBottom: '10px', textAlign: 'center' }}>
              <Typography variant="h2" color={colors.grey[100]} align="center">
                Payment Followup Reminder Notification Detail
              </Typography>
            </div>
            <div style={{}}>
              <Typography variant="h3" color={colors.grey[100]} align="center">
                {payment.data.message.client_id}
              </Typography>
              <div style={{ maxWidth: '80%', margin: 'auto' }}>
                <div style={cardStyle}>
                  <Typography variant="button">Payment Start Date :</Typography>
                  <Typography marginLeft={1} variant="body1">
                    <u>{payment.data.message.start_date}</u>
                  </Typography>
                </div>
                <div style={cardStyle}>
                  <Typography variant="button">
                    Payment Collection Date :
                  </Typography>
                  <Typography marginLeft={1} variant="body1">
                    <u>{payment.data.message.end_date}</u>
                  </Typography>
                </div>
                <div style={cardStyle}>
                  <Typography variant="button">message</Typography>
                  <Typography marginLeft={1} variant="body1">
                    <u>{payment.data.message.message}</u>
                  </Typography>
                </div>
                <div style={cardStyle}>
                  <Typography variant="button">reminder set</Typography>
                  <Typography marginLeft={1} variant="body1">
                    <u>{payment.data.message.collection_schedule}</u>
                  </Typography>
                </div>
                <div style={cardStyle}>
                  <Typography variant="button">
                    {payment.data.message.overdue
                      ? 'Overdue Days'
                      : 'days Left Until Due '}
                  </Typography>
                  <Typography marginLeft={1} variant="body1">
                    <u>{payment.data.message.daysLeftUntilDue}</u>
                  </Typography>
                </div>

                <div style={cardStyle}>
                  <Button
                    variant="contained"
                    color="success"
                    onClick={handleClickOpen}
                  >
                    Update
                  </Button>

                  <Dialog open={openForm} onClose={handleClose}>
                    <DialogTitle variant="h4">
                      Update Payment Details For{' '}
                      {payment.data.message.company_name}
                    </DialogTitle>
                    <DialogContent>
                      <form id="update-form" onSubmit={handleSubmit}>
                        <TextField
                          autoFocus
                          margin="dense"
                          id="name"
                          name="name"
                          label="Who Collected the Payment"
                          type="text"
                          fullWidth
                          variant="standard"
                        />

                        <TextField
                          autoFocus
                          margin="dense"
                          id="amount"
                          name="amount"
                          label="Enter Collected Amount"
                          type="number"
                          fullWidth
                          variant="standard"
                        />

                        <TextField
                          autoFocus
                          margin="dense"
                          id="date"
                          name="date"
                          label="Date"
                          type="date"
                          fullWidth
                          variant="standard"
                        />

                        <TextField
                          autoFocus
                          margin="dense"
                          id="type"
                          name="type"
                          label="Collection Type"
                          placeholder="Bank, Check, Other"
                          type="text"
                          fullWidth
                          variant="standard"
                        />

                        <TextField
                          autoFocus
                          margin="dense"
                          id="comment"
                          name="comment"
                          label="Comment"
                          type="text"
                          fullWidth
                          variant="standard"
                        />

                        {/* Add more form fields as needed */}
                      </form>
                    </DialogContent>
                    <DialogActions>
                      <Button
                        onClick={handleClose}
                        color="error"
                        variant="contained"
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        form="update-form"
                        variant="contained"
                        color="success"
                      >
                        Submit
                      </Button>
                    </DialogActions>
                  </Dialog>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Container>
  );
};

export default NotificationDetailPayment;
