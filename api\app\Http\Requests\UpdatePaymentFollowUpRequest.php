<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePaymentFollowUpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'client_id'=>'required',
            'collection_schedule'=>'required',
            'reminder'=>'required|integer',
            'start_date'=>'required|date',
            'end_date'=>'required|date',
            'amount'=>'required|integer',
            'message'=>'required|string',
            'user_id'=>'required|integer',
        ];
    }
}
