<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('emp_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('emp_basic_id');
            $table->foreign('emp_basic_id')->references('id')->on('emp_basics')->onDelete('cascade');
            $table->integer('height')->nullable();
            $table->integer('weight')->nullable();
            $table->string('address')->nullable();
            $table->string('city')->nullable();
            $table->string('sub_city')->nullable();
            $table->string('kebele')->nullable();
            $table->string('house_no')->nullable();
            $table->string('country')->nullable();
            $table->string('phone')->nullable();
            $table->string('photo_url')->nullable();
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
    }

    // {
    //     Schema::create('emp_detail', function (Blueprint $table) {
    //         $table->integer('Emp_detail_ID')->primary()->unique();
    //         $table->integer('Eid')->index()->unique();
    //         $table->integer('Height')->nullable();
    //         $table->integer('Weight')->nullable();
    //         $table->text('Address1')->nullable();
    //         $table->text('Hobbies')->nullable();
    //         $table->text('City')->nullable();
    //         $table->text('Sub_City')->nullable();
    //         $table->text('Kebele')->nullable();
    //         $table->text('House_No')->nullable();
    //         $table->text('Country')->nullable();
    //         $table->text('Tel')->nullable();
    //         $table->text('Photo_Url')->nullable();
    //         $table->integer('Uid');
    //     });
    // }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('emp_details');
    }
};
