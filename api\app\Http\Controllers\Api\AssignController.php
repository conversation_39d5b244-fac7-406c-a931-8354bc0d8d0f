<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\AssignResource;
use App\Models\Assign;
use App\Models\EmpBasic;
use App\Models\Client;
use App\Models\settings\PayrollDate;
use Illuminate\Support\Facades\Log;

use Illuminate\Http\Request;


class AssignController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * 
     */
    public function index()
    {
        if (request()->input('position')) {
            $empBasics = EmpBasic::leftJoin('assigns', 'emp_basics.id', '=', 'assigns.emp_basic_id')
                ->where('emp_basics.position', '=', request()->input('position'))
                ->where('termination_status', 0)
                ->where('insurance', 1)
                ->whereNull('assigns.id')
                ->select('emp_basics.id', 'emp_basics.first_name', 'emp_basics.middle_name', 'emp_basics.last_name')
                ->get();
            return AssignResource::collection($empBasics);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \App\Http\Resources\AssignResource
     */
    public function store(Request $request)
    {
        $data = Assign::create([
            'client_id' => $request->input('client_id'),
            'emp_basic_id' => $request->input('emp_basic_id'),
            'position' => $request->input('position'),
            'user_id' => $request->input('user_id'),
            'assign_date' => $request->input('assign_date'),
        ]);

        return new AssignResource($data);
    }


    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Assign  $assign
     * @return \App\Http\Resources\AssignResource
     */
    public function show(Assign $assign)
    {
        return new AssignResource($assign);
    }


    //employees assigned in a clinet
    public function clientAssigned($client_id)
    {
        $assigns = Assign::select('emp_basic_id', 'assign_date')->Where('client_id', $client_id)->get();
        $empBasics = collect([]);

        if ($assigns->count() > 0) {

            foreach ($assigns as $assign) {

                $empBasic = EmpBasic::select('id', 'first_name', 'middle_name', 'last_name', 'position')
                    ->where('termination_status', 0)
                    ->find($assign->emp_basic_id);

                if ($empBasic) {
                    $empBasic->assign_date = $assign->assign_date;
                    $empBasics->push($empBasic);
                }
            }
            $empBasics = $empBasics->sortBy('first_name');
            return AssignResource::collection($empBasics);
        }
        if ($assigns->isEmpty()) {

            return response('', 204);
        }
    }

    public function clientAssignedId($client_id)
    {
        $assigns = Assign::select('emp_basic_id')->Where('client_id', $client_id)->get();
        $empBasics = [];
        if ($assigns->count() > 0) {
            foreach ($assigns as $assign) {
                Log::info($assign->emp_basic_id);
                $empBasic = EmpBasic::where('termination_status', 0)->find($assign->emp_basic_id);
                if ($empBasic) {
                    $empBasics[] = $empBasic->id;
                }
            }
            return $empBasics;
        }

        if ($assigns->isEmpty()) {

            return response('', 204);
        }
    }

    public function employeeInPayRollList($client_id)
    {
        $pay_date = PayrollDate::first();
        $start_date = $pay_date->start_date;
        $end_date = $pay_date->end_date;

        $assignedEmpIds = Assign::where('client_id', $client_id)
            ->pluck('emp_basic_id');

        if ($assignedEmpIds->isEmpty()) return response()->json([], 200); // Return empty if no assignments found.

        // Fetch all eligible employees in one go, ensuring they haven't been paid in the period.
        $eligibleEmployees = EmpBasic::select('id', 'first_name', 'middle_name', 'last_name', 'position', 'start_date')
            ->whereIn('id', $assignedEmpIds)
            ->where('termination_status', '0')
            ->whereDoesntHave('payroll', function ($query) use ($start_date, $end_date) {
                $query->whereBetween('pay_date', [$start_date, $end_date]);
            })
            ->orderBy('first_name')
            ->get();

        // Return the collection through a resource.
        return AssignResource::collection($eligibleEmployees);
    }

    public function getAssign($emp_basic_id)
    {
        $assigns = Assign::where('emp_basic_id', $emp_basic_id)->first();

        if ($assigns) {
            $client = Client::where('id', $assigns->client_id)->first();
            $assigns->company_name = $client->company_name;
            $assigns->company_code = $client->company_code;
        } else {
            $assigns = (object) ['message' => 'employee not assigned', 'id' => '1'];
        }

        return $assigns;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Assign  $assign
     * @return \App\Http\Resources\AssignResource
     */

    public function update(Request $request, Assign $assign)
    {
        $assign->update([
            'client_id' => $request->input('client_id'),
            'emp_basic_id' => $request->input('emp_basic_id'),
            'position' => $request->input('position'),
            'user_id' => $request->input('user_id'),
        ]);

        return new AssignResource($assign);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Assign  $assign
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {

        Assign::where('emp_basic_id', $id)->delete();

        return  response('', 204);
    }
}
