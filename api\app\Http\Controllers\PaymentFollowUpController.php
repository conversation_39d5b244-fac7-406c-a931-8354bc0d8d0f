<?php

namespace App\Http\Controllers;

use App\Models\PaymentFollowUp;
use App\Http\Requests\StorePaymentFollowUpRequest;
use App\Http\Requests\UpdatePaymentFollowUpRequest;
use App\Http\Resources\PaymentFollowUpResource;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;

class PaymentFollowUpController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index()
    {
        return PaymentFollowUpResource::collection(PaymentFollowUp::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StorePaymentFollowUpRequest  $request
     * @return \App\Http\Resources\PaymentFollowUpResource
     */
    public function store(StorePaymentFollowUpRequest $request)
    {
        $data = $request->validated();
        $startDate = Carbon::parse($data['start_date']);
        $endDate = Carbon::parse($data['end_date']);
        $interval = 0;

        // Determine the interval based on the collection schedule
        switch ($data['collection_schedule']) {
            case 'monthly':
                $interval = 1;
                break;
            case 'quarterly':
                $interval = 3;
                break;
            case 'semester':
                $interval = 6;
                break;
            case 'yearly':
                $interval = 12;
                break;
            default:
                return response()->json(['error' => 'Invalid collection schedule'], 400);
        }

        // Create multiple PaymentFollowUp records based on contract_length
        for ($i = 0; $i < $data['contract_length']; $i++) {
            $lastSchedule = $endDate->copy()->addMonths($interval * $i);

            $reminderDays = intval($data['reminder']);

            $lastTriggeredDate = $reminderDays < 0
                ? $lastSchedule->copy()->subDays(abs($reminderDays))
                : $lastSchedule->copy()->addDays($reminderDays);

            PaymentFollowUp::create([
                'client_id' => $data['client_id'],
                'collection_schedule' => $data['collection_schedule'],
                'start_date' => $data['start_date'],
                'end_date' => $endDate->copy()->addMonths($interval * $i),
                'last_schedule' => $lastSchedule,
                'last_triggerd_date' => $lastTriggeredDate,
                'reminder' => $data['reminder'],
                'message' => $data['message'],
                'amount' => $data['amount'],
                'contract_length' => $data['contract_length'],
                'user_id' => $data['user_id'],
            ]);
        }

        return response()->json(['message' => 'Payment follow-ups created successfully'], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\PaymentFollowUp  $paymentFollowUp
     * @return \App\Http\Resources\PaymentFollowUpResource
     */
    public function show(PaymentFollowUp $paymentFollowUp)
    {
        return new PaymentFollowUpResource($paymentFollowUp);
    }

    public function showByClient($client_id)
    {
        $companyFollowUps = PaymentFollowUp::where('client_id', '=', $client_id)->get();
        return PaymentFollowUpResource::collection($companyFollowUps);
        //return $companyFollowUps;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdatePaymentFollowUpRequest  $request
     * @param  \App\Models\PaymentFollowUp  $paymentFollowUp
     * @return \App\Http\Resources\PaymentFollowUpResource
     */
    public function update(UpdatePaymentFollowUpRequest $request, PaymentFollowUp $paymentFollowUp)
    {
        //Log::info('Update request received for PaymentFollowUp', ['payment_follow_up_id' => $paymentFollowUp->id]);

        $data = $request->validated();
        //Log::debug('Validated data for update', $data);
        $lastSchedule = '';
        $lastTriggeredDate = '';
        $originalSchedule = $paymentFollowUp->getOriginal('last_schedule');
        $originalReminder = $paymentFollowUp->getOriginal('last_triggerd_date');

        if ($paymentFollowUp->isDirty('collection_schedule')) {
            $originalSchedule = $paymentFollowUp->getOriginal('last_schedule');
            $newSchedule = $data['collection_schedule'];

            $lastSchedule = now();
            if ($data['collection_schedule']) {
                $startDate = Carbon::parse($data['start_date']);

                switch ($data['collection_schedule']) {
                    case 'monthly':
                        $lastSchedule = $startDate->addMonth();
                        break;
                    case 'quarterly':
                        $lastSchedule = $startDate->addMonths(3);
                        break;
                    case 'yearly':
                        $lastSchedule->addYear();
                        break;
                    case 'semister':
                        $lastSchedule->addMonths(6);
                        break;
                    default:
                        $lastSchedule;
                }

                $lastSche = clone $lastSchedule;
                $lastTriggeredDate = intval($data['reminder']) < 0 ? $lastSche->subDays(abs(intval($data['reminder']))) : $lastSche->addDays(abs(intval($data['reminder'])));
            }
        }

        if ($paymentFollowUp->isDirty('reminder')) {
            $originalReminder = $paymentFollowUp->getOriginal('last_triggerd_date');
            $newReminder = $data['reminder'];
            // Perform actions based on the change
            Log::info("Reminder changed from $originalReminder to $newReminder");

            $lastSche = clone $paymentFollowUp->last_schedule;

            // Calculate the lastTriggeredDate based on the reminder field
            $lastTriggeredDate = intval($data['reminder']) < 0 ? $lastSche->subDays(abs(intval($data['reminder']))) : $lastSche->addDays(abs(intval($data['reminder'])));
        }

        $paymentFollowUp->update([
            'client_id' => $data['client_id'],
            'collection_schedule' => $data['collection_schedule'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'last_schedule' => $lastSchedule ? $lastSchedule : $originalSchedule,
            'last_triggerd_date' => $lastTriggeredDate ? $lastTriggeredDate : $originalReminder,
            'reminder' => $data['reminder'],
            'message' => $data['message'],
            'amount' => $data['amount'],
            'user_id' => $data['user_id'],
        ]);
        //Log::info('PaymentFollowUp updated successfully', ['payment_follow_up_id' => $paymentFollowUp->id]);

        return new PaymentFollowUpResource($paymentFollowUp);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\PaymentFollowUp  $paymentFollowUp
     * @return \Illuminate\Http\Response
     */
    public function destroy(PaymentFollowUp $paymentFollowUp)
    {
        $paymentFollowUp->delete();

        return response('', 204);
    }

    public function deleteByClient($id)
    {
        PaymentFollowUp::where('id', $id)->delete();
        return response('', 204);
    }

    public function paymentsOfThisMonth(){
        $now = now();
        $paymentFollowUps = PaymentFollowUp::where('status', NULL)
            ->whereMonth('last_triggerd_date', '=', date('m', strtotime(now())))
            ->whereYear('last_triggerd_date', '=', date('Y', strtotime(now())))
            ->get();

        $overdue = '';
        foreach ($paymentFollowUps as $result) {
            $dueDateLefts = Carbon::parse($result['end_date'])->diffInDays(now());
            // Check if the payment follow up is overdue
            if ($result['last_schedule'] < now() ) {
                // Check if the payment follow up is overdue
                $overdue = 'overdue';
                $result['overdue'] = $overdue;
                $result['daysLeftUntilDue'] = $dueDateLefts;
            }
            // Check if the payment follow up is not overdue
            else {
                $result['overdue'] = 'pending';
                $result['daysLeftUntilDue'] = $dueDateLefts;
            }
        }

        return PaymentFollowUpResource::collection($paymentFollowUps);
    }
}
