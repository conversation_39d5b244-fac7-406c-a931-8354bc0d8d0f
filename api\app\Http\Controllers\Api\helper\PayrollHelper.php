<?php

namespace App\Http\Controllers\Api\helper;

use App\Models\AnnualLeave;
use App\Models\clientDeduct;
use App\Models\EmpAllowance;
use App\Models\EmpBank;
use App\Models\EmpStatus;
use App\Models\finance\AbsenceList;
use App\Models\finance\Additional;
use App\Models\finance\AdditionalDeductable;
use App\Models\finance\AddPcrate;
use App\Models\finance\DailyRate;
use App\Models\finance\Dsa;
use App\Models\finance\EmployeeDeductable;
use App\Models\finance\NonTaxableAllowance;
use App\Models\finance\OverTime;
use App\Models\finance\PcRate;
use App\Models\settings\Deduct;
use App\Models\settings\ExchangeRate;
use App\Models\settings\Holiday;
use App\Models\settings\MedicalShare;
use App\Models\settings\PayrollDate;
use App\Models\settings\Pension;
use App\Models\settings\Provident;
use App\Models\settings\Termination;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PayrollHelper
{


    function provident()
    {
        $pro = Provident::first();
        $providentEdomias = $pro->provident_sns;
        $providentEmp = $pro->provident_emp;
        return [
            'providentEdomias' => $providentEdomias,
            'providentEmp' => $providentEmp
        ];
    }

    function pension()
    {
        $pen = Pension::first();
        $pensionEdomias = $pen->comp_pension;
        $pensionEmp = $pen->emp_pension;

        return [
            'pensionEdomias' => $pensionEdomias,
            'pensionEmp' => $pensionEmp
        ];
    }


    function exchangeRate()
    {
        $exchange = ExchangeRate::first();
        if ($exchange != null) {

            return [
                'exchange' => $exchange->exchange_rate
            ];
        } else {
            return [
                'exchange' => 0
            ];
        }
    }

    function setPayrollDate()
    {
        $date = PayrollDate::first();
        $payrollStartDate = $date->start_date;
        $payrollEndDate = $date->end_date;

        return [
            'startDate' => $payrollStartDate,
            'endDate' => $payrollEndDate,
        ];
    }

    public function deduct($id)
    {

        $deduct = Deduct::first();
        $status = EmpStatus::where('emp_basic_id', $id)->first();
        if (!$status || !$status->social_support || $deduct == null) {
            return [
                'socialFund' => 0
            ];
        }
        return [
            'socialFund' => $deduct->social_fund
        ];
    }
    public function bankInformation($id)
    {
        $bank = EmpBank::where('emp_basic_id', $id)->first();

        if (!$bank) {
            return [
                'account_number' => '0',
                'branch' => '0',
                'bank_code' => '0',
            ];
        }

        return [
            'account_number' => $bank->bank_account,
            'branch' => $bank->bank_branch,
            'bank_code' => $bank->bank_code,
        ];
    }
    public function medicalShare()
    {
        $medicalshare = MedicalShare::first();
        if ($medicalshare != null) {


            return [

                'medicalshare' => $medicalshare->medical_share
            ];
        } else {
            return [

                'medicalshare' => 0
            ];
        }
    }

    public function clientDeduct($id, $clientId)
    {
        $clientDeduct = clientDeduct::where('client_id', '=', $clientId)->first();
        $status = EmpStatus::where('emp_basic_id', $id)->first();
        $laborUnionType = '';
        $laborUnion = 0;
        $creaditAssociation = 0;
        $sportAssociation = 0;
        if ($clientDeduct != null) {


            // foreach ($clientDeduct as $deduct) {
            $laborUnionType = $clientDeduct->type;
            $laborUnion = $clientDeduct->labor_union;
            $creaditAssociation = $clientDeduct->creadit_association;
            $sportAssociation = $clientDeduct->sport_association;
            // }

            if (!$status->labor_union) {
                return [
                    'laborUnionType' => $laborUnionType,
                    'laborUnion' => 0,
                    'sportAssociation' => $sportAssociation,
                    'creaditAssociation' => $creaditAssociation
                ];
            }
            if (!$status->credit_association) {
                return [
                    'laborUnionType' => $laborUnionType,
                    'laborUnion' => $laborUnion,
                    'sportAssociation' => $sportAssociation,
                    'creaditAssociation' => 0
                ];
            }
            if (!$status->sport_association) {
                return [
                    'laborUnionType' => $laborUnionType,
                    'laborUnion' => $laborUnion,
                    'sportAssociation' => 0,
                    'creaditAssociation' => $creaditAssociation
                ];
            }
            return [
                'laborUnionType' => $laborUnionType,
                'laborUnion' => $laborUnion,
                'sportAssociation' => $sportAssociation,
                'creaditAssociation' => $creaditAssociation
            ];
        } else {
            return [
                'laborUnionType' => 0,
                'laborUnion' => 0,
                'sportAssociation' => 0,
                'creaditAssociation' => 0
            ];
        }
    }

    public function actualDateLength()
    {
        $payrolldate = $this->setPayrollDate();

        return Carbon::parse($payrolldate['startDate'])->daysInMonth;
    }

    public function getDayDiff($fromDate, $toDate)
    {
        $startDate = Carbon::parse($fromDate);
        $endDate = Carbon::parse($toDate);
        $days = $startDate->diffInDays($endDate);

        return $days;
    }

    public function nodays($fromDate, $toDate)
    {
        $startDate = Carbon::parse($fromDate);
        $endDate = Carbon::parse($toDate);

        $daysDifference = $startDate->diffInDays($endDate);

        if ($startDate->year === $endDate->year && $startDate->month === $endDate->month) {
            // Dates are in the same year and month
            return $daysDifference;
        } else {
            // Dates are not in the same year and month
            return 0;
        }
    }

    public function overTime($id, $fromDate, $todate)
    {
        $overtimes = OverTime::where('emp_basic_id', $id)->where(
            'paid',
            0
        )->whereBetween('start_date', [$fromDate, $todate])->get();
        $totalOvertimeHours = 0;
        if ($overtimes) {
            foreach ($overtimes as $overtime) {
                if ($overtime->day_type == 'Mid Night') {
                    $totalOvertimeHours += $overtime->hours * 1.75 + ($overtime->minutes / 60) * 1.75;
                } else if ($overtime->day_type == 'Weekend') {
                    $totalOvertimeHours += $overtime->hours * 2 + ($overtime->minutes / 60) * 2;
                } else if ($overtime->day_type == 'Holiday') {
                    $totalOvertimeHours += $overtime->hours * 2.5 + ($overtime->minutes / 60) * 2.5;
                } else if ($overtime->day_type == 'Normal Day') {
                    $totalOvertimeHours += $overtime->hours * 1.5 + ($overtime->minutes / 60) * 1.5;
                } else {
                    $totalOvertimeHours += $overtime->hours * ($overtime->minutes / 60);
                }
            }
            return [
                'totalOvertimeHours' => $totalOvertimeHours
            ];
        } else {
            return [
                'totalOvertimeHours' => 0
            ];
        }
    }

    public function pcrate($id, $fromDate, $endDate)
    {
        $pcrates = PcRate::where('emp_basic_id', $id)->where('paid', 0)->whereBetween('from_date', [$fromDate, $endDate])->get();

        $totalNopc = 0;
        $pcrateType = '';
        $pcrate = 0;
        $totalpcrateSalary = 0;
        foreach ($pcrates as $pcrate) {
            $totalpcrateSalary = $totalpcrateSalary + $pcrate->total;
            $totalNopc = $totalNopc + $pcrate->no_pc;
            $pcrateType = $pcrate->pc_type;
            $pcrate = $pcrate->salary;
        }
        return [
            'totalNoPc' => $totalNopc,
            'pcrateType' => $pcrateType,
            'pcrate' => $pcrate,
            'totalpcrateSalary' => $totalpcrateSalary
        ];
    }

    public function dsa($id, $fromDate, $toDate)
    {
        $dsas = Dsa::where('emp_basic_id', $id)->where('paid', 0)->whereBetween('from_date', [$fromDate, $toDate])->get();
        $totaltaxableDsaSalary = 0;
        $totalNontaxableDsaSalary = 0;
        foreach ($dsas as $dsa) {
            if ($dsa->taxable == 1) {
                $totaltaxableDsaSalary = $totaltaxableDsaSalary + $dsa->total;
            } else {
                $totalNontaxableDsaSalary = $totalNontaxableDsaSalary + $dsa->total;
            }
        }
        return [
            'totaltaxableDsaSalary' => $totaltaxableDsaSalary,
            'totalNontaxableDsaSalary' => $totalNontaxableDsaSalary
        ];
    }

    public function dateLength($startDate)
    {
        $date = Carbon::createFromFormat('Y-m-d', $startDate);
        $day = $date->day;

        if ($this->actualDateLength() == $day) {
            return 1;
        }
        return $this->actualDateLength() - $day;
    }

    public function nonTaxable($id, $startDate)
    {
        // Fetch the first EmpAllowance record for the given emp_basic_id
        $nontax = EmpAllowance::where('emp_basic_id', $id)->first();

        // Create a Carbon instance from the start date
        $date = Carbon::createFromFormat('Y-m-d', $startDate);
        $day = $date->day;

        // Calculate the date length
        $dateLength = $this->actualDateLength() - $day;

        // Initialize allowance variables to 0
        $nonTaxTransportAllowance = 0;
        $nonTaxDesertAllowance = 0;
        $nonTaxMobileAllowance = 0;
        $nonTaxCleaningAllowance = 0;
        $nonTaxOtherAllowance = 0;
        $nonTaxPositionAllowance = 0;

        // Check if $nontax is not null
        if ($nontax) {
            if ($dateLength != 0) {
                $nonTaxTransportAllowance = ($nontax->non_tax_ta / $this->actualDateLength()) * $dateLength;
                $nonTaxDesertAllowance = ($nontax->non_tax_deseret / $this->actualDateLength()) * $dateLength;
                $nonTaxMobileAllowance = ($nontax->non_tax_mobile / $this->actualDateLength()) * $dateLength;
                $nonTaxCleaningAllowance = ($nontax->non_tax_cleaning / $this->actualDateLength()) * $dateLength;
                $nonTaxOtherAllowance = ($nontax->non_tax_other / $this->actualDateLength()) * $dateLength;
                $nonTaxPositionAllowance = ($nontax->non_tax_position / $this->actualDateLength()) * $dateLength;
            } else {
                $nonTaxTransportAllowance = $nontax->non_tax_ta;
                $nonTaxDesertAllowance = $nontax->non_tax_deseret;
                $nonTaxMobileAllowance = $nontax->non_tax_mobile;
                $nonTaxCleaningAllowance = $nontax->non_tax_cleaning;
                $nonTaxOtherAllowance = $nontax->non_tax_other;
                $nonTaxPositionAllowance = $nontax->non_tax_position;
            }
        }

        // Return the calculated allowances as an array
        return [
            'nonTaxTransportAllowance' => $nonTaxTransportAllowance,
            'nonTaxDesertAllowance' => $nonTaxDesertAllowance,
            'nonTaxMobileAllowance' => $nonTaxMobileAllowance,
            'nonTaxCleaningAllowance' => $nonTaxCleaningAllowance,
            'nonTaxOtherAllowance' => $nonTaxOtherAllowance,
            'nonTaxPositionAllowance' => $nonTaxPositionAllowance
        ];
    }


    // public function nonTaxable($id, $startDate)
    // {

    //     $nontax = EmpAllowance::where('emp_basic_id', $id)->first();
    //     $date = Carbon::createFromFormat('Y-m-d', $startDate);
    //     $day = $date->day;
    //     //    $dateLength=$this->actualDateLength()-$day+1;
    //     $dateLength = $this->actualDateLength() - $day;
    //     $nonTaxTransportAllowance = 0;
    //     $nonTaxDesertAllowance = 0;
    //     $nonTaxMobileAllowance = 0;
    //     $nonTaxCleaningAllowance = 0;
    //     $nonTaxOtherAllowance = 0;
    //     $nonTaxPositionAllowance = 0;
    //     if($nontax){
    //     foreach ($nontax as $nontax) {
    //      if ($dateLength != 0) {
    //            $nonTaxTransportAllowance = ($nontax->non_tax_ta /  $this->actualDateLength()) * $dateLength;
    //            $nonTaxDesertAllowance = ($nontax->non_tax_deseret /  $this->actualDateLength()) * $dateLength;
    //            $nonTaxMobileAllowance = ($nontax->non_tax_mobile /  $this->actualDateLength()) * $dateLength;
    //            $nonTaxCleaningAllowance = ($nontax->non_tax_cleaning /  $this->actualDateLength()) * $dateLength;
    //            $nonTaxOtherAllowance = ($nontax->non_tax_other /  $this->actualDateLength()) * $dateLength;
    //            $nonTaxPositionAllowance = ($nontax->non_tax_position /  $this->actualDateLength()) * $dateLength;
    //         } else {
    //            $nonTaxTransportAllowance = $nontax->non_tax_ta;
    //            $nonTaxDesertAllowance = $nontax->non_tax_deseret;
    //            $nonTaxMobileAllowance = $nontax->non_tax_mobile;
    //            $nonTaxCleaningAllowance = $nontax->non_tax_cleaning;
    //            $nonTaxOtherAllowance = $nontax->non_tax_other;
    //            $nonTaxPositionAllowance = $nontax->non_tax_position;
    //         }
    //      }
    //     }
    //     return [
    //         'nonTaxTransportAllowance' => $nonTaxTransportAllowance,
    //         'nonTaxDesertAllowance' => $nonTaxDesertAllowance,
    //         'nonTaxMobileAllowance' => $nonTaxMobileAllowance,
    //         'nonTaxCleaningAllowance' => $nonTaxCleaningAllowance,
    //         'nonTaxOtherAllowance' => $nonTaxOtherAllowance,
    //         'nonTaxPositionAllowance' => $nonTaxPositionAllowance
    //     ];
    // }

    public function taxable($id, $startDate)
    {
        $taxed = EmpAllowance::where('emp_basic_id', $id)->first();

        // Create a Carbon instance from the start date
        $date = Carbon::createFromFormat('Y-m-d', $startDate);
        $day = $date->day;

        // Calculate the date length
        $dateLength = $this->actualDateLength() - $day;

        $TaxTransportAllowance = 0;
        $TaxMobileAllowance = 0;
        $TaxCleaningAllowance = 0;
        $TaxHousingAllowance = 0;
        $TaxPositionAllowance = 0;

        if ($taxed) {
            if ($dateLength != 0) {

                $TaxTransportAllowance = ($taxed->transport_allowance / $this->actualDateLength()) * $dateLength;
                $TaxMobileAllowance = ($taxed->mobile_allowance / $this->actualDateLength()) * $dateLength;
                $TaxCleaningAllowance = ($taxed->cleaning_allowance / $this->actualDateLength()) * $dateLength;
                $TaxHousingAllowance = ($taxed->housing_allowance / $this->actualDateLength()) * $dateLength;
                $TaxPositionAllowance = ($taxed->position_allowance / $this->actualDateLength()) * $dateLength;
            } else {
                $TaxTransportAllowance = $taxed->transport_allowance;
                $TaxMobileAllowance = $taxed->mobile_allowance;
                $TaxCleaningAllowance = $taxed->cleaning_allowance;
                $TaxHousingAllowance = $taxed->housing_allowance;
                $TaxPositionAllowance = $taxed->position_allowance;
            }
        }
        return [
            'guardTransport' => $TaxTransportAllowance,
            'taxMobileAllowance' => $TaxMobileAllowance,
            'taxCleaningAllowance' => $TaxCleaningAllowance,
            'taxHousingAllowance' => $TaxHousingAllowance,
            'taxPositionAllowance' => $TaxPositionAllowance
        ];
    }

    public function additionalNonTaxAllowance($id, $fromDate, $toDate)
    {
        $additionalNonTaxableAllowance = NonTaxableAllowance::where('emp_basic_id', $id)->where('paid', 0)->whereBetween('effective_date', [$fromDate, $toDate])->get()->reduce(function ($carry, $item) {
            return $carry + $item->transport_allowance + $item->desert_allowance + $item->mobile_allowance + $item->cleaning_allowance + $item->other_allowance + $item->medical_allowance + $item->position_allowance;
        }, 0);

        return [
            'additionalNonTaxableAllowance' => $additionalNonTaxableAllowance
        ];
    }

    public function addpcrate($id, $fromDate, $toDate)
    {
        $pcrates = AddPcrate::where('emp_basic_id', $id)->where('paid', 0)->whereBetween('from_date', [$fromDate, $toDate])->get();
        $totalAddNopc = 0;
        $addPcrateType = '';
        $addPcrate = 0;
        $totalAddpcrateSalary = 0;
        foreach ($pcrates as $pcrate) {
            $totalAddNopc = $totalAddNopc + $pcrate->no_pc;
            $addPcrateType = $pcrate->pc_type;
            $addPcrate = $pcrate->salary;
            $totalAddpcrateSalary = $totalAddpcrateSalary + $pcrate->total;
        }
        return [
            'totalAddNopc' => $totalAddNopc,
            'addPcrateType' => $addPcrateType,
            'addPcrate' => $addPcrate,
            'totalAddpcrateSalary' => $totalAddpcrateSalary
        ];
    }

    public function additional($id, $fromDate, $toDate)
    {
        $adds = Additional::where('emp_basic_id', $id)->where('paid', 0)->whereBetween('effective_date', [$fromDate, $toDate])->get();
        $totalAdditionalHours = 0;
        foreach ($adds as $add) {
            $totalAdditionalHours = $totalAdditionalHours + $add->total;
        }
        return [
            'totalAdditionalHours' => $totalAdditionalHours
        ];
    }

    public function additionalDeductable($id, $fromDate, $toDate)
    {
        $additionalDeductables = AdditionalDeductable::where('emp_basic_id', $id)->whereBetween('deduct_date', [$fromDate, $toDate])->get();
        $totalAddDeduction = 0;
        $addDeductReason = '';
        foreach ($additionalDeductables as $deduct) {
            $totalAddDeduction = $totalAddDeduction + ($deduct->duration * $deduct->amount);
            $addDeductReason = $deduct->reason;
        }

        return [
            'totalAddDeduction' => $totalAddDeduction,
            'addDeductReason' => $addDeductReason,
        ];
    }

    public function absenceList($id, $fromDate, $toDate, $workinDate, $guardSalary)
    {
        $absences = AbsenceList::where('emp_basic_id', $id)->where('penalized', 0)->whereBetween('start_date', [$fromDate, $toDate])->get();
        $totalAbsences = 0;
        $absenceGuardSalary = 0;
        $minHour = 0;
        $minDays = 0;
        foreach ($absences as $absence) {
            if ($absence->rate_type == 'Days') {
                if ($absence->minutes != null) {
                    $minDays = $absence->minutes / (60 * 8);
                }
                $totalAbsences = $totalAbsences + (($absence->hour_day + $minDays
                ) * $absence->penality_rate);
            }
            if ($absence->rate_type == 'Hour') {
                if ($absence->minutes != null) {
                    $minHour = $absence->minutes / 60;
                }
                $totalAbsences = $totalAbsences + ((
                    ($absence->hour_day + $minHour) / 8
                ) * $absence->penality_rate);
            }

            $absenceGuardSalary = ($guardSalary / ($workinDate / 8));
        }

        return [
            'totalAbsences' => $totalAbsences,
            'absenceGuardSalary' => $absenceGuardSalary,
        ];
    }

    public function empDeductable($id)
    {
        $medicalLoan = 0;
        $medicalPeriod = 1;
        $loanPeriod = 1;
        $laborPeriod = 1;
        $creditPeriod = 1;
        $creditLoan = 0;
        $laborLoan = 0;
        $totalOtherLoan = 0;
        $loan = 0;
        $totalMedicalLoan = 0;
        $totalLaborLoan = 0;
        $totalCreditLoan = 0;
        $totalLoan = 0;
        $empdeducts = EmployeeDeductable::where('emp_basic_id', $id)->get();
        $status = EmpStatus::where('emp_basic_id', $id)->first();
        foreach ($empdeducts as $deducts) {
            if ($deducts->loan_type == 'Medical Loan' && $deducts->repayment_status != 1) {
                $medicalLoan = $deducts->loan_amount;
                $medicalPeriod = $deducts->loan_period;
            } else if ($deducts->loan_type == 'Labor Loan' && $deducts->repayment_status != 1) {
                $laborLoan = $deducts->loan_amount;
                $laborPeriod = $deducts->loan_period;
            } else if ($deducts->loan_type == 'Credit Loan' && $deducts->repayment_status != 1) {
                $creditLoan = $deducts->loan_amount;
                $creditPeriod = $deducts->loan_period;
            } else if (
                $deducts->loan_type == 'Other Loan' &&
                $deducts->repayment_status != 1
            ) {
                $otherLoan = $deducts->loan_amount;
                $otherLoanPeriod = $deducts->loan_period;
                $totalOtherLoan = $totalOtherLoan + ($otherLoan / $otherLoanPeriod);
            } else {
                if ($deducts->repayment_status != 1) {
                    $loan = $deducts->loan_amount;
                    $loanPeriod = $deducts->loan_period;
                }
            }
        }

        $totalMedicalLoan = $medicalLoan / $medicalPeriod;
        $totalLaborLoan = $laborLoan / $laborPeriod;
        $totalCreditLoan = $creditLoan / $creditPeriod;
        $totalLoan = $loan / $loanPeriod;

        if (!$status || !$status->labor_union) {
            return [
                'laborLoan' => $laborLoan,
                'totalOtherLoan' => $totalOtherLoan,
                'loan' => $loan,
                'totalMedicalLoan' => $totalMedicalLoan,
                'totalLaborLoan' => $totalLaborLoan,
                'totalCreditLoan' => $totalCreditLoan,
                'totalLoan' => $totalLoan,


            ];
        }
        if (!$status->creadit_association) {
            return [
                'laborLoan' => $laborLoan,
                'totalOtherLoan' => $totalOtherLoan,
                'loan' => $loan,
                'totalMedicalLoan' => $totalMedicalLoan,
                'totalLaborLoan' => $totalLaborLoan,
                'totalCreditLoan' => 0,
                'totalLoan' => $totalLoan,


            ];
        }
        return [
            'laborLoan' => $laborLoan,
            'totalOtherLoan' => $totalOtherLoan,
            'loan' => $loan,
            'totalMedicalLoan' => $totalMedicalLoan,
            'totalLaborLoan' => $totalLaborLoan,
            'totalCreditLoan' => $totalCreditLoan,
            'totalLoan' => $totalLoan,


        ];
    }

    public function totalLeaveTakenWithoutPayTaken($id, $cid, $fromDate, $toDate)
    {
        $leaves = 0;
        $leaveTaken = AnnualLeave::where('client_id', $cid)->where('emp_basic_id', $id)->where(
            'reason',
            'Leave with out pay'
        )->whereBetween('reg_date', [$fromDate, $toDate])->select('leave_taken')->get();
        foreach ($leaveTaken as $leave) {
            $leaves = $leaves + $leave;
        }

        return $leaves;
    }

    public function totalLeaveDuration($id, $cid, $fromDate, $toDate)
    {
        $leaveTaken = 0;
        $diffDays = 0;
        $leaveDuration = AnnualLeave::where('emp_basic_id', $id)->where('client_id', $cid)->whereBetween('reg_date', [$fromDate, $toDate])->get();
        foreach ($leaveDuration as $leave) {
            $leaveTaken = $leaveTaken + $leave->leave_taken;
            $firstDate = Carbon::parse($leave->from_date);
            $lastDate = Carbon::parse($leave->to_date);
            $diffDays = $firstDate->diffInDays($lastDate);
        }

        return [
            'leave_taken' => $leaveTaken,
            'daysduration' => $diffDays
        ];
    }

    public function incomeTax($taxableIncome)
    {
        $incomeTax = 0;
        if ($taxableIncome > 0 && $taxableIncome <= 600) {
            return $incomeTax;
        } else if ($taxableIncome > 601 && $taxableIncome <= 1650) {
            $incomeTax = ($taxableIncome * 10) / 100 - 60;

            return $incomeTax;
        } else if ($taxableIncome > 1651 && $taxableIncome <= 3200) {
            $incomeTax = ($taxableIncome * 15) / 100 - 142.50;
            return $incomeTax;
        } else if ($taxableIncome > 3201 && $taxableIncome <= 5250) {
            $incomeTax = ($taxableIncome * 20) / 100 - 302.50;

            return $incomeTax;
        } else if ($taxableIncome > 5251 && $taxableIncome <= 7800) {
            $incomeTax = ($taxableIncome * 25) / 100 - 565.00;
            return $incomeTax;
        } else if ($taxableIncome > 7801 && $taxableIncome < 10900) {
            $incomeTax = ($taxableIncome * 30) / 100 - 955.00;
            return $incomeTax;
        } else if ($taxableIncome > 10900) {
            $incomeTax = ($taxableIncome * 35) / 100 - 1500.00;
            return $incomeTax;
        } else {
            return $incomeTax;
        }
    }

    public function updateOvertime($id)
    {
        $payrolldate = $this->setPayrollDate();
        $overtimes = OverTime::where('emp_basic_id', $id)->where(
            'paid',
            0
        )->whereBetween('start_date', [$payrolldate['startDate'], $payrolldate['endDate']])->get();
        foreach ($overtimes as $overtime) {
            OverTime::where('id', $overtime->id)->update([
                'paid' => 1
            ]);
        }
    }

    public function updateAdditional($id)
    {
        $payrolldate = $this->setPayrollDate();
        $additionals = Additional::where('emp_basic_id', $id)->where(
            'paid',
            0
        )->whereBetween('effective_date', [$payrolldate['startDate'], $payrolldate['endDate']])->get();

        foreach ($additionals as $additional) {
            Additional::where('id', $additional->id)->update([
                'paid' => 1
            ]);
        }
    }

    public function updatePcrate($id)
    {
        $payrolldate = $this->setPayrollDate();
        $pcrates = PcRate::where('emp_basic_id', $id)->where(
            'paid',
            0
        )->whereBetween('from_date', [$payrolldate['startDate'], $payrolldate['endDate']])->get();

        foreach ($pcrates as $pcrate) {
            PcRate::where('id', $pcrate->id)->update([
                'paid' => 1
            ]);
        }
    }

    public function updateAddPcrate($id)
    {
        $payrolldate = $this->setPayrollDate();
        $addpcrates = AddPcrate::where('emp_basic_id', $id)->where(
            'paid',
            0
        )->whereBetween('from_date', [$payrolldate['startDate'], $payrolldate['endDate']])->get();

        foreach ($addpcrates as $addpcrate) {
            AddPcrate::where('id', $addpcrate->id)->update([
                'paid' => 1
            ]);
        }
    }

    public function updateDsa($id)
    {
        $payrolldate = $this->setPayrollDate();
        $dsas = Dsa::where('emp_basic_id', $id)->where(
            'paid',
            0
        )->whereBetween('from_date', [$payrolldate['startDate'], $payrolldate['endDate']])->get();

        foreach ($dsas as $dsa) {
            Dsa::where('id', $dsa->id)->update([
                'paid' => 1
            ]);
        }
    }

    public function updateAdditionalNonTaxAllowance($id)
    {
        $payrolldate = $this->setPayrollDate();
        $nontaxs = NonTaxableAllowance::where('emp_basic_id', $id)->where(
            'paid',
            0
        )->whereBetween('effective_date', [$payrolldate['startDate'], $payrolldate['endDate']])->get();

        foreach ($nontaxs as $nontax) {
            NonTaxableAllowance::where('id', $nontax->id)->update([
                'paid' => 1
            ]);
        }
    }

    public function updateAdditionalDeductable($id)
    {
        $payrolldate = $this->setPayrollDate();
        $deductables = AdditionalDeductable::where('emp_basic_id', $id)->where(
            'paid',
            0
        )->whereBetween('deduct_date', [$payrolldate['startDate'], $payrolldate['endDate']])->get();

        foreach ($deductables as $deduct) {
            AdditionalDeductable::where('id', $deduct->id)->update([
                'paid' => 1
            ]);
        }
    }

    public function updateDailyrate($id)
    {
        $payrolldate = $this->setPayrollDate();
        $dailyRates = DailyRate::where('emp_basic_id', $id)->where(
            'paid',
            0
        )->whereBetween('from_date', [$payrolldate['startDate'], $payrolldate['endDate']])->get();

        foreach ($dailyRates as $deduct) {
            DailyRate::where('id', $deduct->id)->update([
                'paid' => 1
            ]);
        }
    }

    public function updateAbsence($id)
    {
        $payrolldate = $this->setPayrollDate();
        $absenceses = AbsenceList::where('emp_basic_id', $id)->where(
            'penalized',
            0
        )->whereBetween('start_date', [$payrolldate['startDate'], $payrolldate['endDate']])->get();

        foreach ($absenceses as $deduct) {
            AbsenceList::where('id', $deduct->id)->update([
                'penalized' => 1
            ]);
        }
    }

    public function updateEmployeeDeductable($id)
    {
        $empdeduct = EmployeeDeductable::where('emp_basic_id', $id)->where(
            'repayment_status',
            0
        )->get();

        foreach ($empdeduct as $deduct) {
            if ($deduct->loan_type == 'Medical Loan') {
                if ($deduct->loan_amount != 0) {
                    $unpaidLoan = $deduct->loan_amount;
                    if ($deduct->loan_period > 0) {
                        $loanperiod = $deduct->loan_period - 1;
                        EmployeeDeductable::where('id', $deduct->id)
                            ->update([
                                'loan_amount' => $unpaidLoan,
                                'loan_period' => $loanperiod,
                                'repayment_status' => 1
                            ]);
                    } else {
                        EmployeeDeductable::where('id', $deduct->id)
                            ->update([
                                'repayment_status' => 1
                            ]);
                    }
                } else if ($deduct->loan_type == 'Labor Loan') {
                    if ($deduct->loan_amount != 0) {
                        $unpaidLoan = $deduct->loan_amount;
                        if ($deduct->loan_period > 0) {
                            $loanperiod = $deduct->loan_period - 1;
                        }
                        if ($unpaidLoan == 0) {
                            EmployeeDeductable::where('id', $deduct->id)
                                ->update([
                                    'loan_amount' => $unpaidLoan,
                                    'loan_period' => $loanperiod,
                                    'repayment_status' => 1
                                ]);
                        } else {
                            EmployeeDeductable::where('id', $deduct->id)
                                ->update([
                                    'repayment_status' => 1
                                ]);
                        }
                    }
                } else if ($deduct->loan_type == 'Credit Loan') {
                    if ($deduct->loan_amount != 0) {
                        $unpaidLoan = $deduct->loan_amount;
                        if ($deduct->loan_period > 0) {
                            $loanperiod = $deduct->loan_period - 1;
                        }
                        if ($unpaidLoan == 0) {
                            EmployeeDeductable::where('id', $deduct->id)
                                ->update([
                                    'loan_amount' => $unpaidLoan,
                                    'loan_period' => $loanperiod,
                                    'repayment_status' => 1
                                ]);
                        } else {
                            EmployeeDeductable::where('id', $deduct->id)
                                ->update([
                                    'repayment_status' => 1
                                ]);
                        }
                    }
                } else if ($deduct->loan_type == 'Other Loan') {
                    if ($deduct->loan_amount != 0) {
                        $unpaidLoan = $deduct->loan_amount;
                        if ($deduct->loan_period > 0) {
                            $loanperiod = $deduct->loan_period - 1;
                        }
                        if ($unpaidLoan == 0) {
                            EmployeeDeductable::where('id', $deduct->id)
                                ->update([
                                    'loan_amount' => $unpaidLoan,
                                    'loan_period' => $loanperiod,
                                    'repayment_status' => 1
                                ]);
                        } else {
                            EmployeeDeductable::where('id', $deduct->id)
                                ->update([
                                    'repayment_status' => 1
                                ]);
                        }
                    }
                } else {
                    if ($deduct->loan_amount != 0) {
                        $unpaidLoan = $deduct->loan_amount;
                        if ($deduct->loan_period > 0) {
                            $loanperiod = $deduct->loan_period - 1;
                        }
                        EmployeeDeductable::where('id', $deduct->id)
                            ->update([
                                'loan_amount' => $unpaidLoan,
                                'loan_period' => $loanperiod,
                                'repayment_status' => 1
                            ]);
                    } else {
                        EmployeeDeductable::where('id', $deduct->id)
                            ->update([
                                'repayment_status' => 1
                            ]);
                    }
                }
            }
        }
    }

    public function getPay($clientId)
    {
        $payDate = $this->setPayrollDate();
        $empIds = DB::table('payrolls')->select('emp_basic_id')->where('client_id', $clientId)->whereBetween('payrolls.pay_date', [$payDate['startDate'], $payDate['endDate']])->get();

        return response($empIds);
    }

    public function getPcRate($empId, $fromDate, $endDate)
    {
        $totalNoPc = 0;
        $pcType = '';
        $pcRate = 0;
        $pcrate = PcRate::where('emp_basic_id', $empId)->where('paid', 1)->whereBetween('from_date', [$fromDate, $endDate])->get();

        foreach ($pcrate as $pc) {
            $totalNoPc += $pc['no_pc'];
            $pcRate = $pc['salary'];
            $pcType = $pc['pc_type'];
        }
        return [
            'totalNoPc' => $totalNoPc,
            'pcRate' => $pcRate,
            'pcType' => $pcType
        ];
    }

    function termPayroll($id)
    {

        //Log::info('Emp and Client IDs: ' . json_encode([$id]));

        $date = Termination::where('emp_basic_id', '=', $id)->first();
        $payrollStartDate = $date->payroll_start_date;
        $payrollEndDate = $date->payroll_end_date;

        return [
            'startDate' => $payrollStartDate,
            'endDate' => $payrollEndDate,
        ];
    }
}
