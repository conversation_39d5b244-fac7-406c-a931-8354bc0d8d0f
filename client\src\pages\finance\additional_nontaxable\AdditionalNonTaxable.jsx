import React from "react";
import { Box, Grid, Paper, useTheme } from "@mui/material";
import { tokens } from "../../../utils/theme";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";
import Header from "../../../components/Header";
import CeSearchComponent from "../../../components/CeSearchComponent";
import SearchBar from "../../../components/SearchBar";
import EmployeeSelecter from "../../../components/EmployeeSelecter";
import AdditionalNonTaxableForm from "./AdditionalNonTaxableForm";
import AdditionalNonTaxableList from "./AdditionalNonTaxableList";
function AdditionalNonTaxable() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [clicked, setClicked] = useState(false);
  const [editData, setEditData] = useState({});
  const [formData, setFormData] = useState({});

  const onSelectedRow = (value) => {
    setEditData(value);
  };
  const handleClietSelect = ({value}) => {
    setFormData({
      ...formData,
      client_id: value.id,
    });
  };
  const handleEmployeeSelect = ({ value, clicked }) => {
    setFormData({
      ...formData,
      emp_basic_id: value.id,
    });
    setClicked(clicked);
  };
  const { data: assignedEmployee ,isFetched} = useQuery(
    ["assignedEmployee", formData.client_id],
    async () =>
      await api.get(`assign/client/${formData.client_id}`).then(({ data }) => {
        return data.data;
      }),
    {
      enabled: !!formData.client_id,
    }
  );

  return (
    <Box m="5px">
      <Header
        title="Add Additional Non Taxable"
        subtitle="add additional non taxable here"
      />
      <Box>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}>
            <Paper
              sx={{
                padding: "1rem",
                color: colors.grey[100],
                background: colors.primary[400],
              }}
            >
              <CeSearchComponent
                ClientSearch={
                  <SearchBar
                    onClientSelect={handleClietSelect}
                  />
                }
                EmployeeSelecter={
                  <EmployeeSelecter
                    assignedEmployee={assignedEmployee}
                    isFetched={isFetched}
                    onEmployeeSelect={handleEmployeeSelect}
                  />
                }
              />
            </Paper>
          </Grid>
          <Grid item xs={12} sm={8}>
            {clicked && (
              <Box>
                <Paper
                  sx={{
                    padding: "1rem",
                    color: colors.grey[100],
                    background: colors.primary[400],
                  }}
                >
                  <AdditionalNonTaxableForm
                    editData={editData}
                    formData={formData}
                    setEditData={setEditData}
                  />
                </Paper>
                <Paper
                  sx={{
                    marginTop: "30px",
                    padding: "1rem",
                    color: colors.grey[100],
                    background: colors.primary[400],
                  }}
                >
                  <AdditionalNonTaxableList
                    nontaxable={formData}
                    selectedRow={onSelectedRow}
                  />
                </Paper>
              </Box>
            )}
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}

export default AdditionalNonTaxable;
