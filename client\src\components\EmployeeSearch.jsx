import { Card, MenuItem, TextField, useTheme } from "@mui/material";
import React from "react";
import { useState } from "react";
import Minisearch from "minisearch";
import { tokens } from "../utils/theme";

const EmployeeSearch = ({ onHandleSelect, employee, margin, label, size }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [selected, setSelected] = useState(0);
  const [empId, setEmpId] = useState("");
  const [res, setRes] = useState([]);

  const miniSearch = new Minisearch({
    fields: ["first_name", "middle_name", "last_name"],
    storeFields: ["id", "first_name", "last_name", "middle_name"],
  });
  if (employee) {
    miniSearch.addAll(employee);
  }
  const handleChange = (e) => {
    setRes(
      miniSearch.search(e.target.value, {
        fuzzy: 0.3,
      })
    );
    setEmpId(e.target.value);
  };
  const handleSelect = (value) => {
    onHandleSelect(value);
    setRes([]);
  };

  return (
    <>
      <TextField
        margin={margin ?? "normal"}
        label={label}
        name="emp_basic_id"
        fullWidth
        type="search"
        InputLabelProps={{
          color: "success",
        }}
        onChange={handleChange}
        value={empId}
        size={size ?? undefined}
      />
      <Card>
        {res.map((value) => (
          <MenuItem
            sx={{
              background: selected === value.id ? colors.blueAccent[500] : "",
            }}
            key={value.id}
            value={value.id}
            onClick={() => {
              handleSelect(value);
              setSelected(value.id);
              setEmpId(
                `${value.first_name} ${value.middle_name} ${value.last_name}`
              );
            }}
          >
            {value.first_name} {value.middle_name} {value.last_name}
          </MenuItem>
        ))}
      </Card>
    </>
  );
};

export default EmployeeSearch;
