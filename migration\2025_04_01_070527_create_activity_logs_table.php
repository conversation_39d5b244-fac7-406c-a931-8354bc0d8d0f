<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('activity_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Link to user, set null if user deleted
            $table->string('method'); // GET, POST, PUT, DELETE
            $table->string('url', 1000); // Store the full URL (increase length if needed)
            $table->string('ip_address')->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('request_body')->nullable(); // Store request body (consider json or text)
            $table->integer('response_status')->nullable(); // HTTP status code
            $table->timestamps(); // created_at and updated_at

            $table->index('user_id'); // Index for faster user lookups
            $table->index('created_at'); // Index for sorting by time
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('activity_logs');
    }
};
