import { Box, Button, Typography, useTheme } from "@mui/material";
import { Link, useRouteError } from "react-router-dom";
import { tokens } from "../../utils/theme";
function Error() {
  const errors = useRouteError();
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  return (
    <Box
      sx={{
        position: "absolute",
        left: "30%",
        top: "30%",
        transform: "translate(-15%, -30%)",
      }}
    >
      <Typography variant="h3" pb={2}>
        Oops!
      </Typography>
      <Typography variant="h6" color='error' pb={1}>
        You Have Accessed The Route That Doesn't Exist
      </Typography>
      <Typography variant="h6" pb={2}>
        {errors.status} Route Is {errors.statusText}
      </Typography>
      <Button LinkComponent={Link} to="/" variant="contained" disableElevation sx={{px:4,py:1}}>
        Go Back
      </Button>
    </Box>
  );
}

export default Error;
