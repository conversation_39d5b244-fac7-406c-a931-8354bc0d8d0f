import { AppBar, Box, Tab, Tabs, useTheme } from '@mui/material';
import React, { useState } from 'react';
import { tokens } from '../../../utils/theme';
import Notifications from './Notifications';
import ReadNotifications from './ReadNotifications';
import UnreadNotifications from './UnreadNotifications';
function TabPanel({ children, value, index }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`full-width-tabpanel-${index}`}
      aria-labelledby={`full-width-tab-${index}`}
    >
      {value === index && children}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `full-width-tab-${index}`,
    'aria-controls': `full-width-tabpanel-${index}`,
  };
}

const AllNotifications = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [value, setValue] = useState(0);
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <Box
      sx={{
        margin: '10px',
      }}
    >
      {/* <AppBar
        position="static"
        color="transparent"
        // sx={{
        //   padding: "1rem",
        //   color: colors.grey[100],
        //   background: colors.primary[400],
        // }}
      > */}
      <Tabs
        value={value}
        onChange={handleChange}
        TabIndicatorProps={{
          style: { backgroundColor: colors.blueAccent[500] },
        }}
        variant="fullWidth"
        selectionFollowsFocus
      >
        <Tab
          label="all notifications"
          {...a11yProps(2)}
          sx={{
            '&.Mui-selected': {
              color: `${colors.blueAccent[400]}`,
            },
          }}
        />
        <Tab
          label="unread Notifications"
          {...a11yProps(0)}
          sx={{
            '&.Mui-selected': {
              color: `${colors.blueAccent[400]}`,
            },
          }}
        />

        <Tab
          label="viewed Notifications"
          {...a11yProps(2)}
          sx={{
            '&.Mui-selected': {
              color: `${colors.blueAccent[400]}`,
            },
          }}
        />
      </Tabs>
      {/* </AppBar> */}

      <TabPanel value={value} index={1}>
        <UnreadNotifications />
      </TabPanel>
      <TabPanel value={value} index={0}>
        <Notifications />
      </TabPanel>
      <TabPanel value={value} index={2}>
        <ReadNotifications />
      </TabPanel>
    </Box>
  );
};

export default AllNotifications;
