import { Box, Button, Grid, Paper, TextField, useTheme } from "@mui/material";
import React from "react";
import Header from "../../../components/Header";
import {
  useSeveranceCalculateion,
  useTeminatedEmployeeSeverance,
} from "../../../api/userApi/clinetHook";
import EmployeeSearch from "../../../components/EmployeeSearch";
import { useState } from "react";
import { useStateContext } from "../../../context/ContextProvider";
import { tableStyle } from "../../../utils/tablestyle";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";
import { tokens } from "../../../utils/theme";

const SeverancePayment = () => {
  const queryClinet = useQueryClient();
  const { setNotification, user } = useStateContext();
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [formData, setFormData] = useState({
    emp_basic_id: "",
    approval_date: new Date().toISOString().slice(0, 10),
  });
  const { data: employees } = useTeminatedEmployeeSeverance();
  const handleSelect = (data) => {
    setFormData({
      ...formData,
      emp_basic_id: data.id,
    });
  };

  const {
    data: severance,
    isLoading,
    isFetched,
  } = useSeveranceCalculateion(formData.emp_basic_id);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const insertSeverance = useMutation(
    (payload) => api.post("severances", payload),
    {
      onSuccess: () => {
        setNotification("severance Payment Made Successfully ");
        queryClinet.invalidateQueries({ queryKey: ["terminated_emps"] });
      },
    }
  );
  const handleSubmit = (e) => {
    e.preventDefault();
    const payload = {
      start_date: severance.start_date,
      emp_basic_id: formData.emp_basic_id,
      termination_date: severance.termination_date,
      basic_salary: severance.basic_salary,
      y_of_service: severance.y_of_service,
      total_severance_pay: severance.total_severance_pay,
      severance_tax: severance.severance_tax,
      severance_net: severance.severance_net,
      termination_reason: severance.termination_reason,
      approval_date: formData.approval_date,
      user_id: user.id,
    };
    // window.print();
    // return true;
    insertSeverance.mutate(payload);
  };
  return (
    <Box margin="10px">
      <Header
        title="Severance Pay"
        subtitle="search terminated employee here inorder to pay severance "
      />
      <Grid container spacing={2}>
        <Grid item xs={12} sm={3}>
          <EmployeeSearch
            employee={employees}
            label="Search Terminated Employee"
            onHandleSelect={handleSelect}
          />
        </Grid>
        <Grid item xs={12} sm={9}>
          {isFetched && (
            <Box component="form" onSubmit={handleSubmit} marginBottom={1}>
              <Paper
                sx={{
                  padding: "1rem",
                  color: colors.grey[100],
                  background: colors.primary[400],
                }}
              >
                <table style={tableStyle.table}>
                  <thead>
                    <tr>
                      <th style={tableStyle.th}>Field</th>
                      <th style={tableStyle.th}>Value</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td style={tableStyle.td}>fullName</td>
                      <td style={tableStyle.td}>{severance.fullname}</td>
                    </tr>
                    <tr>
                      <td style={tableStyle.td}>Employee StartDate</td>
                      <td style={tableStyle.td}>{severance.start_date}</td>
                    </tr>
                    <tr>
                      <td style={tableStyle.td}>employee TerminationDate</td>
                      <td style={tableStyle.td}>
                        {severance.termination_date}
                      </td>
                    </tr>
                    <tr>
                      <td style={tableStyle.td}>Basic Salary</td>
                      <td style={tableStyle.td}>{severance.basic_salary}</td>
                    </tr>
                    <tr>
                      <td style={tableStyle.td}>Year Of Service</td>
                      <td style={tableStyle.td}>{severance.y_of_service}</td>
                    </tr>
                    <tr>
                      <td style={tableStyle.td}>Gross SeverancePayment</td>
                      <td style={tableStyle.td}>
                        {severance.total_severance_pay}
                      </td>
                    </tr>
                    <tr>
                      <td style={tableStyle.td}>Severance Tax</td>
                      <td style={tableStyle.td}>{severance.severance_tax}</td>
                    </tr>
                    <tr>
                      <td style={tableStyle.td}>Severance Net Payment</td>
                      <td style={tableStyle.td}>{severance.severance_net}</td>
                    </tr>
                    <tr>
                      <td style={tableStyle.td}>Termination Reason</td>
                      <td style={tableStyle.td}>
                        {severance.termination_reason}
                      </td>
                    </tr>
                    <tr>
                      <td style={tableStyle.td}>Aproval Date</td>
                      <td style={tableStyle.td}>
                        <TextField
                          margin="normal"
                          label="ApprovalDate"
                          name="approval_date"
                          fullWidth
                          variant="standard"
                          type="date"
                          InputLabelProps={{
                            color: "success",
                          }}
                          onChange={handleChange}
                          value={formData.approval_date}
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
                <Button
                  type="submit"
                  variant="contained"
                  color="success"
                  size="small"
                >
                  approval
                </Button>
              </Paper>
            </Box>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default SeverancePayment;
