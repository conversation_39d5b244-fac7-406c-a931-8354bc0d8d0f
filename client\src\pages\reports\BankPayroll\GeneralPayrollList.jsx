import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { useLocation } from 'react-router-dom';
import api from '../../../api/config/axiosConfig';
import { Box } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import _CircularProgress from '../../../components/_CircularProgress';
import CustomToolBar from '../../../helper/CustomToolBar';
import Header from '../../../components/Header';
import { useState } from 'react';
import { formatedMessage } from '../formattedMessage';

function GeneralPayrollList() {
  const location = useLocation();
  const recivedData = location.state;
  const [companyName, setCompanyName] = useState('');
  const [region, setRegion] = useState('');
  const company = recivedData.company;

  const {
    data: response,
    isLoading,
    isFetched,
  } = useQuery(['getgeneralpayroll'], () =>
    api.get('getgeneralpayroll', { params: recivedData }).then(({ data }) => {
      console.log(data)

      if (data.length > 0) {
        setCompanyName(
          recivedData.company ? recivedData.company : data[0].company_name
        );
        setRegion(recivedData.region);
        console.log(data);
        const uniqueRows = Array.from(
          new Map(data.map((item) => [item.id, item])).values()
        );
        console.log(uniqueRows, recivedData);
        return uniqueRows;
      }
    })
  );

  if (response) {
    console.log(response);
  }

  const numberFormatter = (value) => value.toLocaleString();

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
    },
    {
      field: 'gender',
      headerName: 'Gender',
    },
    {
      field: 'start_date',
      headerName: 'Start Date',
    },
    {
      field: 'first_name',
      headerName: 'First Name',
    },
    {
      field: 'middle_name',
      headerName: 'Middle Name',
    },
    {
      field: 'last_name',
      headerName: 'Last Name',
    },
    {
      field: 'total_work_duration',
      headerName: 'Total Working Duration',
    },
    {
      field: 'basic_salary',
      headerName: 'Basic Salary',
      // valueFormatter: ({ value }) => numberFormatter(value? value : ''),
    },
    {
      field: 'nontax_transport_allowance',
      headerName: 'Non Tax Transport Allowance',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'transport_allowance',
      headerName: 'Transport Allowance',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'over_time',
      headerName: 'overtime',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'nontax_position_allowance',
      headerName: 'NonTax Position Allowance',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'nontax_mobile_allowance',
      headerName: 'NonTax Mobile Allowance',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'nontax_desert_allowance',
      headerName: 'NonTax Desert Allowance',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'nontax_cleaning_allowance',
      headerName: 'NonTax Cleaning Allowance',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'nontax_dsa_allowance',
      headerName: 'NonTax DSA Allowance',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'tax_dsa_allowance',
      headerName: 'Tax DSA Allowance',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'loan',
      headerName: 'Company Loan',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'medical_cost',
      headerName: 'Medical Loan',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'other_loan',
      headerName: 'Other Loan',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'labor_union',
      headerName: 'Labor Union',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'social_support',
      headerName: 'Social Support',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'creadit_association',
      headerName: 'Credit Association',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'sport_association',
      headerName: 'Sport Association',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    // {
    //   field: 'transport_allowance_deduction',
    //   headerName: 'Transport Deduction',
    //   valueFormatter: ({ value }) => numberFormatter(value),
    // },
    {
      field: 'total_Deduction',
      headerName: 'Total Deduction',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'absence',
      headerName: 'Absence Deduction',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    // {
    //   field: 'non_taxable_income_summry',
    //   headerName: 'Total Non Taxable Income',
    //   valueFormatter: ({ value }) => numberFormatter(value),
    // },

    {
      field: 'income_tax',
      headerName: 'Income Tax',
      cellClassName: 'custom-tax',
      headerClassName: 'custom-tax',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'pension_emp',
      headerName: 'pension Emp',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'pension_sns',
      headerName: 'pension Comp',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'Gross_salary',
      headerName: 'Gross Salary',
      cellClassName: 'custom-non-tax',
      headerClassName: 'custom-non-tax',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'total_deduction',
      headerName: 'Total Deduction Summry',
      cellClassName: 'custom-ded',
      headerClassName: 'custom-ded',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'net_pay',
      headerName: 'Net Payment',
      cellClassName: 'custom-pay',
      headerClassName: 'custom-pay',
      //valueFormatter: ({ value }) => numberFormatter(value),
    },
    {
      field: 'userName',
      headerName: 'User Name',
    },
    {
      field: 'company_name',
      headerName: 'Site Name',
    },
    {
      field: 'company_code',
      headerName: 'Company Name',
    },
  ];

  return (
    <Box margin="10px">
      {recivedData && recivedData.clientId ? (
        <Header
          title={`${companyName} General Payroll REPORT `}
          subtitle={formatedMessage(
            recivedData.firstDate,
            recivedData.lastDate,
            recivedData.year
          )}
        />
      ) : (
        <Header
          title={`${company} General Payroll REPORT `}
          subtitle={formatedMessage(
            recivedData.firstDate,
            recivedData.lastDate,
            recivedData.year,
            recivedData.taxCenter
          )}
        />
      )}

      {recivedData.region && (
        <Header
          title={`${region} General Payroll REPORT `}
          subtitle={formatedMessage(
            recivedData.firstDate,
            recivedData.lastDate,
            recivedData.year
          )}
        />
      )}

      {/* {isLoading && <_CircularProgress />}
      {isFetched && (
        <DataGrid
          columns={columns}
          rows={response}
          hideFooter
          autoHeight
          components={{ Toolbar: CustomToolBar }}
        />
      )} */}

      {isLoading && <_CircularProgress />}
      {response && (
        <DataGrid
          columns={columns}
          rows={response} // Use rows with total net pay
          //hideFooter
          autoHeight
          components={{ Toolbar: CustomToolBar }}
        />
      )}
    </Box>
  );
}

export default GeneralPayrollList;
