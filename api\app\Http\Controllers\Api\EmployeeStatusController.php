<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\EmpStatus;
use App\Http\Requests\StoreEmpStatusRequest;
use App\Http\Requests\UpdateEmpStatusRequest;
use App\Http\Resources\EmpStatusResource;

class EmployeeStatusController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return EmpStatusResource::collection(EmpStatus::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreEmpStatusRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreEmpStatusRequest $request)
    {
        $data = $request->validated();
        $empstatus = EmpStatus::create([
            'emp_basic_id'=>$data['emp_basic_id'],
            'certificate_medical'=>$request->input('certificate_medical'),
            'marital_status' => $request->marital_status,
            'driving_license' => $request->driving_license,
            'criminal_record' => $request->criminal_record,
            'living_certificate' => $request->living_certificate,
            'labor_union' => $data['labor_union'],
            'credit_association' => $data['credit_association'],
            'sport_association' => $data['sport_association'],
            'social_support' => $request->social_support,
            'finggure_print' => $data['finggure_print'],
            'holiday' => $data['holiday'],
            'eng_lang' => $request->eng_lang,
            'user_id' => $request->user_id,
        ]);

        return response(new EmpStatusResource($empstatus));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EmpStatus  $empStatus
     * @return \Illuminate\Http\Response
     */
    public function show(EmpStatus $empStatus)
    {
        return response(new EmpStatusResource($empStatus));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateEmpStatusRequest  $request
     * @param  \App\Models\EmpStatus  $empStatus
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateEmpStatusRequest $request, EmpStatus $empStatus)
    {
        $data = $request->validated();
        $empStatus->update([
            'certificate_medical'=>$request->input('certificate_medical'),
            'marital_status' => $request->marital_status,
            'driving_license' => $request->driving_license,
            'criminal_record' => $request->criminal_record,
            'living_certificate' => $request->living_certificate,
            'labor_union' => $data['labor_union'],
            'credit_association' => $data['credit_association'],
            'sport_association' => $data['sport_association'],
            'social_support' => $request->social_support,
            'finggure_print' => $data['finggure_print'],
            'holiday' => $data['holiday'],
            'eng_lang' => $request->eng_lang,
            'user_id' => $request->user_id,
        ]);

        return response($empStatus);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EmpStatus  $empStatus
     * @return \Illuminate\Http\Response
     */
    public function destroy(EmpStatus $empStatus)
    {
        $empStatus->delete();

        return response('', 204);
    }
}
