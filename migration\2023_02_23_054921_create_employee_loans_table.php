<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('employee_loans', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('emp_basic_id');
            $table->foreign('emp_basic_id')->references('id')->on('emp_basics')->onDelete('cascade');
            $table->string('loan_type')->nullable();
            $table->string('loan_amount')->nullable();
            $table->string('loan_period')->nullable();
            $table->string('remaining_amount')->nullable();
            $table->string('remaining_repayment_period')->nullable();
            $table->string('repayment_status')->nullable();
            $table->date('loan_date')->format('Y-m-d')->nullable();
            $table->string('reason')->nullable();
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
    }
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('employee_loans');
    }
};
