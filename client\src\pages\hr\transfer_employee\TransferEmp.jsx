import { Box, Grid, <PERSON><PERSON>ield, MenuI<PERSON>, But<PERSON> } from "@mui/material";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import React from "react";
import { useState } from "react";
import api from "../../../api/config/axiosConfig";
import { useStateContext } from "../../../context/ContextProvider";
import SearchBar from "../../../components/SearchBar";
function TransferEmp({ id }) {
  const useQueryClinet = useQueryClient();
  const { setNotification } = useStateContext();
  const [formData, setFormData] = useState({
    emp_basic_id: "",
    f_client_id: "",
    t_client_id: "",
    effective_date: "",
    reason: "",
  });
  const { data: client, isFetched } = useQuery(
    ["client"],
    () => api.get("client").then(async ({ data }) => await data.data),
    { refetchOnWindowFocus: false, staleTime: 60000 }
  );
  //fetch employee assigned to a client
  const { data: assignedEmployee } = useQuery(
    ["assign", formData.f_client_id],
    () =>
      api
        .get(`assign/client/${formData.f_client_id}`)
        .then(async ({ data }) => await data.data),
    {
      enabled: !!formData.f_client_id,
    }
  );
  

  const { data: assegn } = useQuery(
    ["assegn", formData.emp_basic_id],
    () =>
      api
        .get(`assign/employee/${formData.emp_basic_id}`)
        .then(async ({ data }) => await data),
    {
      enabled: !!formData.emp_basic_id,
    }
  );
  const handleClientSelect1 = ({ value}) => {
    setFormData({
      ...formData,
      f_client_id:value.id
    });
  };
  const handleClientSelect2 = ({ value}) => {
    setFormData({
      ...formData,
      t_client_id:value.id
    });
  };
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const transfer = useMutation((data) => api.post("transfer", data), {
    onSuccess: () => {
      useQueryClinet.invalidateQueries(["transfered", "assign", "assegn"]);
      setNotification("Employee Transferd Successfully");
    },
  });
  const handleSubmit = (e) => {
    e.preventDefault();
    api
      .put(`assign/${assegn.id}`, {
        emp_basic_id: assegn.emp_basic_id,
        client_id: formData.t_client_id,
        position: assegn.position,
      })
      .then(() => {
        useQueryClinet.invalidateQueries(["assegn,assign"]);
      });

    transfer.mutate(formData);
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={4}>
        <SearchBar client={client} onClientSelect={handleClientSelect1}/>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
        <SearchBar client={client} onClientSelect={handleClientSelect2}/>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            margin="normal"
            label="Employee To Be Transferd"
            name="emp_basic_id"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.emp_basic_id}
          >
            {assignedEmployee ? (
              assignedEmployee.map((val, index) => (
                <MenuItem key={index} value={val.id}>
                  {val.first_name}
                </MenuItem>
              ))
            ) : (
              <MenuItem value={""}>select client</MenuItem>
            )}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            margin="normal"
            label="Effective Date"
            name="effective_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.effective_date}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            margin="normal"
            label="Reason"
            name="reason"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.reason}
          />
        </Grid>
      </Grid>
      <Button
        type="submit"
        variant="contained"
        sx={{ mt: 3, ml: 1 }}
        color="success"
      >
        submit
      </Button>
    </Box>
  );
}

export default TransferEmp;
