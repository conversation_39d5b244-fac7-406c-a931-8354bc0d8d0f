<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('non_taxable_allowances', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('emp_basic_id');
            $table->foreign('emp_basic_id')->references('id')->on('emp_basics')->onDelete('cascade');
            $table->float('transport_allowance')->default(0)->nullable();
            $table->float('desert_allowance')->default(0)->nullable();
            $table->float('position_allowance')->default(0)->nullable();
            $table->float('mobile_allowance')->default(0)->nullable();
            $table->float('cleaning_allowance')->default(0)->nullable();
            $table->float('other_allowance')->default(0)->nullable();
            $table->tinyInteger('paid')->default(0)->nullable();
            $table->float('medical_allowance')->default(0)->nullable();
            $table->date('effective_date')->format('Y-m-d');
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('non_taxable_allowances');
    }
};
