import { createBrowserRouter } from 'react-router-dom';

import { lazy, Suspense } from 'react';

import GuestLayout from './layouts/GuestLayout';
import MainLayout from './layouts/MainLayout';
import Error from './pages/errors/Error';
const ProtectedRoute = lazy(() => import('./components/ProtectedRoute'));
const UnauthorizedPage = lazy(()=>import('./pages/global/UnauthorizedPage'));

const Login = lazy(() => import('./pages/auth/Login'));
const Register = lazy(() => import('./pages/Auth/Register'));

const Dashboard = lazy(() => import('./pages/dashboard/Dashboard'));
import AllNotifications from './pages/dashboard/notifications/AllNotifications';
import NotificationDetail from './pages/dashboard/notifications/NotificationDetail';
import NotificationDetailPayment from './pages/dashboard/notifications/NotificationDetailPayment';
import Permissions from './pages/dashboard/permissions/Permissions';

const Client = lazy(() => import('./pages/clients/Client'));
const ClientForm = lazy(() => import('./pages/clients/ClientForm'));
const ShowClient = lazy(() => import('./pages/clients/ShowClient'));
const ClientDeduct = lazy(()=> import('./pages/clients/ClientDeduct'));
const ContractFollowup = lazy(() => import('./pages/clients/ContractFollowup'));
const PaymentFollowup = lazy(() => import('./pages/clients/PaymentFollowup'));
const Project = lazy(() => import('./pages/clients/Project'));

const Assign = lazy(() => import('./pages/hr/assign/Assign'));
const Search = lazy(() => import('./pages/hr/em_update_component/Search')) ;
const UpdateComponent =lazy(() => import('./pages/hr/em_update_component/UpdateComponent'));
const HR = lazy(()=> import( './pages/hr/HR'));
const Position = lazy(()=> import('./pages/hr/Position'));
const RegisterEmployee = lazy(()=> import('./pages/hr/registration_component/RegisterEmployee'));
const Transfer = lazy(() =>import('./pages/hr/transfer_employee/Transfer'));
const Leave = lazy(()=> import( './pages/hr/leave/Leave'));
const LeaveForm = lazy(()=> import( './pages/hr/leave/LeaveForm'));
const LeaveSearch = lazy(()=> import( './pages/hr/leave/LeaveSearch'));
const LeaveSummary = lazy(()=> import( './pages/hr/leave/LeaveSummary'));
const LeaveSummaryLookup = lazy(()=> import( './pages/hr/leave/LeaveSummaryLookup'));
const LeaveTakenDetail = lazy(()=> import( './pages/hr/leave/LeaveTakenDetail'));
const LeaveTakenTwoYears = lazy(() => import('./pages/hr/leave/LeaveTakenTwoYears'));


const Finance = lazy(() => import( './pages/finance/Finance'));
const Payroll = lazy(() => import( './pages/finance/payroll/Payroll'));
const Absence = lazy(() => import( './pages/finance/absence/Absence'));
const AddDeductable = lazy(() => import( './pages/finance/add_deductable/AddDeductable'));
const Pcrate = lazy(() => import( './pages/finance/add_pc_rate/Pcrate'));
const AdditionalNonTaxable = lazy(() => import( './pages/finance/additional_nontaxable/AdditionalNonTaxable'));
const AdditionalPcrate = lazy(() => import( './pages/finance/additional_pc_rate/AdditionalPcrate'));
const Additional = lazy(() => import( './pages/finance/addtitonal/Additional'));
const DailyRate = lazy(() => import( './pages/finance/dailyRate/DailyRate'));
const Dsa = lazy(() => import( './pages/finance/dsa/Dsa'));
const EmployeeDeductable = lazy(() => import( './pages/finance/employee_deductable/EmployeeDeductable'));
const OverTime = lazy(() => import( './pages/finance/overtime/OverTime'));
const DraftPayroll = lazy(() => import( './pages/finance/draftpayroll/DraftPayroll'));
const CalculatePayroll = lazy(() => import( './pages/finance/payroll/CalculatePayroll'));
const PaymentSlip = lazy(() => import( './pages/finance/payment_slip/PaymentSlip'));
const PaymentSlipList = lazy(() => import( './pages/finance/payment_slip/PaymentSlipList'));
const SeverancePayment = lazy(() => import( './pages/finance/severance/SeverancePayment'));


const Settings = lazy(()=> import( './pages/settings/Settings'));
const TaxCenter = lazy(()=> import( './pages/settings/TaxCenter'));
const Terminate = lazy(()=> import( './pages/settings/Terminate'));
const Warehouse = lazy(() => import('./pages/settings/Warehouse'));
const Users = lazy(()=> import( './pages/users/Users'));
const ResetPassword = lazy(()=> import( './pages/settings/ResetPassword'));
const ChangePassword = lazy(()=> import( './pages/users/ChangePassword'));
const PcrateType = lazy(() => import('./pages/settings/PcrateType'));
const ActivityLogPage = lazy(() => import('./pages/settings/ActivityLogPage'));


const Report = lazy(() => import( './pages/reports/Report'));
const Operation = lazy(() => import( './pages/operation/Operation'));
const BankPayroll = lazy(() => import( './pages/reports/BankPayroll/BankPayroll'));
const BankPayrollListReport = lazy(() => import( './pages/reports/BankPayroll/BankPayrollListReport'));
const GeneralPayroll = lazy(() => import( './pages/reports/BankPayroll/GeneralPayroll'));
const GeneralPayrollList = lazy(() => import( './pages/reports/BankPayroll/GeneralPayrollList'));
const IncomeTax = lazy(() => import( './pages/reports/BankPayroll/IncomeTax'));
const IncomeTaxList = lazy(() => import( './pages/reports/BankPayroll/IncomeTaxList'));
const PensionTax = lazy(() => import( './pages/reports/BankPayroll/PensionTax'));
const PensionTaxList = lazy(() => import( './pages/reports/BankPayroll/PensionTaxList'));
const ProvidentTax = lazy(() => import( './pages/reports/BankPayroll/ProvidentTax'));
const ProvidentTaxList = lazy(() => import( './pages/reports/BankPayroll/ProvidentTaxList'));
const EmployeeReport = lazy(() => import( './pages/reports/EmployeeReport'));
const EmployeesList = lazy(() => import( './pages/reports/employees/EmployeesList'));
const EmployeesReport = lazy(() => import( './pages/reports/employees/EmployeesReport'));
const LeaveLiability = lazy(() => import( './pages/reports/leave/LeaveLiability'));
const LeaveReport = lazy(() => import( './pages/reports/LeaveReport'));
const PayrollReport = lazy(() => import( './pages/reports/PayrollReport'));
const PaidSeverance = lazy(() => import( './pages/reports/severance/PaidSeverance'));
const PaySeverance = lazy(() => import( './pages/reports/severance/PaySeverance'));
const SeveranceLiablity = lazy(() => import( './pages/reports/severance/SeveranceLiablity'));
const SeveranceReport = lazy(() => import( './pages/reports/SeveranceReport'));
const TerminationReportList = lazy(() => import( './pages/reports/termination/TerminationReportList'));
const TRTable = lazy(() => import( './pages/reports/termination/TRTable'));
const TerminationReport = lazy(() => import( './pages/reports/TerminationReport'));
const Category = lazy(() => import('./pages/store/Category'));
const Items = lazy(() => import('./pages/store/Items'));
const Stock = lazy(() => import('./pages/store/Stock'));
const Requests = lazy(() => import('./pages/store/Requests'));
const Store = lazy(() => import('./pages/store/Store'));
const Approval = lazy(() => import('./pages/store/Approval'));

const CoffeeExportPage = lazy(() => import('./pages/coffee/CoffeeExportPage'));
const CoffeeBatches = lazy(() => import('./pages/coffee/CoffeeBatches'));
const Farmers = lazy(() => import('./pages/coffee/Farmers'));
const ExportOrders = lazy(() => import('./pages/coffee/ExportOrders'));
const Shipments = lazy(() => import('./pages/coffee/Shipments'));
const Certificates = lazy(() => import('./pages/coffee/Certificates'));
const StockMovements = lazy(() => import('./pages/coffee/StockMovements'));


const router = createBrowserRouter([
  {
    path: '/login',
    element: <GuestLayout />,
    errorElement: <Error />,
    children: [
      {
        path: '/login',
        element: <Login />,
      },
    ],
  },

  {
    path: '/',
    element: <MainLayout />,
    children: [
      {
        index: true,
        path: 'dashboard',
        element: <Dashboard />,
      },
      {
        path: '/messages/detail/:id',
        element: <NotificationDetail />,

      },
      {
        path: '/messages/detail/payment/:id',
        element: <NotificationDetailPayment />,
      },
      {
        path: '/all_notifications',
        element: <AllNotifications />,
      },
      {
        path: '/permissions',
        element: <Permissions />,
      },
      {
        path: 'settings/users',
        element: (
          <ProtectedRoute
            childern={<Users />}
            roles={['admin']}
            path={['settings/users']}
          />
        ),
      },
      {
        path: 'settings/users/new',
        element: (
          <ProtectedRoute
            childern={<Register key="userCreate" />}
            roles={['admin']}
            path={['settings/users/new']}
          />
        ),
      },
      {
        path: '/activity',
        element: (
          <ProtectedRoute
            childern={<ActivityLogPage />}
            roles={['admin']}
            path={['/activity']}
          />
        ),
      },
      {
        path: 'settings/users/:id',
        element: (
          <ProtectedRoute
            childern={<Register key="userUpdate" />}
            roles={['admin']}
            path={['settings/users/:id']}
          />
        ),
      },
      {
        path: 'employees',
        element: (
          <ProtectedRoute
            childern={<HR />}
            roles={['admin', 'hr']}
            path={['employees']}
          />
        ),
      },
      {
        path: 'employees/edit',
        element: (
          <ProtectedRoute
            childern={<Search />}
            roles={['admin', 'hr']}
            path={['employees/edit']}
          />
        ),
      },
      {
        path: 'employees/:id',
        element: (
          <ProtectedRoute
            childern={<UpdateComponent />}
            roles={['admin', 'hr']}
            path={['employees/:id']}
          />
        ),
      },
      {
        path: 'employees/new',
        element: (
          <ProtectedRoute
            childern={<RegisterEmployee key="employeeCreate" />}
            roles={['admin', 'hr']}
            path={['employees/new']}
          />
        ),
      },
      {
        path: 'employees/position',
        element: (
          <ProtectedRoute
            childern={<Position />}
            roles={['admin', 'hr']}
            path={['employees/position']}
          />
        ),
      },
      {
        path: '/employees/assign',
        element: (
          <ProtectedRoute
            childern={<Assign />}
            roles={['admin', 'hr']}
            path={['/employees/assign']}
          />
        ),
      },
      {
        path: '/employees/transfer',
        element: (
          <ProtectedRoute
            childern={<Transfer key="createTransfer" />}
            roles={['admin', 'hr']}
            path={['/employees/transfer']}
          />
        ),
      },
      {
        path: '/employees/transfer/:id',
        element: (
          <ProtectedRoute
            childern={<Transfer key="updateTransfer" />}
            roles={['admin', 'hr']}
            path={['/employees/transfer/:id']}
          />
        ),
      },
      {
        path: '/employees/leave',
        element: (
          <ProtectedRoute
            childern={<LeaveSearch />}
            roles={['admin', 'hr']}
            path={['/employees/leave']}
          />
        ),
      },
      {
        path: '/employees/leaves',
        element: (
          <ProtectedRoute
            childern={<Leave />}
            roles={['admin', 'hr']}
            path={['/employees/leaves']}
          />
        ),
      },
      {
        path: '/employees/add-leaves',
        element: (
          <ProtectedRoute
            childern={<LeaveForm key="createLeave" />}
            roles={['admin', 'hr']}
            path={['/employees/add-leaves']}
          />
        ),
      },
      {
        path: '/employees/add-leaves/:id',
        element: (
          <ProtectedRoute
            childern={<LeaveForm key="updateLeave" />}
            roles={['admin', 'hr']}
            path={['/employees/add-leaves/:id']}
          />
        ),
      },
      {
        path: '/employees/leave-detail',
        element: (
          <ProtectedRoute
            childern={<LeaveTakenDetail />}
            roles={['admin', 'hr']}
            path={['/employees/leave-detail']}
          />
        ),
      },
      {
        path: '/employees/leave-detail-two',
        element: (
          <ProtectedRoute
            childern={<LeaveTakenTwoYears />}
            roles={['admin', 'hr']}
            path={['/employees/leave-detail-two']}
          />
        ),
      },
      {
        path: '/employees/leave-summary',
        element: (
          <ProtectedRoute
            childern={<LeaveSummary />}
            roles={['admin', 'hr']}
            path={['/employees/leave-summary']}
          />
        ),
      },
      {
        path: '/employees/leave-summary-lookup',
        element: (
          <ProtectedRoute
            childern={<LeaveSummaryLookup />}
            roles={['admin', 'hr']}
            path={['/employees/leave-summary-lookup']}
          />
        ),
      },
      {
        path: 'clients',
        element: (
          <ProtectedRoute
            childern={<Project />}
            roles={['admin', 'hr', 'finance']}
            path={['clients']}
          />
        ),
      },
      {
        path: 'clients/list',
        element: (
          <ProtectedRoute
            childern={<Client />}
            roles={['admin', 'hr', 'finance']}
            path={['clients/list']}
          />
        ),
      },
      {
        path: 'clients/deduct',
        element: (
          <ProtectedRoute
            childern={<ClientDeduct />}
            roles={['admin', 'hr']}
            path={['clients/deduct']}
          />
        ),
      },
      {
        path: 'clients/payment-follow-up',
        element: (
          <ProtectedRoute
            childern={<PaymentFollowup />}
            roles={['admin', 'hr']}
            path={['clients/payment-follow-up']}
          />
        ),
      },
      {
        path: '/clients/contract-follow-up',
        element: (
          <ProtectedRoute
            childern={<ContractFollowup />}
            roles={['admin', 'hr']}
            path={['/clients/contract-follow-up']}
          />
        ),
      },
      {
        path: 'clients/new',
        element: (
          <ProtectedRoute
            childern={<ClientForm key="clinetCreate" />}
            roles={['admin', 'hr']}
            path={['clients/new']}
          />
        ),
      },
      {
        path: 'clients/:id',
        element: (
          <ProtectedRoute
            childern={<ClientForm key="ClientUpdate" />}
            roles={['admin', 'hr']}
            path={['clients/:id']}
          />
        ),
      },
      {
        path: '/clients/show/:id',
        element: (
          <ProtectedRoute
            childern={<ShowClient />}
            roles={['admin', 'hr']}
            path={['/clients/show/:id']}
          />
        ),
      },

      {
        path: 'finance',
        element: (
          <ProtectedRoute
            childern={<Finance />}
            roles={['admin', 'finance']}
            path={['finance']}
          />
        ),
      },
      {
        path: 'finance/draft',
        element: (
          <ProtectedRoute
            childern={<DraftPayroll />}
            roles={['admin', 'finance']}
            path={['finance/draft']}
          />
        ),
      },
      {
        path: 'finance/overtime',
        element: (
          <ProtectedRoute
            childern={<OverTime />}
            roles={['admin', 'finance']}
            path={['finance/overtime']}
          />
        ),
      },
      {
        path: 'finance/absence',
        element: (
          <ProtectedRoute
            childern={<Absence />}
            roles={['admin', 'finance']}
            path={['finance/absence']}
          />
        ),
      },
      {
        path: 'finance/additional',
        element: (
          <ProtectedRoute
            childern={<Additional />}
            roles={['admin', 'finance']}
            path={['finance/additional']}
          />
        ),
      },
      {
        path: 'finance/pcrate',
        element: (
          <ProtectedRoute
            childern={<Pcrate />}
            roles={['admin', 'finance']}
            path={['finance/pcrate']}
          />
        ),
      },
      {
        path: 'finance/additionalpcrate',
        element: (
          <ProtectedRoute
            childern={<AdditionalPcrate />}
            roles={['admin', 'finance']}
            path={['finance/additionalpcrate']}
          />
        ),
      },
      {
        path: 'finance/dsa',
        element: (
          <ProtectedRoute
            childern={<Dsa />}
            roles={['admin', 'finance']}
            path={['finance/dsa']}
          />
        ),
      },
      {
        path: 'finance/nontaxable',
        element: (
          <ProtectedRoute
            childern={<AdditionalNonTaxable />}
            roles={['admin', 'finance']}
            path={['finance/nontaxable']}
          />
        ),
      },
      {
        path: 'finance/setdeductable',
        element: (
          <ProtectedRoute
            childern={<EmployeeDeductable />}
            roles={['admin', 'finance']}
            path={['finance/setdeductable']}
          />
        ),
      },
      {
        path: '/finance/sevrenace',
        element: (
          <ProtectedRoute
            childern={<SeverancePayment />}
            roles={['admin', 'finance']}
            path={['/finance/sevrenace']}
          />
        ),
      },
      {
        path: 'finance/add-deductable',
        element: (
          <ProtectedRoute
            childern={<AddDeductable />}
            roles={['admin', 'finance']}
            path={['finance/add-deductable']}
          />
        ),
      },
      {
        path: 'finance/dailyrate',
        element: (
          <ProtectedRoute
            childern={<DailyRate />}
            roles={['admin', 'finance']}
            path={['finance/dailyrate']}
          />
        ),
      },
      {
        path: '/finance/payroll',
        element: (
          <ProtectedRoute
            childern={<Payroll />}
            roles={['admin', 'finance']}
            path={['/finance/payroll']}
          />
        ),
      },
      {
        path: '/finance/slip',
        element: (
          <ProtectedRoute
            childern={<PaymentSlip />}
            roles={['admin', 'finance']}
            path={['/finance/slip']}
          />
        ),
      },
      {
        path: '/finance/slip/list',
        element: (
          <ProtectedRoute
            childern={<PaymentSlipList />}
            roles={['admin', 'finance']}
            path={['/finance/slip/list']}
          />
        ),
      },
      {
        path: '/finance/payroll/calc',
        element: (
          <ProtectedRoute
            childern={<CalculatePayroll />}
            roles={['admin', 'finance']}
            path={['/finance/payroll/calc']}
          />
        ),
      },
      {
        path: 'operations',
        element: (
          <ProtectedRoute
            childern={<Operation />}
            roles={['admin', 'finance', 'hr']}
            path={['operations']}
          />
        ),
      },
      {
        path: 'reports',
        element: (
          <ProtectedRoute
            childern={<Report />}
            roles={['admin', 'finance', 'hr']}
            path={['reports']}
          />
        ),
      },
      {
        path: '/reports/employee',
        element: <EmployeeReport />,
      },
      {
        path: '/reports/employee/employees',
        element: <EmployeesReport />,
      },
      {
        path: '/reports/employee/employees_list',
        element: <EmployeesList />,
      },
      {
        path: '/reports/payroll',
        element: <PayrollReport />,
      },
      {
        path: '/reports/payroll/bankpayrolist',
        element: <BankPayroll />,
      },
      {
        path: '/reports/payroll/General_payroll',
        element: <GeneralPayroll />,
      },
      {
        path: '/reports/General_payroll_list/report',
        element: <GeneralPayrollList />,
      },
      {
        path: '/reports/payroll/income_tax_list',
        element: <IncomeTax />,
      },
      {
        path: '/reports/payroll/pension_tax_list',
        element: <PensionTax />,
      },
      {
        path: '/reports/payroll/provident_tax_list',
        element: <ProvidentTax />,
      },
      {
        path: '/reports/payroll/provident_tax/report',
        element: <ProvidentTaxList />,
      },
      {
        path: '/reports/payroll/pension_tax/report',
        element: <PensionTaxList />,
      },
      {
        path: '/reports/payroll/income_tax/report',
        element: <IncomeTaxList />,
      },

      {
        path: '/reports/payroll/bankpayrollist/report',
        element: <BankPayrollListReport />,
      },
      {
        path: '/reports/severance',
        element: <SeveranceReport />,
      },
      {
        path: 'pay-severance',
        element: <PaySeverance />,
      },
      {
        path: 'paid-severance',
        element: <PaidSeverance />,
      },
      {
        path: 'severance-liablity',
        element: <SeveranceLiablity />,
      },
      {
        path: '/reports/leave',
        element: <LeaveReport />,
      },
      {
        path: 'leave-liablity',
        element: <LeaveLiability />,
      },
      {
        path: '/reports/TerminationReport',
        element: <TerminationReport />,
      },
      {
        path: '/reports/termination/report',
        element: <TerminationReportList />,
      },
      {
        path: '/reports/termination/reports',
        element: <TRTable />,
      },
      {
        path: 'settings/category',
        element: (
          <ProtectedRoute
            childern={<Category />}
            roles={['admin', 'storekeeper']}
            path={['settings/category']}
          />
        ),
      },
      {
        path: 'store/items',
        element: (
          <ProtectedRoute
            childern={<Items />}
            roles={['admin', 'storekeeper']}
            path={['store/items']}
          />
        ),
      },
      {
        path: 'store/stock',
        element: (
          <ProtectedRoute
            childern={<Stock />}
            roles={['admin', 'storekeeper']}
            path={['store/stock']}
          />
        ),
      },
      {
        path: 'store/requests',
        element: (
          <ProtectedRoute
            childern={<Requests />}
            roles={['admin', 'storekeeper']}
            path={['store/requests']}
          />
        ),
      },
      {
        path: 'store',
        element: (
          <ProtectedRoute
            childern={<Store />}
            roles={['admin', 'storekeeper']}
            path={['store']}
          />
        ),
      },
      {
        path: 'store/approval/:id',
        element: (
          <ProtectedRoute
            childern={<Approval />}
            roles={['admin', 'storekeeper']}
            path={['store/approval/:id']}
          />
        ),
      },
      {
        path: 'settings',
        element: (
          <ProtectedRoute
            childern={<Settings />}
            roles={['admin']}
            path={['settings']}
          />
        ),
      },
      {
        path: 'settings/tax-center',
        element: (
          <ProtectedRoute
            childern={<TaxCenter />}
            roles={['admin']}
            path={['settings/tax-center']}
          />
        ),
      },
      {
        path: 'settings/warehouse',
        element: (
          <ProtectedRoute
            childern={<Warehouse />}
            roles={['admin']}
            path={['settings/warehouse']}
          />
        ),
      },
      {
        path: 'settings/terminate',
        element: (
          <ProtectedRoute
            childern={<Terminate />}
            roles={['admin']}
            path={['settings/terminate']}
          />
        ),
      },
      {
        path: 'settings/pcrate-type',
        element: (
          <ProtectedRoute
            childern={<PcrateType />}
            roles={['admin']}
            path={['settings/pcrate_type']}
          />
        ),
      },
      {
        path: '/settings/users/reset-password',
        element: (
          <ProtectedRoute
            childern={<ResetPassword />}
            roles={['admin']}
            path={['/settings/users/reset-password']}
          />
        ),
      },
      {
        path: '/user/change-password',
        element: <ChangePassword />,
      },
      {
        path: 'unauthorizedpage',
        element: <UnauthorizedPage />,
      },
      {
        path: 'coffeepage',
        element: (
          <ProtectedRoute
            childern={<CoffeeExportPage />}
            roles={['admin', 'storekeeper']}
            path={['coffeepage']}
          />
        ),
      },
      {
        path: 'coffeepage/batches',
        element: (
          <ProtectedRoute
            childern={<CoffeeBatches />}
            roles={['admin', 'storekeeper']}
            path={['coffeepage/batches']}
          />
        ),
      },
      {
        path: 'coffeepage/farmers',
        element: (
          <ProtectedRoute
            childern={<Farmers />}
            roles={['admin', 'storekeeper']}
            path={['coffeepage/farmers']}
          />
        ),
      },
      {
        path: 'coffeepage/export-orders',
        element: (
          <ProtectedRoute
            childern={<ExportOrders />}
            roles={['admin', 'storekeeper']}
            path={['coffeepage/export-orders']}
          />
        ),
      },
      {
        path: 'coffeepage/shipments',
        element: (
          <ProtectedRoute
            childern={<Shipments />}
            roles={['admin', 'storekeeper']}
            path={['coffeepage/shipments']}
          />
        ),
      },
      {
        path: 'coffeepage/certificates',
        element: (
          <ProtectedRoute
            childern={<Certificates />}
            roles={['admin', 'storekeeper']}
            path={['coffeepage/certificates']}
          />
        ),
      },
      {
        path: 'coffeepage/stock-movements',
        element: (
          <ProtectedRoute
            childern={<StockMovements />}
            roles={['admin', 'storekeeper']}
            path={['coffeepage/stock-movements']}
          />
        ),
      },
    ],
  },
]);
export default router;
