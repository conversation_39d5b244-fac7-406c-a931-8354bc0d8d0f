<?php

namespace App\Http\Controllers\Api\finance;

use App\Http\Controllers\Controller;
use App\Http\Resources\OverTimeResource;
use App\Models\finance\OverTime;
use Illuminate\Http\Request;
use Symfony\Component\Console\Input\Input;

class OverTimeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index()
    {
        return OverTimeResource::collection(OverTime::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \App\Http\Resources\OverTimeResource
     */
    public function store(Request $request)
    {
        $data = OverTime::create([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'client_id' => $request->input('client_id'),
            'start_date' => $request->input('start_date'),
            'end_date' => $request->input('end_date'),
            'rate_type' => $request->input('rate_type'),
            'day_type' => $request->input('day_type'),
            'minutes' => $request->input('minutes'),
            'hours' => $request->input('hours'),
            'overtime_remark' => $request->input('overtime_remark'),
            'user_id' => $request->input('user_id'),
        ]);

        return new OverTimeResource($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\finance\OverTime  $overTime
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function show($id)
    {
        $overTimeData = OverTime::where('emp_basic_id', $id)->where('paid', 0)->get();
        $overTimeCount = $overTimeData->count(); // Gets the count of overtime records

        return response()->json([
            'overTimeCount' => $overTimeCount,
            'overTimeData' => OverTimeResource::collection($overTimeData)
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\finance\OverTime  $overTime
     * @return \App\Http\Resources\OverTimeResource
     */
    public function update(Request $request, $id)
    {

        $overTime = OverTime::find($id);
        $overTime->update([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'client_id' => $request->input('client_id'),
            'start_date' => $request->input('start_date'),
            'end_date' => $request->input('end_date'),
            'rate_type' => $request->input('rate_type'),
            'minutes' => $request->input('minutes'),
            'day_type' => $request->input('day_type'),
            'hours' => $request->input('hours'),
            'overtime_remark' => $request->input('overtime_remark'),
            'user_id' => $request->input('user_id'),
        ]);

        return new OverTimeResource($overTime);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\finance\OverTime  $overTime
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $overTime = OverTime::find($id);
        $overTime->delete();
        return response('', 204);
    }
}