import {
  Box,
  Grid,
} from "@mui/material";
import React from "react";

import Header from "../../components/Header";
import LinkCard from "../../components/LinkCard";

function HR() {
  return (
    <Box mx="40px">
      <Header title="HR Page" subtitle="Welcome to the hr page" />
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6}>
          <LinkCard
            title="Register Employee"
            subtitle="a place to register new employee"
            to="/employees/new"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <LinkCard
            title="Update Employee"
            subtitle="a place to update registered employye information"
            to="/employees/edit"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <LinkCard
            title="Transfer Employee"
            subtitle="a place to transfer an employee in to diffrent clinet "
            to="/employees/transfer"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <LinkCard
            title="Assign Employee"
            subtitle="a place to Assign new  registerd Employee"
            to="/employees/assign"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <LinkCard
            title="Add Position"
            subtitle="a place to Register new Positions"
            to="/employees/position"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <LinkCard
            title="Employee Leave"
            subtitle="a place to calculate Employee Leave"
            to="/employees/leave"
          />
        </Grid>
      </Grid>
    </Box>
  );
}

export default HR;
