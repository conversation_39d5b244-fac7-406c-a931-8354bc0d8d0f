import React from "react";
import {
  <PERSON><PERSON>,
  Typo<PERSON>,
  useTheme,
  Box,
  CircularProgress,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { useStateContext } from "../../../context/ContextProvider";
import { tokens } from "../../../utils/theme";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import Header from "../../../components/Header";
import api from "../../../api/config/axiosConfig";

function AdditonalPcRateList({ additionalPcrate, selectedRow }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const {
    data: addpcrate,
    isLoading,
    isFetched,
  } = useQuery(
    ["addpcrate", additionalPcrate.emp_basic_id],
    () =>
      api
        .get(`addpcrate/${additionalPcrate.emp_basic_id}`)
        .then(({ data }) => data),
    {
      enabled: !!additionalPcrate.emp_basic_id,
      staleTime: 60000,
    }
  );
  const columns = [
    {
      field: "id",
      headerName: "ID",
      flex: 0.2,
    },
    {
      field: "from_date",
      headerName: "StartDate",
      flex: 0.5,
    },
    {
      field: "to_date",
      headerName: "To Date",
      flex: 0.5,
    },
    {
      field: "pc_type",
      headerName: "pc Type",
      flex: 0.8,
    },
    {
      field: "no_pc",
      headerName: "Number of Pc",
      flex: 0.5,
    },
    {
      field: "pc_rate_amount",
      headerName: "Pc Rate Amount",
      flex: 0.5,
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 0.7,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          selectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "delete",
      headerName: "Delete",
      flex: 0.8,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deletPcRate = useMutation((id) => api.delete(`addpcrate/${id}`), {
          onSuccess: () => {
            setNotification("additional Pc rate record deleted successfully");
            queryClient.invalidateQueries({ queryKey: "addpcrate" });
          },
        });
        const deleteOverTime = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              "Are you sure you want to delete the selected pcrate record?"
            )
          ) {
            return;
          }
          deletPcRate.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deleteOverTime}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];
  return (
    <>
      <Header title="List of Additional Pc rate Records" heading="h5" />
      {isLoading && <CircularProgress />}
      {isFetched && (
        <DataGrid columns={columns} autoHeight hideFooter rows={addpcrate} />
      )}
    </>
  );
}

export default AdditonalPcRateList;
