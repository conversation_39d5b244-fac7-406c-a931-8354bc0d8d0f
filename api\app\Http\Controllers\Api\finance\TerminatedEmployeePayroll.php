<?php

namespace App\Http\Controllers\Api\finance;

use App\Http\Controllers\Controller;
use App\Http\Resources\PayRollResource;
use App\Models\EmpBasic;
use App\Models\Client;
use App\Models\finance\DailyRate;
use App\Models\finance\EmployeeDeductable;
use App\Models\finance\Payroll;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\helper\PayrollHelper;
use App\Models\settings\Termination;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class TerminatedEmployeePayroll extends Controller
{

    public $empId;
    public $clientId;
    public $guardSalary;
    public $overTimeGuardSalary;
    public $leaderSalary;
    public $guardLsalary;
    public $leaderLsalary;
    public $guardTransport;
    public $rateType;
    public $totalWorkingHour;
    public $noOfDays;
    public $totalNontaxableDsaSalary;
    public $totaltaxableDsaSalary;
    public $nonTaxTransportAllowance;
    public $nonTaxDesertAllowance;
    public $nonTaxPositionAllowance;
    public $nonTaxMobileAllowance;
    public $nonTaxCleaningAllowance;
    public $nonTaxOtherAllowance;
    public $laborUnion;
    public $totalWorkingDuration;
    public $basicSalary;
    public $transportDeductionNewEmployee;
    public $transportAllowanceDeduction;
    public $otherTransportAllowance;
    public $currency;

    public function __construct()
    {
        $this->empId = 0;
        $this->clientId = 0;
        $this->guardSalary = 0;
        $this->overTimeGuardSalary = 0;
        $this->leaderSalary = 0;
        $this->leaderLsalary = 0;
        $this->guardLsalary = 0;
        $this->guardTransport = 0;
        $this->rateType = '';
        $this->totalWorkingHour = 0;
        $this->noOfDays = 0;
        $this->totalNontaxableDsaSalary = 0;
        $this->totaltaxableDsaSalary = 0;
        $this->nonTaxTransportAllowance = 0;
        $this->nonTaxDesertAllowance = 0;
        $this->nonTaxPositionAllowance = 0;
        $this->nonTaxMobileAllowance = 0;
        $this->nonTaxCleaningAllowance = 0;
        $this->nonTaxOtherAllowance = 0;
        $this->totalWorkingDuration = 0;
        $this->basicSalary = 0;
        $this->transportDeductionNewEmployee = 0;
        $this->transportAllowanceDeduction = 0;
        $this->otherTransportAllowance = 0;
        $this->currency = 0;
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index()
    {
        return PayRollResource::collection(Payroll::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \App\Http\Resources\PayRollResource
     */
    public function store(Request $request)
    {
        $payrollData = $request->all();
        $helper = new PayrollHelper;
        foreach ($payrollData as $data) {
            $payroll = new Payroll();
            $loanPaymentEachMonth = EmployeeDeductable::expectedPaymentEachMonth($data['emp_basic_id']);
            $payroll->emp_basic_id = $data['emp_basic_id'];
            $payroll->client_id = $data['client_id'];
            $payroll->total_work_duration = $data['total_work_duration'];
            $payroll->basic_salary = $data['basic_salary'];
            // $payroll->all_holiday = $data['all_holiday'];
            $payroll->all_holiday = 0;
            //overTime
            $payroll->over_time = $data['over_time'];
            //guard transport allowance
            $payroll->transport_allowance = $data['transport_allowance'];
            $payroll->absence = $data['absence'];
            //loan, associations and unions
            $payroll->medical_cost = $data['medical_cost'];
            $payroll->loan = $data['loan'];
            $payroll->other_loan = $data['other_loan'];
            $payroll->labor_union = $data['labor_union'];
            $payroll->social_supprot = $data['social_support'];
            $payroll->creadit_association = $data['creadit_association'];
            $payroll->sport_association = $data['sport_association'];

            $payroll->additional = $data['total_additional_hours'];
            $payroll->provident_emp = $data['providentEmp'];
            $payroll->provident_sns = $data['providentComp'];
            $payroll->income_tax = $data['incomeTax'];
            $payroll->gross_salary = $data['sum_taxable_income'];
            $payroll->total_deduction = $data['total_deduction_summry'];
            $payroll->net_pay = $data['net_payment'];
            $payroll->other_transport_allowance = $data['taxable_other_allowance'];
            $payroll->transport_allowance_deduction = $data['trans_ded'];
            $payroll->lw_out_pay_ded = $data['lw_out_pay_ded'];
            $payroll->add_ded_reason = $data['total_add_reason'];
            $payroll->oth_loan_reason = '';
            $payroll->pay_date = $data['pay_date'];
            $payroll->pension_emp = $data['pensionEmp'];
            $payroll->pension_comp = $data['pensionComp'];
            //pcrate
            $payroll->pcrate_salary = $data['total_pcrate_salary'];
            $payroll->addpcreate_salary = $data['total_add_pcrate_salary'];
            //non taxable
            $payroll->nontax_transport_allowance = $data['nontax_transport_allowance'] ?? 0;
            $payroll->nontax_desert_allowance = $data['nontax_desert_allowance'] ?? 0;
            $payroll->nontax_position_allowance = $data['nontax_position_allowance'] ?? 0;
            $payroll->nontax_other_allowance = $data['nontax_other_allowance'] ?? 0;
            $payroll->nontax_mobile_allowance = $data['nontax_mobile_allowance'] ?? 0;
            $payroll->nontax_cleaning_allowance = $data['nontax_cleaning_allowance'] ?? 0;
            $payroll->nontax_dsa_allowance = $data['nontax_dsa_allowance'] ?? 0;
            $payroll->nontax_medical_allowance = 0;
            $payroll->additional_nontax_allowance = $data['additional_nontaxable_allowance'] ?? 0;
            //taxable
            $payroll->housing_allowance = $data['taxable_housing_allowance'] ?? 0;
            $payroll->position_allowance = $data['taxable_position_allowance'] ?? 0;
            $payroll->tax_dsa_allowance = $data['total_taxable_dsa_salary'] ?? 0;

            $payroll->exchange_rate = $data['exchange_rate'];
            $payroll->emp_client_additional = $data['emp_client_additional'] ?? 0;
            $payroll->user_id = $data['user_id'];

            $payroll->save();
            $helper->updateOvertime($data['emp_basic_id']);
            $helper->updateAdditional($data['emp_basic_id']);
            $helper->updatePcrate($data['emp_basic_id']);
            $helper->updateAddPcrate($data['emp_basic_id']);
            $helper->updateDsa($data['emp_basic_id']);
            $helper->updateAbsence($data['emp_basic_id']);
            $helper->updateEmployeeDeductable($data['emp_basic_id']);
            $helper->updateAdditionalDeductable($data['emp_basic_id']);
            $helper->updateAdditionalNonTaxAllowance($data['emp_basic_id']);
            $helper->updateDailyrate($data['emp_basic_id']);
        }
        return new PayRollResource($payroll);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }


    public function dailyRate($id, $fromDate, $toDate)
    {
        $daily = DailyRate::where('emp_basic_id', $id)->where('paid', 0)->whereBetween('from_date', [$fromDate, $toDate])->get();
        foreach ($daily as $list) {
            $this->noOfDays = $list->no_days;
        }
        $this->totalWorkingDuration = $this->noOfDays;
    }


    public function calculatePayroll(Request $request)
    {
        $this->empId = $request->input('emp_id');
        //$this->clientId = $request->input('client_id');

        $empPayroll = collect([]);
        $pHelper = new PayrollHelper();

        $payrollDate = $pHelper->setPayrollDate();

        $provident = $pHelper->provident();
        $pension = $pHelper->pension();

        $emps = EmpBasic::whereIn('id', $this->empId)->get();

        //$payrollDate = $pHelper->setPayrollDate();

        $dateInHumans = Carbon::parse($payrollDate['startDate']);

        $year = $dateInHumans->year;

        $month = $dateInHumans->format('F');

        foreach ($emps as $emp) {

            $termination = Termination::where('emp_id', $emp->id)->first();
            $this->clientId = $termination->client_id;

            $payrollData = $pHelper->termPayroll($emp->id);

            $dateInHumans = Carbon::parse($payrollDate['startDate']);

            $year = $dateInHumans->year;

            $month = $dateInHumans->format('F');

            $actualDateLength = $pHelper->dateLength($emp->start_date);
            $dateLength = $pHelper->actualDateLength();

            $this->rateType = $emp->rate_type;

            if ($emp->start_date == $payrollDate['endDate'] || $emp->start_date < $payrollDate['startDate']) {
                $this->overTimeGuardSalary = $emp->basic_salary;
                if ($emp->position == 'Regular Guard') {
                    $this->guardSalary = $emp->basic_salary;
                    $this->overTimeGuardSalary = $emp->basic_salary;
                } else {
                    $this->leaderSalary = $emp->basic_salary;
                }
            } else {
                if ($emp->position == 'Regular Guard') {
                    $this->guardSalary = ($emp->basic_salary / $dateLength) * $actualDateLength;
                    // $this->overTimeGuardSalary = $emp->basic_salary;
                } else {
                    $this->leaderSalary = ($emp->basic_salary / $dateLength) * $actualDateLength;
                }
            }

            $client = Client::where('id', $this->clientId)->first();

            if ($emp->start_date == $payrollDate['startDate'] || $emp->start_date < $payrollDate['startDate']) {
                if ($emp->position == 'Regular Guard') {
                    if ($this->guardSalary == null || $this->guardSalary == 0) {
                        $this->guardSalary = $client->guard_salary;
                        $this->guardLsalary = $client->guard_lsalary;
                    }
                } else if ($emp->position == 'Shift Leader') {
                    if ($this->leaderSalary == null || $this->leaderSalary == 0) {
                        $this->leaderSalary = $client->leader_salary;
                        $this->leaderLsalary = $client->leader_lsalary;
                    }
                } else if ($emp->position == 'Relief Guard') {
                    if ($this->leaderSalary == null || $this->leaderSalary == 0) {
                        $this->leaderSalary = $client->leader_salary;
                        $this->leaderLsalary = $client->leader_lsalary;
                    }
                }
            } else {
                if ($emp->position == 'Regular Guard') {
                    if ($this->guardSalary == null || $this->guardSalary == 0) {
                        $this->guardSalary = ($client->guard_salary / $dateLength) * $actualDateLength;
                        $this->guardLsalary = ($client->guard_lsalary / $dateLength) * $actualDateLength;
                    }
                } else if ($emp->position == 'Shift Leader') {

                    if ($this->leaderSalary == null || $this->leaderSalary == 0) {
                        $this->leaderSalary = ($client->leader_salary / $dateLength) * $actualDateLength;
                        $this->leaderLsalary = ($client->leader_lsalary / $dateLength) * $actualDateLength;
                    }
                } else if ($emp->position == 'Relief Guard') {
                    if ($this->leaderSalary == null || $this->leaderSalary == 0) {
                        $this->leaderSalary = ($client->leader_salary / $dateLength) * $actualDateLength;
                        $this->leaderLsalary = ($client->leader_lsalary / $dateLength) * $actualDateLength;
                    }
                }
            }

            if ($client->guard_transport == null || $client->guard_transport == 0) {
                $this->guardTransport = $client->guard_transport;
            }

            if ($client->rate_type == null || $client->rate_type == 0) {
                $this->rateType = $client->rate_type;
                $this->totalWorkingHour = $client->total_working_hours;
            }

            if ($client->guard_salary == null) {
                $this->guardSalary = $this->leaderSalary;
            }

            if ($client->client_additional > 0) {
                $emp_client_additional = ($client->client_additional > 0)
                    ? ($emp->basic_salary * $client->client_additional) / 100
                    : 0;
            } else {
                $emp_client_additional = 0;
            }

            //call get holiday but not relevant for now 
            $taxedAllowance = $pHelper->taxable($emp->id, $emp->start_date);
            $this->guardTransport = $taxedAllowance['guardTransport'];

            if ($this->rateType == 'Daily') {
                $this->dailyRate($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            }
            //could of call metaholiday but its irrelevant for now
            //hodayrate
            $overtimes = $pHelper->overTime($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $pcRate = $pHelper->pcrate($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $dsa = $pHelper->dsa($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $nontaxable = $pHelper->nonTaxable($emp->id, $emp->start_date);
            $additionalNonTaxableAllowance = $pHelper->additionalNonTaxAllowance($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $addPcrate = $pHelper->addpcrate($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $additionals = $pHelper->additional($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            //client additonal could of called here 
            $additonalDeductable = $pHelper->additionalDeductable($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $absence = $pHelper->absenceList($emp->id, $payrollDate['startDate'], $payrollDate['endDate'], $client->total_working_hours, $this->guardSalary);
            $deduct = $pHelper->deduct($emp->id);
            $clientDeduct = $pHelper->clientDeduct($emp->id, $client->id);
            $medicalShare = $pHelper->medicalShare();

            $empDeductable = $pHelper->empDeductable($emp->id);
            $loanPaymentEachMonth = EmployeeDeductable::expectedPaymentEachMonth($emp->id);

            $medicalLoan = $loan = $otherLoan = $laborLoan = $costSharing = 0;

            foreach ($loanPaymentEachMonth as $key => $value) {
                if ($key == 'Medical Cost') {
                    $medicalLoan = $value['each_payment'];
                } else if ($key == 'Company Loan') {
                    $loan = $value['each_payment'];
                } else if ($key == 'Other Loan') {
                    $otherLoan = $value['each_payment'];
                } else if ($key == 'Labor Loan') {
                    $laborLoan = $value['each_payment'];
                } else if ($key == 'CostSharing Loan') {
                    $costSharing = $value['each_payment'];
                }
            }

            $totalLeaveTakenduration = $pHelper->totalLeaveDuration($emp->id, $client->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $totalLeaveTakenWithoutPay = $pHelper->totalLeaveTakenWithoutPayTaken($emp->id, $client->id, $payrollDate['startDate'], $payrollDate['endDate']);

            $totalLeaveTransDeduction = round(($taxedAllowance['guardTransport'] / $actualDateLength) * $totalLeaveTakenduration['daysduration'], 2);
            $this->otherTransportAllowance = $taxedAllowance['taxMobileAllowance'] + $taxedAllowance['taxCleaningAllowance'];

            if ($emp->position == 'Regular Guard') {
                if ($this->rateType == 'Month') {
                    $lWoutpayDed = round((($this->guardSalary / $actualDateLength) * $totalLeaveTakenWithoutPay), 2);
                    $leaveWithoutPayMess = $lWoutpayDed . 'Leave with out pay';
                } else {
                    $lWoutpayDed = round(($totalLeaveTakenWithoutPay * $this->guardSalary), 2);
                    $leaveWithoutPayMess = $lWoutpayDed . 'Leave with out pay';
                }
            } else {
                if ($this->rateType == 'Month') {
                    $lWoutpayDed = round((($this->leaderSalary / $actualDateLength) * $totalLeaveTakenWithoutPay), 2);
                    $leaveWithoutPayMess = $lWoutpayDed . 'Leave with out pay';
                } else {
                    $lWoutpayDed = round(($totalLeaveTakenWithoutPay * $this->leaderSalary), 2);
                    $leaveWithoutPayMess = $lWoutpayDed . 'Leave with out pay';
                }
            }

            if ($emp->position == 'Regular Guard') {
                if ($this->rateType == 'Hour' || $this->rateType == 'Daily') {
                    $this->basicSalary = ($this->guardSalary * $this->totalWorkingDuration - $lWoutpayDed) + $this->totalWorkingDuration;
                } else {
                    $this->basicSalary = ($this->guardSalary - $lWoutpayDed) + $pcRate['totalpcrateSalary'];
                }
            } else {
                if ($this->rateType == 'Hour' || $this->rateType == 'Daily') {
                    $this->basicSalary = ($this->guardSalary * $this->totalWorkingDuration - $lWoutpayDed) +  $pcRate['totalpcrateSalary'];
                } else {
                    $this->basicSalary = ($this->guardSalary - $lWoutpayDed) +  $pcRate['totalpcrateSalary'];
                }
            }

            if ($taxedAllowance['guardTransport'] == null || $taxedAllowance['guardTransport'] == 0) {
                if ($client->guard_transport != 0) {
                    $this->guardTransport = $client->guard_transport / 100 * $this->basicSalary;
                }
            }

            if ($emp->start_date != $payrollDate['startDate']) {
                $noDays = $pHelper->nodays($emp->start_date, $payrollDate['startDate']);
                $this->transportDeductionNewEmployee = $noDays * ($this->guardTransport / $actualDateLength);
            }

            $this->transportAllowanceDeduction = $this->transportDeductionNewEmployee;

            if ($emp->position == 'Regular Guard') {

                if ($this->currency == 1) {
                    $this->guardSalary = $this->guardSalary * $pHelper->exchangeRate()['exchange'];
                }
            } else {
                if ($this->currency == 1) {
                    $this->guardSalary = $this->leaderSalary * $pHelper->exchangeRate()['exchange'];
                }
                $this->guardLsalary = $this->leaderLsalary;
            }

            if ($this->currency == 1) {
                $this->nonTaxTransportAllowance = $nontaxable['nonTaxTransportAllowance'] * $pHelper->exchangeRate()['exchange'];
                $this->nonTaxDesertAllowance = $nontaxable['nonTaxDesertAllowance'] * $pHelper->exchangeRate()['exchange'];
                $this->nonTaxMobileAllowance = $nontaxable['nonTaxMobileAllowance'] * $pHelper->exchangeRate()['exchange'];
                $this->nonTaxCleaningAllowance = $nontaxable['nonTaxCleaningAllowance'] * $pHelper->exchangeRate()['exchange'];
                $this->nonTaxOtherAllowance = $nontaxable['nonTaxOtherAllowance'] * $pHelper->exchangeRate()['exchange'];
                $this->nonTaxPositionAllowance = $nontaxable['nonTaxPositionAllowance'] * $pHelper->exchangeRate()['exchange'];
                $this->guardTransport = $this->guardTransport * $pHelper->exchangeRate()['exchange'];
                $this->totaltaxableDsaSalary = $dsa['totaltaxableDsaSalary'] * $pHelper->exchangeRate()['exchange'];
                $this->totalNontaxableDsaSalary = $dsa['totalNontaxableDsaSalary'] * $pHelper->exchangeRate()['exchange'];
            } else {
                $this->nonTaxTransportAllowance = $nontaxable['nonTaxTransportAllowance'];
                $this->nonTaxDesertAllowance = $nontaxable['nonTaxDesertAllowance'];
                $this->nonTaxMobileAllowance = $nontaxable['nonTaxMobileAllowance'];
                $this->nonTaxCleaningAllowance = $nontaxable['nonTaxCleaningAllowance'];
                $this->nonTaxOtherAllowance = $nontaxable['nonTaxOtherAllowance'];
                $this->nonTaxPositionAllowance = $nontaxable['nonTaxPositionAllowance'];
                $this->totaltaxableDsaSalary = $dsa['totaltaxableDsaSalary'];
                $this->totalNontaxableDsaSalary = $dsa['totalNontaxableDsaSalary'];
            }

            if ($clientDeduct['laborUnionType']) {
                $this->laborUnion = ($clientDeduct['laborUnion'] * $this->guardSalary / 100);
            } else {
                $this->laborUnion = $clientDeduct['laborUnion'];
            }

            // if ($this->rateType == 'Hour') {
            // $this->overTimeGuardSalary =   $this->overTimeGuardSalary * $overtimes['totalOvertimeHours'];

            $this->overTimeGuardSalary =   ($this->overTimeGuardSalary * $overtimes['totalOvertimeHours'] / ($client->total_working_hours));
            // } 
            // else {
            // if ($actualDateLength != 0) {
            //     $this->overTimeGuardSalary = ($this->guardSalary / $actualDateLength) * $dateLength;
            // } else {
            //     $this->overTimeGuardSalary = $this->guardSalary * $overtimes['totalOvertimeHours'] / $this->totalWorkingDuration;
            // }
            // }

            $taxable_transport_allowance = $this->guardTransport - $this->transportDeductionNewEmployee;
            $taxable_transport_allowance = $this->guardTransport;

            if ($emp->pension) {
                $pensionEmp = $pension['pensionEmp'] * $this->basicSalary / 100;
                $pensionComp = $pension['pensionEdomias'] * $this->basicSalary / 100;
            } else {
                $pensionEmp = 0;
                $pensionComp = 0;
            }

            if ($emp->provident) {
                $providentEmp = $provident['providentEmp'] * $this->basicSalary / 100;
                $providentComp = $provident['providentEdomias'] * $this->basicSalary / 100;
            } else {
                $providentEmp = 0;
                $providentComp = 0;
            }

            $this->otherTransportAllowance = $taxedAllowance['taxMobileAllowance'] + $taxedAllowance['taxCleaningAllowance'];

            $sumofIncomTax = $this->basicSalary + $this->overTimeGuardSalary + $additionals['totalAdditionalHours'] + $this->guardTransport + $addPcrate['totalAddpcrateSalary'] + $this->totaltaxableDsaSalary + $this->otherTransportAllowance + $taxedAllowance['taxHousingAllowance'] + $taxedAllowance['taxPositionAllowance'] + $emp_client_additional - $additonalDeductable['totalAddDeduction']
                - $this->transportDeductionNewEmployee
                - ($absence['totalAbsences'] * $absence['absenceGuardSalary']);


            $totalAdditionalDeductable =  $deduct['socialFund'];
            $totalDeduction = $providentEmp + $pensionEmp + $pHelper->incomeTax($sumofIncomTax) + $totalAdditionalDeductable + ($medicalLoan + $otherLoan + $loan + $laborLoan + $costSharing);

            $totalNonTaxAllowance = $this->totalNontaxableDsaSalary + $additionalNonTaxableAllowance['additionalNonTaxableAllowance'] + $this->nonTaxTransportAllowance + $this->nonTaxDesertAllowance + $this->nonTaxMobileAllowance + $this->nonTaxPositionAllowance + $this->nonTaxCleaningAllowance + $this->nonTaxOtherAllowance;
            $netPay = $sumofIncomTax + $totalNonTaxAllowance - $totalDeduction;

            $payrollData = [
                'year' => $year,
                'month' => $month,
                'emp_basic_id' => $emp->id,
                'client_id' => $client->id,
                'company_id' => $emp->company_id,
                'company_name' => $client->company_name,
                'first_name' => $emp->first_name,
                'middle_name' => $emp->middle_name,
                'last_name' => $emp->last_name,
                'date_length' => $dateLength,
                'emp_start_date' => $emp->start_date,
                'basic_salary' => round($this->guardSalary, 2),
                'holiday_all' => '',
                'rate_type' => $this->rateType,
                'location_salary' => round($this->guardLsalary, 2),
                // gauard transport
                'transport_allowance' => round($this->guardTransport, 2),
                'bank_account_number' => $pHelper->bankInformation($emp->id)['account_number'],
                'currency' => $this->currency,
                'exchange_rate' => $pHelper->exchangeRate()['exchange'],
                'pension' => $emp->pension ? 'yes' : 'no',
                'provident' => $emp->provident ? 'yes' : 'no',
                'total_work_duration' => $this->totalWorkingDuration,
                //abscence and overtime
                'total_absence_duration' => $absence['totalAbsences'],
                'total_overtime_hours' => $overtimes['totalOvertimeHours'],

                'total_additional_hours' => $additionals['totalAdditionalHours'],
                //pcrate
                'total_no_pc' => $pcRate['totalNoPc'],
                'pc_rate_type' => $pcRate['pcrateType'],
                'pcrate' => $pcRate['pcrate'],
                'total_pcrate_salary' => $pcRate['totalpcrateSalary'],
                'total_add_no_pc' => $addPcrate['totalAddNopc'],
                'add_pc_rate_type' => $addPcrate['addPcrateType'],
                'add_pcrate' => $addPcrate['addPcrate'],
                'total_add_pcrate_salary' => $addPcrate['totalAddpcrateSalary'],
                //non taxable
                'additional_nontaxable_allowance' => $additionalNonTaxableAllowance['additionalNonTaxableAllowance'],
                'nontax_transport_allowance' => round($this->nonTaxTransportAllowance, 2),
                'nontax_desert_allowance' => round($this->nonTaxDesertAllowance, 2),
                'nontax_mobile_allowance' => round($this->nonTaxMobileAllowance, 2),
                'nontax_cleaning_allowance' => round($this->nonTaxCleaningAllowance, 2),
                'nontax_other_allowance' => round($this->nonTaxOtherAllowance, 2),
                'nontax_position_allowance' => round($this->nonTaxPositionAllowance, 2),
                //dsa
                'nontax_dsa_allowance' => round($this->totalNontaxableDsaSalary, 2),
                'total_taxable_dsa_salary' => round($this->totaltaxableDsaSalary, 2),
                //loans , associations and unions
                'medical_cost' => $medicalLoan,
                'loan' => $loan,
                'other_loan_ded' => $otherLoan,
                'other_loan' => $otherLoan,
                'sport_association' => $clientDeduct['sportAssociation'],
                'creadit_association' => $clientDeduct['creaditAssociation'],
                'labor_union' => $this->laborUnion,
                'labor_loan' => $laborLoan,
                'costsharing_loan' => $costSharing,
                'social_support' => $deduct['socialFund'],
                //deductable
                'trans_ded' => round($this->transportAllowanceDeduction, 2),
                'total_add_Deduction' => $additonalDeductable['totalAddDeduction'],
                'total_add_reason' => $additonalDeductable['addDeductReason'] ? $additonalDeductable['addDeductReason'] : 'none',
                'last_basic_salary' => $this->basicSalary,
                'over_time' => round($this->overTimeGuardSalary, 2),
                // taxable
                'taxable_transport_allowance' => round($taxable_transport_allowance, 2),
                'taxable_housing_allowance' => round($taxedAllowance['taxHousingAllowance'], 2),
                'taxable_position_allowance' => round($taxedAllowance['taxPositionAllowance'], 2),
                'taxable_other_allowance' => round($this->otherTransportAllowance, 2),
                //pension and provident amount
                'pensionEmp' => $pensionEmp,
                'pensionComp' => $pensionComp,
                'providentEmp' => $providentEmp,
                'providentComp' => $providentComp,
                'lw_out_pay_ded' => $lWoutpayDed,
                //income tax, total deduction and net payment
                'incomeTax' => round($pHelper->incomeTax($sumofIncomTax), 2),
                'employee_medical' => '',
                'absence_deduction_summry' => round($absence['totalAbsences'] * $absence['absenceGuardSalary'], 2),
                'absence' => $absence['totalAbsences'],
                'total_additional_ded_summry' => $totalAdditionalDeductable,
                'sum_taxable_income' => round($sumofIncomTax, 2),
                'total_deduction_summry' => round($totalDeduction, 2),
                'non_taxable_income_summry' => round($totalNonTaxAllowance, 2),
                'net_payment' => round($netPay, 2),
                'emp_client_additional' => $emp_client_additional,
            ];

            $empPayroll->push($payrollData);
        }
        return response($empPayroll);
    }
}
