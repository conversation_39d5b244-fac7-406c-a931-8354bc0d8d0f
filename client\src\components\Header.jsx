import { Box, Typography, useTheme } from "@mui/material";
import React from "react";
import { tokens } from "../utils/theme";
function Header({ title, subtitle, heading }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  return (
    <Box mb="10px">
      <Typography
        variant={heading ?? "h2"}
        color={colors.grey[100]}
        fontWeight="bold"
        sx={{ mb: "5px" }}
      >
        {title}
      </Typography>
      <Typography variant="h5" color={colors.grey["400"]}>
        {subtitle}
      </Typography>
    </Box>
  );
}

export default Header;
