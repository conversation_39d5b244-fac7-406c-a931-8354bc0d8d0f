import { AddOutlined } from "@mui/icons-material";
import {
  Box,
  Button,
  Typography,
  IconButton,
  useTheme,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import React from "react";
import { Link } from "react-router-dom";
import api from "../../api/config/axiosConfig";
import Header from "../../components/Header";
import { useStateContext } from "../../context/ContextProvider";
import { tokens } from "../../utils/theme";
import _LinearProgress from "../../components/_LinearProgress";
import _CircularProgress from "../../components/_CircularProgress";
import { useClientData } from "../../api/userApi/clinetHook";

function Client() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { data: client, isLoading } = useClientData();
  const { setNotification, errors, setErrors } = useStateContext();

  const columns = [
    {
      field: "id",
      headerName: "ID",
    },
    {
      field: "company_name",
      headerName: "Company Name",
      flex: 2,
    },
    {
      field: "company_code",
      headerName: "Company Code",
      flex: 1,
    },
    {
      field: "city",
      headerName: "City",
      flex: 1,
    },
    {
      field: "sub_city",
      headerName: "Sub City",
      flex: 1,
    },
    {
      field: "street_address1",
      headerName: "street_address1",
      flex: 1,
    },
    {
      field: "street_address2",
      headerName: "street_address2",
      flex: 1,
    },
    {
      field: "kebele",
      headerName: "Kebele",
      flex: 1,
    },
    {
      field: "house_no",
      headerName: "House No",
      flex: 1,
    },
    // {
    //   field: "full_name",
    //   headerName: "Contact Name",
    //   valueGetter: (params) =>
    //     `${params.row("company_name") || " "} ${
    //       params.row("last_name") || " "
    //     }`,
    //   flex: 1,
    // },
    {
      field: "tel",
      headerName: "contact phone",
      flex: 1,
    },
    {
      field: "total_working_hours",
      headerName: "Working Hours",
      flex: 1,
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 1,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Link
              to={`/clients/${params.row.id}`}
              style={{ textDecoration: "none" }}
            >
              <Button onClick={onRowClicked}>
                <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                  Edit
                </Typography>
              </Button>
            </Link>
          </Box>
        );
      },
    },
    // {
    //   field: "show",
    //   headerName: "Show",
    //   flex: 1,
    //   filterable: false,
    //   hide: false,
    //   sortable: false,
    //   renderCell: (params) => {
    //     const onRowClicked = (e) => {
    //       e.stopPropagation();
    //     };
    //     return (
    //       <Box
    //         width="60%"
    //         m="0 auto"
    //         p="30x"
    //         display="flex"
    //         justifyContent="center"
    //         backgroundColor={colors.greenAccent[700]}
    //         borderRadius="4px"
    //       >
    //         <Link
    //           to={`/clients/show/${params.row.id}`}
    //           style={{ textDecoration: "none" }}
    //         >
    //           <Button onClick={onRowClicked}>
    //             <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
    //               Show
    //             </Typography>
    //           </Button>
    //         </Link>
    //       </Box>
    //     );
    //   },
    // },
    {
      field: "delete",
      headerName: "Delete",
      flex: 1,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deletePosition = (e) => {
          e.stopPropagation();
          if (!window.confirm("Are you sure you want to delete the clinet?")) {
            return;
          }
          // delete api in here
          api
            .delete(`client/${params.id}`)
            .then(() => {
              setNotification("Client Is Deleted successfully");
            })
            .catch((err) => {});
        };

        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deletePosition}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];

  return (
    <Box m="10px">
      <Header title="Client Page" subtitle="welcome to the client page" />
      <Box display="flex" justifyContent="center" alignItems="center">
        <Link to="/clients/new">
          <IconButton
            sx={{
              boxShadow: "2px 2px 4px rgba(0,0,0,0.25)",
              background: colors.primary[400],
            }}
          >
            <AddOutlined color={colors.grey[100]} />
          </IconButton>
        </Link>
      </Box>
      <Box
        m="20px 0 0 0"
        height="75vh"
        sx={{
          "& .MuiDataGrid-root": {
            border: "none",
          },
          "& .MuiDataGrid-cell": {
            borderBottom: "none",
          },
          "& .name-column--cell": {
            color: colors.greenAccent[300],
          },
          "& .MuiDataGrid-columnHeaders": {
            backgroundColor: colors.blueAccent[700],
            borderBottom: "none",
          },
          "& .MuiDataGrid-virtualScroller": {
            backgroundColor: colors.primary[400],
          },
          "& .MuiDataGrid-footerContainer": {
            borderTop: "none",
            backgroundColor: colors.blueAccent[700],
          },
          "& .MuiCheckBox-root": {
            color: `${colors.greenAccent[200]}!important`,
          },
          "& .MuiDataGrid-toolbarContainer .MuiButton-text": {
            color: `${colors.grey[100]}!important`,
          },
        }}
      >
        {isLoading ? (
          <_CircularProgress />
        ) : (
          <DataGrid rows={client} columns={columns} />
        )}
      </Box>
    </Box>
  );
}

export default Client;
