<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CollectedPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'contract_id',
        'client_id',
        'collector_name',
        'amount',
        'collection_date',
        'collection_type',
        'comment',
        'user_id',
    ];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function contract()
    {
        return $this->belongsTo(PaymentFollowUp::class);
    }
}
