import React from "react";
import { useState } from "react";
import { Box, Button, Grid } from "@mui/material";
import { useNavigate } from "react-router-dom";
import FilterTax from "../../../components/FilterTax";
const ProvidentTax = () => {
    const [firstDate, setFirstDate] = useState("");
    const [lastDate, setLastDate] = useState("");
    const [center, setCenter] = useState("");
    const navigate = useNavigate();
    const passFromDate = (fromDate) => {
      setFirstDate(fromDate);
    };
    const passToDate = (toDate) => {
      setLastDate(toDate);
    };
    const passCenter = (center) => {
      setCenter(center);
    };
    const handleButtonClick = () => {
      if (firstDate || lastDate|| center) {
        navigate("/reports/payroll/provident_tax/report", {
          state: {
            firstDate: firstDate,
            lastDate: lastDate,
            taxCenter: center,
            status: 0,
          },
        });
      } else {
        navigate("/reports/payroll/provident_tax/report", {
          state: {
            status: 1,
          },
        });
      }
    };
  return (
    <Grid margin="10px" container spacing={2}>
    <Grid xs={12} sm={10}>
    
      <FilterTax 
        passCenter={passCenter}
        passFromDate={passFromDate}
        passToDate={passToDate}
      />
    </Grid>
    <Grid item xs={12} sm={2}>
      <Button
        sx={{
          marginLeft: "auto",
          width: "80%",
          height: "80%",
        }}
        variant="contained"
        color="success"
        onClick={handleButtonClick}
      >
        filter
      </Button>
    </Grid>
  </Grid>
  )
}

export default ProvidentTax