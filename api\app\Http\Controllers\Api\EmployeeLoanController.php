<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreEmployeeLoanRequest;
use App\Http\Requests\UpdateEmployeeLoanRequest;
use App\Models\EmployeeLoan;
use Illuminate\Http\Request;

class EmployeeLoanController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // return EmployeeLoan::all();
        return response(EmployeeLoan::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // $data=$request->validated();
        $data = $request->all();
        $loan = EmployeeLoan::create([
            'emp_basic_id' => $data['emp_basic_id'],
            'loan_amount' => $data['loan_amount'],
            'remaining_amount' => $data['loan_amount'],
            'loan_period' => $data['loan_period'],
            'remaining_repayment_period' => $data['loan_period'],
            'loan_type' => $data['loan_type'],
            'repayment_status' => 0,
            'loan_date' => $data['loan_date'],
            'reason' => $data['reason'],
            'user_id' => $request->user_id,
        ]);

        return response(['loan' => $loan], 201);
    }


    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        return response(EmployeeLoan::findOrFail($id));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // $data=$request->validated();
        $data = $request->all();

        $empLoan = EmployeeLoan::findOrFail($id);
        $empLoan->update([
            'emp_basic_id' => $data['emp_basic_id'],
            'loan_type' => $data['loan_type'],
            'loan_amount' => $data['loan_amount'],
            'loan_period' => $data['loan_period'],
            'loan_date' => $data['loan_date'],
            'paid_amount' => $data['paid_amount'],
            'loan_paid' => $data['loan_paid'],
            'reason' => $data['reason'],
            'user_id' => $request->user_id,
        ]);

        return response($empLoan);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(EmployeeLoan $empLoan)
    {
        $empLoan->delete();
        return response('', 204);
    }
}
