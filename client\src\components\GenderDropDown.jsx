import { TextField, MenuItem, Box } from "@mui/material";
import React, { useState } from "react";
const gender = [
  {
    label: "Male",
    value: "male",
  },
  {
    label: "Female",
    value: "female",
  },
];
const GenderDropDown = ({ onSelectedGender }) => {
  const [selectedGender, setSelectedGender] = useState("");
  const handleCenterChange = (e) => {
    const centerValue = e.target.value;
    setSelectedGender(centerValue);
    onSelectedGender(centerValue);
  };
  return (
    <Box>
      <TextField
        select
        name="gender"
        value={selectedGender}
        onChange={handleCenterChange}
        fullWidth
        label="Select Gender"
        // variant='standard'
        margin="normal"
        type="text"
        InputLabelProps={{
          color: "success",
        }}
      >
        {gender.map((g) => (
          <MenuItem key={g.value} value={g.value}>
            {g.label}
          </MenuItem>
        ))}
      </TextField>
    </Box>
  );
};

export default GenderDropDown;
