import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { getShipments } from '../../api/coffee';
import { DataGrid } from '@mui/x-data-grid';
import { tokens } from '../../utils/theme';
import { useTheme } from '@mui/material/styles';

const Shipments = () => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['shipments'],
    queryFn: getShipments,
  });

  const columns = [
    { field: 'id', headerName: 'ID', flex: 0.5 },
    { field: 'shipment_number', headerName: 'Shipment Number', flex: 1 },
    {
      field: 'export_order',
      headerName: 'Order Number',
      flex: 1,
      valueGetter: (params) => params.row.export_order?.order_number,
    },
    { field: 'container_number', headerName: 'Container Number', flex: 1 },
    { field: 'status', headerName: 'Status', flex: 1 },
  ];

  if (isLoading) {
    return <Typography>Loading...</Typography>;
  }

  if (isError) {
    return <Typography>Error: {error.message}</Typography>;
  }

  return (
    <Box m="20px">
      <Box display="flex" justifyContent="space-between" alignItems="center">
        <Typography variant="h2" component="h1" gutterBottom>
          Shipments
        </Typography>
        <Button
          variant="contained"
          sx={{ backgroundColor: colors.greenAccent[600] }}
        >
          Add New Shipment
        </Button>
      </Box>
      <Box
        m="40px 0 0 0"
        height="75vh"
        sx={{
          '& .MuiDataGrid-root': {
            border: 'none',
          },
          '& .MuiDataGrid-cell': {
            borderBottom: 'none',
          },
          '& .name-column--cell': {
            color: colors.greenAccent[300],
          },
          '& .MuiDataGrid-columnHeaders': {
            backgroundColor: colors.blueAccent[700],
            borderBottom: 'none',
          },
          '& .MuiDataGrid-virtualScroller': {
            backgroundColor: colors.primary[400],
          },
          '& .MuiDataGrid-footerContainer': {
            borderTop: 'none',
            backgroundColor: colors.blueAccent[700],
          },
        }}
      >
        <DataGrid
          rows={data?.data || []}
          columns={columns}
          pageSize={10}
          rowsPerPageOptions={[10]}
          checkboxSelection
        />
      </Box>
    </Box>
  );
};

export default Shipments;
