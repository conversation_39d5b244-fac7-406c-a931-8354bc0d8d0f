import React from 'react'
import { Box, Paper, Typography, useTheme } from "@mui/material";
import { commonGridStyles } from "../../../helper/usegridStyle";
import { DataGrid } from "@mui/x-data-grid";
import Header from "../../../components/Header";
import { useLeaveLiablity, useSeveranceLiablity } from "../../../api/userApi/clinetHook";
import { tokens } from "../../../utils/theme";
import CustomToolBar from '../../../helper/CustomToolBar';
const LeaveLiability = () => {
  const theme = useTheme();
  const colors=tokens(theme.palette.mode)
  const gridStyles = commonGridStyles(theme);
  const {data:liablity,isLoading}=useLeaveLiablity()
  const columns = [
    {
      field: "id",
      headerName: "ID",
    },
    {
      field: "full_name",
      headerName: "FullName",
      // width: "200",
      flex: 1.5,
    },
    {
      field: "salary",
      headerName: "BasicSalary",
      flex: 1,
    },
    {
      field: "employeed_date",
      headerName: "employeedDate",
      flex: 1,
    },
    {
      field: "entitled_leave",
      headerName: "entitledLeave",
      flex: 1,
      cellClassName: "row",
    },
    {
      field: "actual_leave_left",
      headerName: "Available Leave",
      flex: 1,
      cellClassName: "custom-ded",
    },
    {
      field: "expired_leave",
      headerName: "Expired Leave",
      flex: 1,
      cellClassName: "custom-pay",
    },
    {
      field: "total_liablity",
      headerName: "TotalLiablity(available+expired)",
      flex: 1,
      cellClassName: "custom-pay",
    },
    {
      field: "leave_liable_in_money",
      headerName: "TotalLiablity In Money",
      flex: 1,
      cellClassName: "custom-pay",
    },
  ];
  if (isLoading) {
    return <>loading....</>;
  }
  return (
    <div style={{ margin: "10px" }}>
    <Header title="Leave Liablity Report" />
    <Box
      sx={{
        ...gridStyles,
      }}
    >
      {liablity && (
        <DataGrid
          sx={{
            width: "100%",
            marginBottom: 5,
            "& .MuiDataGrid-columnHeaderTitle": {
              whiteSpace: "normal",
              wordWrap: "break-word",
              overflowWrap: "",
              lineHeight: "normal",
            },
            "& .MuiDataGrid-columnHeader": {
              height: "unset !important",
              paddingX: "20px",
            },
          }}
          columns={columns}
          rows={liablity.leaves}
          autoHeight
          pagination
          pageSize={10}
          components={{
            Toolbar:CustomToolBar
          }}
          rowsPerPageOptions={[10, 20, 30]}
        />
      )}
      {liablity && (
        <Paper
          sx={{
            padding: "1rem",
            color: colors.grey[100],
            background: colors.primary[400],
            display:'flex',
            flexDirection:'column'
          }}
        >
          <Typography variant="button">Sum of Total Leave Liablity:<span style={{
              color:colors.redAccent[500],
              fontSize:'16px'
          }}>{` ${liablity.totals_leave} `}</span>days</Typography>
          <Typography variant="button">Sum of Total Leave Liablity In Money:<span style={{
              color:colors.redAccent[500],
              fontSize:'16px'
          }}>{` ${liablity.totals_money} `}</span> birr</Typography>
        </Paper>
      )}
    </Box>
  </div>
);
}

export default LeaveLiability