<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('export_order_batches', function (Blueprint $table) {
            $table->id();
            $table->foreignId('export_order_id')->constrained('export_orders');
            $table->foreignId('coffee_batch_id')->constrained('coffee_batches');
            $table->decimal('allocated_weight_kg', 10, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('export_order_batches');
    }
};
