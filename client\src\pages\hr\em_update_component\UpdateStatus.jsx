import { Box, Grid, TextField, Button, MenuItem } from "@mui/material";
import React, { useEffect } from "react";

import { useState } from "react";
import api from "../../../api/config/axiosConfig";

import { useStateContext } from "../../../context/ContextProvider";

function UpdateStatus({id}) {
  const { errors, setErrors, setNotification } = useStateContext();
  const [isemp, setisEmp] = useState(false);
  const [formData, setFormData] = useState({
    emp_basic_id: "",
    certificate_medical: "",
    marital_status: "",
    driving_license: "",
    criminal_record: "",
    living_certificate: "",
    labor_union: "",
    credit_association: "",
    sport_association: "",
    social_support: "",
    finggure_print: "",
    holiday: "",
    eng_lang: "",
  });
  if(id){
    useEffect(()=>{
      api
        .get(`emp_basics/${id}?all=true`, { params: { all: 'status' } })
        .then(({ data }) => {
          if (data.data.emp_status) {
            setFormData(data.data.emp_status);
            setisEmp(true);
          }
        });
    },[id])
  }
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  const boolValues = [
    {
      lable: "Yes",
      value:1,
    },
    {
      lable: "No",
      value: 0,
    },
  ];
  const  englishSkills=[
    {
        lable:"good",
        value:'good',
    },
    {
        lable:"very good",
        value:'verygood',
    },
    {
        lable:"excellent",
        value:'excellent',
    },
  ]
  const handleSubmit = (e) => {
    e.preventDefault();
    if(isemp){
      api.put(`emp_status/${formData.id}`,formData).then(()=>{
        setNotification('updated successfully');
      }).catch((err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      });
    }else{
      const data=new FormData(e.currentTarget)
      const payload={
        emp_basic_id:id,
        certificate_medical:data.get('certificate_medical'),
        marital_status:data.get('marital_status'),
        driving_license:data.get('driving_license'),
        criminal_record:data.get('criminal_record'),
        living_certificate:data.get('living_certificate'),
        labor_union:data.get('labor_union'),
        credit_association:data.get('credit_association'),
        sport_association:data.get('sport_association'),
        social_support:data.get('social_support'),
        finggure_print:data.get('finggure_print'),
        holiday:data.get('holiday'),
        eng_lang:data.get('eng_lang')
      }
      api.post("emp_status",payload).then(()=>{
        setNotification('created successfully')
      }).catch((err) => {
      if (err.response && err.response.status === 422) {
        if (err.response.data.errors) {
          setErrors(err.response.data.errors);
        }
      }
    });
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.certificate_medical == ""}
            margin="normal"
            label="Certificate_Medical"
            name="certificate_medical"
            fullWidth
            variant="standard"
            type="number"
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.certificate_medical?formData.certificate_medical:'1'}
            helperText={errors.certificate_medical ? errors.certificate_medical[0] : null}
          >
            {boolValues.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.marital_status == ""}
            margin="normal"
            label="Marital_Status"
            name="marital_status"
            fullWidth
            variant="standard"
            type="number"
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.marital_status?formData.marital_status:0}
            helperText={errors.marital_status ? errors.marital_status[0] : null}
          >
            {boolValues.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.driving_license == ""}
            margin="normal"
            label="Driving_License"
            name="driving_license"
            fullWidth
            variant="standard"
            type="number"
            defaultValue={boolValues[0][1]}
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.driving_license?formData.driving_license:0}
            helperText={errors.driving_license ? errors.driving_license[0] : null}
          >
            {boolValues.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.criminal_record == ""}
            margin="normal"
            label="Criminal_Record"
            name="criminal_record"
            fullWidth
            variant="standard"
            type="number"
            defaultValue={boolValues[0][1]}
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.criminal_record?formData.criminal_record:0}
            helperText={errors.criminal_record ? errors.criminal_record[0] : null}
          >
            {boolValues.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.living_certificate == ""}
            margin="normal"
            label="Living_Certificate"
            name="living_certificate"
            fullWidth
            variant="standard"
            type="number"
            defaultValue={boolValues[0][1]}
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.living_certificate?formData.living_certificate:1}
            helperText={errors.living_certificate ? errors.living_certificate[0] : null}
          >
            {boolValues.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.labor_union == ""}
            margin="normal"
            label="Labor_Union"
            name="labor_union"
            fullWidth
            variant="standard"
            type="number"
            defaultValue={boolValues[0][1]}
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.labor_union?formData.labor_union:0}
            helperText={errors.labor_union ? errors.labor_union[0] : null}
          >
            {boolValues.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.credit_association == ""}
            margin="normal"
            label="Credit_Association"
            name="credit_association"
            fullWidth
            variant="standard"
            type="number"
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.credit_association?formData.credit_association:0}
            helperText={errors.credit_association ? errors.credit_association[0] : null}
          >
            {boolValues.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.sport_association == ""}
            margin="normal"
            label="Sport_Association"
            name="sport_association"
            fullWidth
            variant="standard"
            type="number"
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.sport_association?formData.sport_association:1}
            helperText={errors.sport_association ? errors.sport_association[0] : null}
          >
            {boolValues.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.social_support == ""}
            margin="normal"
            label="Social_Support"
            name="social_support"
            fullWidth
            variant="standard"
            type="number"
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.social_support?formData.social_support:1}
            helperText={errors.social_support ? errors.social_support[0] : null}
          >
            {boolValues.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.finggure_print == ""}
            margin="normal"
            label="Finger_Print"
            name="finggure_print"
            fullWidth
            variant="standard"
            type="number"
            defaultValue={boolValues[0][1]}
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.finggure_print?formData.finggure_print:1}
            helperText={errors.finggure_print ? errors.finggure_print[0] : null}
          >
            {boolValues.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.holiday == ""}
            margin="normal"
            label="Holiday"
            name="holiday"
            fullWidth
            variant="standard"
            type="number"
            defaultValue={boolValues[0][1]}
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.holiday?formData.holiday:0}
            helperText={errors.holiday ? errors.holiday[0] : null}
          >
            {boolValues.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.eng_lang == ""}
            margin="normal"
            label="English Skill"
            name="eng_lang"
            fullWidth
            variant="standard"
            type="text"
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.eng_lang?formData.eng_lang:'good'}
            helperText={errors.eng_lang ? errors.eng_lang[0] : null}
          >
            {englishSkills.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
          {isemp ? <div>update</div> : <div>create</div>}
        </Button>
      </Box>
    </Box>
  );
}

export default UpdateStatus;
