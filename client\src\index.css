@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600;700&display=swap");

html,
body,
#root,
.app,
.content {
  flex-grow: 1;
  
  overflow-y: auto;
  position: relative; /* Add this for positioning the loading overlay */
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8); /* Semi-transparent background */
  z-index: 9999; /* Ensure it's on top */
}
/* .content {
  min-height: "100vh";
  background-image: url("/src/assets/edomias.svg");
  background-repeat: no-repeat;
  background-size: "cover";
  background-position: center;
} */
.app {
  display: flex;
  position: relative;
}

::-webkit-scrollbar {
  width: 10px;
}
/* Track */
::-webkit-scrollbar-track {
  background: #e0e0e0;
}
/* handle */
::-webkit-scrollbar-thumb {
  background: #888;
}
/* handle on Hover */
::-webkit-scrollbar-track:hover {
  background-color: #555;
}

.MuiOutlinedInput-notchedOutline {
  border-color: #53af5b !important;
}

.sidebar {
  position: absolute;
  height: 100%;
}

.custom-cell {
  color: green;
  font-weight: bold;
}
.custom-pay {
  color: red;
  font-weight: bold;
}
.custom-pay-header {
  color: red;
  font-weight: bold;
}
.custom-ded{
  color: orange;
  font-weight: bold;
}
.custom-tax{
  color: #53af5b;
  font-weight: bold;
}
.custom-non-tax{
  color: blue;
  font-weight: bold;
}


.row{
  color: #53af5b;
}
