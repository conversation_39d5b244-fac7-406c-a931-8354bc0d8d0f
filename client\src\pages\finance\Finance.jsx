import { Box, Grid } from "@mui/material";
import React from "react";
import Header from "../../components/Header";
import LinkCard from "../../components/LinkCard";

function Finance() {
  return (
    <Box ml="10px">
      <Header
        title="Finance Page"
        subtitle="all finance related oprations are here "
      />
      <Grid container spacing={2} maxWidth="95%" ml="20px">
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="calculate payroll"
            subtitle="calculate employees payroll monthly"
            to="/finance/payroll"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="Draft Payroll"
            subtitle="a palce to cross check employee payroll "
            to="/finance/draft"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="Payment slip"
            subtitle="employee payment slip"
            to="/finance/slip"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="add over time"
            subtitle="add employee over time"
            to="/finance/overtime"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="add abscence "
            subtitle="add emplyee abscence"
            to="/finance/absence"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="add additional"
            subtitle="add employee additional "
            to="/finance/additional"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="add deductable"
            subtitle="add employee deductable"
            to="/finance/add-deductable"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="add pc rate"
            subtitle="add employee pc rate"
            to="/finance/pcrate"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="add dsa"
            subtitle="add employee dsa"
            to="/finance/dsa"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="add daily rate"
            subtitle="add employee daily rate"
            to="/finance/dailyrate"
          />
        </Grid>
        {/* <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="add additional pc rate"
            subtitle="add additional pcrate"
            to="/finance/additionalpcrate"
          />
        </Grid> */}
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="add non taxable "
            subtitle="add additional pcrate"
            to="/finance/nontaxable"
          />
        </Grid>
        {/* <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="Set deductable "
            subtitle="set deductable"
            to="/finance/setdeductable"
          />
        </Grid> */}
        <Grid item xs={12} sm={6} md={4}>
          <LinkCard
            title="Severance Payment"
            subtitle="Pay severance for terminated employee"
            to="/finance/sevrenace"
          />
        </Grid>
      </Grid>
    </Box>
  );
}

export default Finance;
