import React from "react";
import { useStateContext } from "../../../../context/ContextProvider";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import DeleteRenderCell from "../../../../components/DeleteRenderCell";
import EditRenderCell from "../../../../components/EditRenderCell";
import Header from "../../../../components/Header";
import { CircularProgress } from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import api from "../../../../api/config/axiosConfig";

const RecommandationList = ({ id, selectedRow }) => {
  const { setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const {
    data: award,
    isLoading,
    isFetched,
  } = useQuery(
    ["recommandation", id],
    () =>
      api
        .get(`emp_basics/${id}`, { params: { all: "award" } })
        .then(({ data }) => {
          if (data.data.award) {
            return data.data.award;
          }
        }),
    {
      enabled: !!id,
      staleTime: 600000,
      refetchOnWindowFocus: false,
    }
  );
  const deleteRow = useMutation((id) => api.delete(`award/${id}`), {
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: "recommandation" });
      setNotification("Recommandation  record is deleted Successfully");
    },
  });
  const handleDelete = (id) => {
    if (
      !window.confirm("Are you sure you want to delete the selected  record?")
    ) {
      return;
    }
    deleteRow.mutate(id);
  };
  const columns = [
    {
      field: "id",
      headerName: "Id",
    },
    {
      field: "award_type",
      headerName: "awardType",
    },
    {
      field: "award_reason",
      headerName: "awardReason",
    },
    {
      field: "ref_no",
      headerName: "RefNo",
    },
    {
      field: "rate",
      headerName: "Rate",
    },
    {
      field: "from_date",
      headerName: "effectiveDate",
    },

    {
      field: "edit",
      headerName: "Edit",
      filterable: false,
      sortable: false,
      hide: false,
      renderCell: (params) => (
        <EditRenderCell
          onClick={(e) => {
            e.stopPropagation();
            selectedRow(params.row);
          }}
          title="Edit"
        />
      ),
    },

    {
      field: "delete",
      headerName: "Delete",
      filterable: false,
      sortable: false,
      hide: false,
      renderCell: (params) => (
        <DeleteRenderCell handleDelete={() => handleDelete(params.row.id)} />
      ),
    },
  ];
  return (
    <div style={{ marginTop: "15px" }}>
      <Header title="List of Award" heading="h5" />
      {isLoading && <CircularProgress />}
      {isFetched && (
        <DataGrid columns={columns} rows={award} hideFooter autoHeight />
      )}
    </div>
  );
};

export default RecommandationList;
