import { TextField, MenuItem, Box } from '@mui/material';
import React, { useEffect, useState } from 'react';
import api from '../api/config/axiosConfig';
import _CircularProgress from './_CircularProgress';
import { useQuery } from '@tanstack/react-query';


const companySearch = ({ onCompanySelect }) => {
  const [selectedCenter, setSelectedCenter] = useState('');

  const [company, setCompany] = useState([]);

  const { data: company_list, isLoading } = useQuery(
    ['comp'],
    async () =>
      await api.get('comp').then(({ data }) => {
        return data;
      })
  );

  useEffect(() => {
    setCompany(company_list);

    console.log(company_list);
  }, [setCompany, company_list]);

  const handleCenterChange = (e) => {
    const centerValue = e.target.value;

    console.log(centerValue);

    setSelectedCenter(centerValue);
    
    onCompanySelect({
      value: centerValue,
      clicked: true,
    });
    //console.log(onSelectedCenter)
  };

  //console.log(onSelectedTaxCenter);

  if (!company) {
   return < _CircularProgress />
  }
  return (
    <Box>
      <TextField
        select
        name="tax_center"
        value={selectedCenter}
        onChange={handleCenterChange}
        fullWidth
        label="Select Site"
        // variant='standard'
        margin="normal"
        type="text"
        InputLabelProps={{
          color: 'success',
        }}
      >
        {company.map((item) => (
          <MenuItem key={item} value={item}>
            {item}
          </MenuItem>
        ))}
      </TextField>
    </Box>
  );
};

export default companySearch;
