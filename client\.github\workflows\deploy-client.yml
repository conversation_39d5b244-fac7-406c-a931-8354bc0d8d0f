name: Deploy Edomias Client to cPanel

on:
  push:
    branches:
      - main # or your main branch name

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Set up Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18' # Use a Node.js version compatible with your project

    - name: Install dependencies
      run: npm install
      working-directory: ./edomias-client

    - name: Build React App
      run: npm run build
      working-directory: ./edomias-client

    - name: Deploy to cPanel
      uses: SamKirkland/FTP-Deploy-Action@4.3.0
      with:
        server: ${{ secrets.FTP_SERVER }}
        username: ${{ secrets.FTP_USERNAME }}
        password: ${{ secrets.FTP_PASSWORD }}
        local-dir: ./edomias-client/dist/ # The build output directory for Vite
        server-directory: public_html/edomias-client/ # Adjust this to your cPanel deployment path
        protocol: ftp # or ftps, sftp if supported by your cPanel
        state-name: .ftp-deploy-state.json
        dry-run: false
