<?php

use App\Http\Controllers\Api\AnnualLeaveController;
use App\Http\Controllers\Api\AssignController;
use App\Http\Controllers\Api\AwardController;
use App\Http\Controllers\Api\ChangePasswordController;
use App\Http\Controllers\Api\ClientController;
use App\Http\Controllers\Api\ClientDeductController;
use App\Http\Controllers\Api\ContractScheduleDueController;
use App\Http\Controllers\Api\DisciplineController;
use App\Http\Controllers\Api\DuePaymentFollowupController;
use App\Http\Controllers\Api\EmpAllowanceController;
use App\Http\Controllers\Api\EmpBankController;
use App\Http\Controllers\Api\EmpBasicController;
use App\Http\Controllers\Api\EmpDetailController;
use App\Http\Controllers\Api\EmpEducationController;
use App\Http\Controllers\Api\EmpEmergencyContactController;
use App\Http\Controllers\Api\EmployeeLoanController;
use App\Http\Controllers\Api\EmployeeSponserController;
use App\Http\Controllers\Api\EmployeeStatusController;
use App\Http\Controllers\Api\FamilyInfoController;
use App\Http\Controllers\Api\finance\AbsenceListController;
use App\Http\Controllers\Api\finance\AdditionalController;
use App\Http\Controllers\Api\finance\AdditionalDeductableController;
use App\Http\Controllers\Api\finance\AddPcrateController;
use App\Http\Controllers\Api\finance\OverTimeController;
use App\Http\Controllers\Api\finance\PcRateController;
use App\Http\Controllers\Api\finance\DsaController;
use App\Http\Controllers\Api\finance\DailyRateController;
use App\Http\Controllers\Api\finance\DraftPayrollController;
use App\Http\Controllers\Api\finance\EmployeeDeductableController;
use App\Http\Controllers\Api\finance\NonTaxableAllowanceController;
use App\Http\Controllers\Api\finance\PaymentSlipController;
use App\Http\Controllers\Api\finance\PayrollController;
use App\Http\Controllers\Api\LanguageController;
use App\Http\Controllers\Api\LeaveLiablityController;
use App\Http\Controllers\Api\MetricsController;
use App\Http\Controllers\Api\PositionController;
use App\Http\Controllers\Api\Report\TaxReportController;
use App\Http\Controllers\Api\settings\DeductController;
use App\Http\Controllers\Api\settings\ExchangeRateController;
use App\Http\Controllers\Api\settings\HolidayController;
use App\Http\Controllers\Api\settings\MedicalShareController;
use App\Http\Controllers\Api\settings\PayrollDateController;
use App\Http\Controllers\Api\settings\PcrateTypeController;
use App\Http\Controllers\Api\settings\PensionController;
use App\Http\Controllers\Api\settings\ProvidentController;
use App\Http\Controllers\Api\settings\TaxCenterController;
use App\Http\Controllers\Api\settings\TerminationController;
use App\Http\Controllers\Api\SponsoredEmpController;
use App\Http\Controllers\Api\TransferController;
use App\Http\Controllers\Api\TerminationReportController;
use App\Http\Controllers\Api\UserPermissionController;
use App\Http\Controllers\Api\WorkExperianceController;
use App\Http\Controllers\AuthApi\AuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\AuthApi\AuthUser;
use App\Http\Controllers\ContractFollowUpController;
use App\Http\Controllers\PaymentFollowUpController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\SeverancePayment\CalculateSveranceController;
use App\Http\Controllers\Api\SeverancePayment\SeverancePaymentController;
use App\Http\Controllers\Api\TerminationReasonController;
use App\Http\Controllers\Api\BankPayrollController;
use App\Http\Controllers\Api\Report\EmployeesReportController;
use App\Http\Controllers\Api\Report\GeneralPayrollReportController;
use App\Http\Controllers\Api\store\ApprovalController;
use App\Http\Controllers\Api\store\CategoryController;
use App\Http\Controllers\Api\store\InventoryMovementController;
use App\Http\Controllers\Api\store\RequestController;
use App\Http\Controllers\Api\store\StoreItemController;
use App\Http\Controllers\CollectedPaymentController;
use App\Http\Controllers\ActivityLogController;
//use App\Http\Controllers\Store\CategoryController;
use App\Http\Controllers\Store\CoffeeBatchController;
use App\Http\Controllers\Store\CustomerController;
use App\Http\Controllers\Store\CustomsDeclarationController;
use App\Http\Controllers\Store\ExportOrderController;
use App\Http\Controllers\Store\FarmerController;
use App\Http\Controllers\Store\InvoiceController;
use App\Http\Controllers\Store\ItemController;
use App\Http\Controllers\Store\PhytosanitaryCertificateController;
use App\Http\Controllers\Store\PurchaseFromFarmerController;
use App\Http\Controllers\Store\QualityInspectionController;
use App\Http\Controllers\Store\ShipmentController;
use App\Http\Controllers\Store\StockMovementController;
use App\Http\Controllers\Store\WarehouseController;
use App\Http\Controllers\Store\CertificationController;
use App\Models\CollectedPayment;
use Illuminate\Support\Facades\Route;


/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('v1')->group(function () {
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('user', [AuthUser::class, 'user']);
        Route::post('change-password', [ChangePasswordController::class, 'changePassword']);
        Route::post('reset-password/{userId}', [ChangePasswordController::class, 'resetPassword']);
        Route::apiResource('users-permission', UserPermissionController::class);
        Route::delete('/logout', [AuthController::class, 'logout']);
        Route::apiResource('/users', UserController::class);
        Route::get('/activity-logs',[ActivityLogController::class, 'index']);
        
        Route::apiResource('/hire_requisitions', HireRequisitionController::class);

        Route::apiResource('/emp_basics', EmpBasicController::class);
        Route::get('/empid', [EmpBasicController::class, 'empId']);

        
        Route::apiResource('/job_postings', JobPostingController::class);
        Route::apiResource('/applicants', ApplicantController::class);
        Route::apiResource('/exam_scores', ExamScoreController::class);
        Route::apiResource('/emp_details', EmpDetailController::class);
        
        Route::apiResource('/evaluation_data', EvaluationDataController::class);
        Route::apiResource('/committee_members', CommitteeMemberController::class);



        Route::apiResource('/emp_banks', EmpBankController::class);
        Route::apiResource('/emp_allowance', EmpAllowanceController::class);
        Route::apiResource('/emp_loan', EmployeeLoanController::class);
        Route::apiResource('/emp_sponser', EmployeeSponserController::class);
        Route::apiResource('/emp_status', EmployeeStatusController::class);
        Route::apiResource('/emp_education', EmpEducationController::class);
        Route::apiResource('/emp_work_experiance', WorkExperianceController::class);
        Route::apiResource('/emp_emergency_contact', EmpEmergencyContactController::class);
        Route::apiResource('/emp_language', LanguageController::class);
        Route::apiResource('/positions', PositionController::class);
        Route::apiResource('/family_info', FamilyInfoController::class);
        Route::apiResource('/sponsord_emp', SponsoredEmpController::class);
        Route::apiResource('/award', AwardController::class);
        Route::apiResource('/discipline', DisciplineController::class);
        Route::get('/bank_code', [EmpBankController::class, 'company']);

        Route::apiResource('client', ClientController::class);
        Route::apiResource('client-deduct', ClientDeductController::class);
        Route::apiResource('assign', AssignController::class);
        Route::apiResource('payment-follow-up', PaymentFollowUpController::class);
        Route::apiResource('contract-follow-up', ContractFollowUpController::class);
        Route::apiResource('collected_payments', CollectedPaymentController::class);
        Route::get('ContractByClient/{id}', [ContractFollowUpController::class, 'showByClient']);
        Route::delete('ContractDeleteByClient/{client_id}', [ContractFollowUpController::class, 'deleteByClient']);
        Route::get('client-code',[ClientController::class, 'company']);
        Route::get('paymentsOfThisMonth',[PaymentFollowUpController::class, 'paymentsOfThisMonth']);

        Route::get('due', [ContractScheduleDueController::class, 'index']);
        Route::get('due/{id}', [ContractScheduleDueController::class, 'updateScheduleReminder']);
        Route::get('followup/due', [DuePaymentFollowupController::class, 'index']);
        Route::get('followup/due/{id}', [DuePaymentFollowupController::class, 'updateReminder']);

        //get all employs assigned to a clinet 
        Route::get('assign/client/{client_id}', [AssignController::class, 'clientAssigned']);
        Route::get('assign/employee/{emp_basic_id}', [AssignController::class, 'getAssign']);
        Route::apiResource('transfer', TransferController::class);
        Route::apiResource('leave', AnnualLeaveController::class);
        Route::get('leaveId/{id}', [AnnualLeaveController::class, 'getLeave']);
        Route::get('leave_detail/{emp_basic_id}', [AnnualLeaveController::class, 'leaveTaken']);
        Route::get('leave_twoyears/{emp_basic_id}', [AnnualLeaveController::class, 'leaveTakenTwoYears']);
        Route::get('liablity/leave', [LeaveLiablityController::class, 'liablity']);
        Route::get('PaymentByClient/{client_id}', [PaymentFollowUpController::class, 'showByClient']);
        Route::delete('PaymentDeleteByClient/{client_id}', [PaymentFollowUpController::class, 'deleteByClient']);

        //settings route 
        Route::apiResource('payroll_date', PayrollDateController::class);
        Route::apiResource('tax_center', TaxCenterController::class);
        Route::apiResource('terminate', TerminationController::class);
        Route::get('pension', [PensionController::class, 'index']);
        Route::post('pension', [PensionController::class, 'store']);
        Route::put('pension/{id}', [PensionController::class, 'update']);
        Route::get('provident', [ProvidentController::class, 'index']);
        Route::post('provident', [ProvidentController::class, 'store']);
        Route::put('provident/{id}', [ProvidentController::class, 'update']);
        Route::apiResource('pc_rate_type', PcrateTypeController::class);
        Route::get('deduct', [DeductController::class, 'index']);
        Route::post('deduct', [DeductController::class, 'store']);
        Route::put('deduct/{id}', [DeductController::class, 'update']);
        Route::get('exchange_rate', [ExchangeRateController::class, 'index']);
        Route::post('exchange_rate', [ExchangeRateController::class, 'store']);
        Route::put('exchange_rate/{id}', [ExchangeRateController::class, 'update']);
        Route::get('medical_share', [MedicalShareController::class, 'index']);
        Route::put('medical_share/{id}', [MedicalShareController::class, 'update']);
        Route::post('medical_share', [MedicalShareController::class, 'store']);
        Route::get('holiday', [HolidayController::class, 'index']);
        Route::post('holiday', [HolidayController::class, 'store']);
        Route::put('holiday/{id}', [HolidayController::class, 'update']);
        Route::apiResource('permissions', UserPermissionController::class);
        Route::apiResource('/termination_reasons', TerminationReasonController::class);
        //finance routes 
        Route::apiResource('payroll', PayrollController::class);
        Route::apiResource('overtime', OverTimeController::class);
        Route::apiResource('absence', AbsenceListController::class);
        Route::apiResource('additional', AdditionalController::class);
        Route::apiResource('additional_deductable', AdditionalDeductableController::class);
        Route::apiResource('pcrate', PcRateController::class);
        Route::apiResource('dsa', DsaController::class);
        Route::apiResource('daily_rate', DailyRateController::class);
        Route::apiResource('addpcrate', AddPcrateController::class);
        Route::apiResource('emp_deductable', EmployeeDeductableController::class);
        Route::apiResource('nontaxable', NonTaxableAllowanceController::class);
        Route::get('payroll_emp_list/{id}', [AssignController::class, 'employeeInPayRollList']);
        Route::get('getpayroll', [PayrollController::class, 'calculatePayroll']);
        Route::apiResource('getdraft', DraftPayrollController::class);
        Route::get('getslip/{id}', [PaymentSlipController::class, 'getSlip']);
        Route::get('severance/employee/{emp_id}', [CalculateSveranceController::class, 'index']);
        Route::get('severance/terminated_emp', [CalculateSveranceController::class, 'getTerminated']);
        Route::get('severance/pay_report', [CalculateSveranceController::class, 'getSeverancePayReport']);
        Route::get('severance/paid_report', [CalculateSveranceController::class, 'getSeverancePayed']);
        Route::get('severance/liablity_report', [CalculateSveranceController::class, 'severanceLiablity']);
        Route::apiResource('severances', SeverancePaymentController::class);

        //reports 
        Route::get('bankpayroll', [BankPayrollController::class, 'getBankPayroll']);
        Route::get('/termination/report', [TerminationReportController::class, 'getTerminationReport']);
        Route::get('/incometax', [TaxReportController::class, 'incomeTaxReport']);
        Route::get('/pensiontax', [TaxReportController::class, 'pensionTaxReport']);
        Route::get('/report/employees', [EmployeesReportController::class, 'getEmployeesReport']);
        Route::get('getgeneralpayroll', [GeneralPayrollReportController::class, 'getGeneralPayrollReport']);
        //notifications
        Route::get('/notifications', [NotificationController::class, 'all']);
        Route::get('/notifications/unread', [NotificationController::class, 'index']);
        Route::get('/notifications/read', [NotificationController::class, 'viewedNotifications']);
        Route::get('/notifications/mark_as_read', [NotificationController::class, 'markAllAsRead']);
        Route::get('/notifications/delete', [NotificationController::class, 'clearNotifications']);
        Route::delete('/notification/delete/{id}', [NotificationController::class, 'delete']);
        Route::get('/markAsRead/{notification_id}', [NotificationController::class, 'markAsReadNotification']);
        Route::get('/countNotification', [NotificationController::class, 'countUnreadNotifications']);
        Route::get('/getMetrics', [MetricsController::class, 'getMetrics']);

        // Store Management Routes
        Route::apiResource('store/categories', CategoryController::class);
        Route::apiResource('store/items', ItemController::class);
        Route::apiResource('store/requests', RequestController::class);
        Route::apiResource('store/approvals', ApprovalController::class);
        Route::apiResource('store/stock-movements', StockMovementController::class);
        Route::apiResource('store/warehouses', WarehouseController::class);
        Route::apiResource('store/farmers', FarmerController::class);
        Route::apiResource('store/coffee-batches', CoffeeBatchController::class);
        Route::apiResource('store/purchases-from-farmers', PurchaseFromFarmerController::class);
        Route::apiResource('store/quality-inspections', QualityInspectionController::class);
        Route::apiResource('store/customers', CustomerController::class);
        Route::apiResource('store/export-orders', ExportOrderController::class);
        Route::apiResource('store/shipments', ShipmentController::class);
        Route::apiResource('store/certifications', CertificationController::class);
        Route::apiResource('store/invoices', InvoiceController::class);
        Route::apiResource('store/customs-declarations', CustomsDeclarationController::class);
        Route::apiResource('store/phytosanitary-certificates', PhytosanitaryCertificateController::class);

        // Extra action for allocating batch to order and state transitions
        Route::post('store/export-orders/{exportOrder}/allocate-batch', [ExportOrderController::class, 'allocateBatch']);
        Route::post('store/export-orders/{exportOrder}/ship', [ExportOrderController::class, 'ship']);
        Route::post('store/export-orders/{exportOrder}/deliver', [ExportOrderController::class, 'deliver']);
    });
    Route::middleware('guest')->group(function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('register', [AuthController::class, 'register']);
    });
});
