<?php

namespace App\Http\Controllers\Api\settings;

use App\Http\Controllers\Controller;
use App\Models\settings\ExchangeRate;
use Illuminate\Http\Request;

class ExchangeRateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(ExchangeRate::latest()->first());
    }

   

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data=ExchangeRate::create([
            'exchange_rate'=>$request->input('exchange_rate')
        ]);
        return response($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\settings\ExchangeRate  $exchangeRate
     * @return \Illuminate\Http\Response
     */
    public function show(ExchangeRate $exchangeRate)
    {
      return  response($exchangeRate);
    }

  

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\settings\ExchangeRate  $exchangeRate
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request,  $id)
    {

        $exchangeRate=ExchangeRate::find($id);
        $exchangeRate->update([
            'exchange_rate'=>$request->input('exchange_rate')
        ]);
        return response($exchangeRate);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\settings\ExchangeRate  $exchangeRate
     * @return \Illuminate\Http\Response
     */
    public function destroy(ExchangeRate $exchangeRate)
    {
        $exchangeRate->delete();
        return response('',204);
    }
}
