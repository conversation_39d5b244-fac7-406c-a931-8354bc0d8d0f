import React from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";
import Header from "../../../components/Header";
import { DataGrid } from "@mui/x-data-grid";
import {
  Button,
  Typography,
  useTheme,
  Box,
  CircularProgress,
} from "@mui/material";

import { tokens } from "../../../utils/theme";
import { useStateContext } from "../../../context/ContextProvider";
function EmployeeDeductableList({ deductable, selectedRow }) {
  const queryClient = useQueryClient();
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const {
    data: emp_deduct,
    isLoading,
    isError,
  } = useQuery(
    ["empDeductable", deductable.emp_basic_id],
    async () =>
      await api
        .get(`emp_deductable/${deductable.emp_basic_id}`)
        .then(({ data }) => data),
    {
      enabled: !!deductable.emp_basic_id,
    }
  );
  
  const { setNotification } = useStateContext();
  const columns = [
    {
      field: "id",
      headerName: "ID",
      flex: 0.2,
    },
    {
      field: "loan_type",
      headerName: "Loan Type",
      flex: 1,
    },
    {
      field: "loan_amount",
      headerName: "Loan Amount",
      flex: 0.8,
    },
    {
      field: "loan_period",
      headerName: "Loan Period",
      flex: 0.5,
    },

    {
      field: "loan_date",
      headerName: "Effective Date",
      flex: 0.5,
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 0.7,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          selectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "delete",
      headerName: "Delete",
      flex: 0.8,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deleteDeductable = useMutation(
          (id) => api.delete(`emp_deductable/${id}`),
          {
            onSuccess: () => {
              setNotification(
                "Additional deductable record deleted successfully"
              );
              // setDeduct(deduct.filter((data) => data.id != params.row.id));
            },
            onSettled: () => {
              queryClient.invalidateQueries({
                queryKey: ["assignedEmployee", "empDeductable"],
              });
            },
          }
        );
        const onDeleteDeductable = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              "Are you sure you want to delete the selected  additional deductable record?"
            )
          ) {
            return;
          }
          deleteDeductable.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onDeleteDeductable}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];
  return (
    <>
      <Header title="List of Deductable records" heading="h5" />
      {isLoading ? (
        <CircularProgress />
      ) : (
        <DataGrid autoHeight hideFooter columns={columns} rows={emp_deduct} />
      )}
    </>
  );
}

export default EmployeeDeductableList;
