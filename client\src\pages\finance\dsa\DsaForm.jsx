import React, { useState, useEffect } from "react";
import { Grid, Box, TextField, MenuItem, Button } from "@mui/material";
import { useStateContext } from "../../../context/ContextProvider";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";
export const  isTaxable = [
  {
    label: "taxable",
    value: "1",
  },
  {
    label: "non-taxable",
    value: "0",
  },
];
function DsaForm({ formData, editData, setEditData }) {
  const numberArray = [...Array(31).keys()].map((x) => ++x);
  const queryClient = useQueryClient();
  const [dsa, setDsa] = useState({
    from_date: "",
    to_date: "",
    rate_amount: "",
    no_days: "",
    taxable: "",
  });
  useEffect(() => {
    if (editData) {
      setDsa(editData);
    }
  }, [editData]);

  const { setNotification, user } = useStateContext();
  const createDsa = useMutation((data) => api.post("dsa", data), {
    onSuccess: () => {
      setNotification("dsa inserted successfully");
      queryClient.invalidateQueries({ queryKey: "dsa" });
    },
  });
  const handleChange = (e) => {
    setDsa({
      ...dsa,
      [e.target.name]: e.target.value,
    });
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    const payload = {
      emp_basic_id: formData.emp_basic_id,
      client_id: formData.client_id,
      from_date: data.get("from_date"),
      to_date: data.get("to_date"),
      rate_amount: data.get("rate_amount"),
      no_days: data.get("no_days"),
      taxable: data.get("taxable"),
      user_id: user.id,
    };
    if (editData && editData.id) {
      api.put(`dsa/${editData.id}`, payload).then(() => {
        setNotification("dsa record updated successfully");
        setEditData({});
        queryClient.invalidateQueries({ queryKey: "dsa" });
      });
    } else {
      createDsa.mutate(payload);
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="From Date"
            name="from_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={dsa.from_date || new Date().toISOString().slice(0, 10)}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Ending Date"
            name="to_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={dsa.to_date || new Date().toISOString().slice(0, 10)}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Rate Amount"
            name="rate_amount"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={dsa.rate_amount}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Number of Days"
            name="no_days"
            fullWidth
            select
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={dsa.no_days ? dsa.no_days : 1}
          >
            {numberArray.map((num) => (
              <MenuItem key={num} value={num}>
                {num}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={3}>
          <TextField
            margin="normal"
            label="Dsa-Allowance Taxable/non-Taxable"
            name="taxable"
            fullWidth
            select
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={dsa.taxable ? dsa.taxable : ""}
          >
            {isTaxable.map((value) => (
              <MenuItem key={value.value} value={value.value}>
                {value.label}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
      </Grid>
      <span style={{ float: "inline-start" }}>
        <Button type="submit" variant="contained" color="success">
          {editData.id ? <>update</> : <>create</>}
        </Button>
      </span>
    </Box>
  );
}

export default DsaForm;
