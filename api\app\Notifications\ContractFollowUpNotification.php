<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ContractFollowUpNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(public $contract_id, public $client_id, public $daysLeftUntilDue, public $start_date, public $end_date, public $contract_document, public $contract_schedule, public $message, public $last_triggerd_date, public $last_schedule, public $overdue, public $company_name, public $triggering_date)
    {

        $this->contract_id = $contract_id;
        $this->client_id = $client_id;
        $this->start_date = $start_date;
        $this->end_date = $end_date;
        $this->contract_document = $contract_document;
        $this->contract_schedule = $contract_schedule;
        $this->message = $message;
        $this->overdue = $overdue;
        $this->company_name = $company_name;
        $this->triggering_date = $triggering_date;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->line('The introduction to the notification.')
            ->action('Notification Action', url('/'))
            ->line('Thank you for using our application!');
    }

    // /**
    //  * Get the array representation of the notification.
    //  *
    //  * @param  mixed  $notifiable
    //  * @return array
    //  */
    // public function toArray($notifiable)
    // {
    //     return [
    //         // 'id' => $this->data['id'],
    //         'client_id' => $this->data['client_id'],
    //         'daysLeftUntilDue' => $this->data['daysLeftUntilDue'],
    //         'start_date' => $this->data['start_date'],
    //         'end_date' => $this->data['end_date'],
    //         'contract_document' => $this->data['contract_document'],
    //         'contract_schedule' => $this->data['contract_schedule'],
    //         'message' => $this->data['message'],
    //     ];
    // }

    public function toDatabase($notifiable)
    {
        return [
            'message' => [
                'contract_id' => $this->contract_id,
                'client_id' => $this->client_id,
                'daysLeftUntilDue' => $this->daysLeftUntilDue,
                'start_date' => $this->start_date,
                'end_date' => $this->end_date,
                'contract_document' => $this->contract_document,
                'contract_schedule' => $this->contract_schedule,
                'message' => $this->message,
                'overdue' => $this->overdue,
                'company_name' => $this->company_name,
                'trigering_date' => $this->triggering_date,
                'type' => 'contract'
            ]
        ];
    }
}
