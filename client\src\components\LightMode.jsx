import { DarkModeOutlined, LightModeOutlined } from "@mui/icons-material";
import { IconButton, useTheme, } from "@mui/material";
import { useContext } from "react";

import { ColorModeContext, tokens } from "../utils/theme";

function LightMode() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const colorMode = useContext(ColorModeContext);
  return (
    <IconButton onClick={colorMode.toggleColorMode}>
      {theme.palette.mode === "dark" ? (
        <DarkModeOutlined />
      ) : (
        <LightModeOutlined />
      )}
    </IconButton>
  );
}

export default LightMode;
