<?php

namespace App\Http\Controllers\Api\settings;

use App\Http\Controllers\Controller;
use App\Models\settings\PcRateType;
use Illuminate\Http\Request;

class PcrateTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(PcRateType::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data=PcRateType::create([
            'pc_rate_type'=>$request->input('pc_rate_type'),
            'pc_rate_amount'=>$request->input('pc_rate_amount'),
            // 'user_id'=>null,
        ]);

        return response($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\settings\PcRateType  $pcRateType
     * @return \Illuminate\Http\Response
     */
    public function show(PcRateType $pcRateType)
    {
        return response($pcRateType);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\settings\PcRateType  $pcRateType
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, PcRateType $pcRateType)
    {
        $pcRateType->update([
            'pc_rate_type'=>$request->input('pc_rate_type'),
            'pc_rate_amount'=>$request->input('pc_rate_amount'),
            // 'user_id'=>'',
        ]);

        return response($pcRateType);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\settings\PcRateType  $pcRateType
     * @return \Illuminate\Http\Response
     */
    public function destroy(PcRateType $pcRateType)
    {
        $pcRateType->delete();

        return response('',204);
    }
}
