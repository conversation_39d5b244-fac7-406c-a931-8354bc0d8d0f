<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class EmpBasicResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // Check if the request has 'all' parameter to determine if all related data should be loaded
        $shouldLoadAllData = $request->query('all', false);
        if ($shouldLoadAllData) {
            // Load all related data if 'all' parameter is true
            return parent::toArray($request);
        }
        // Return the basic employee data along with related data if loaded
        // Note: Adjust the fields as per your actual model attributes
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'middle_name' => $this->middle_name,
            'last_name' => $this->last_name,
            'assigned_client' => $this->assign?->client?->company_name ?? 'Not Assigned',
            'position' => $this->position,
        ];
    }
}