<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('emp_banks', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('emp_basic_id');
            $table->foreign('emp_basic_id')->references('id')->on('emp_basics')->onDelete('cascade');
            $table->string('bank_account')->nullable();
            $table->string('bank_code')->nullable();
            $table->string('bank_branch')->nullable();
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
    }

    // Schema::create('emp_bank', function (Blueprint $table) {
    //     $table->integer('Emp_Bank_ID')->primary()->unique();
    //     $table->integer('Eid');
    //     $table->text('Bank_Account');
    //     $table->text('Bank_Code');
    //     $table->text('Bank_Branch');
    //     $table->integer('Uid');
    // });
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('emp_banks');
    }
};
