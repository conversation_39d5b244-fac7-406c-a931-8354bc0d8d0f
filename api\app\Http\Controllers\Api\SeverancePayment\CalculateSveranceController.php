<?php

namespace App\Http\Controllers\Api\SeverancePayment;

use App\Http\Controllers\Controller;
use App\Models\EmpBasic;
use App\Models\settings\Termination;
use App\Models\SeverancePayment;
use App\Models\TerminationReason;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class CalculateSveranceController extends Controller
{


    public function index($emp_id)
    {
        $data = EmpBasic::join('terminations', 'emp_basics.id', '=', 'terminations.emp_basic_id')->select('emp_basics.id', 'emp_basics.first_name', 'emp_basics.middle_name', 'emp_basics.last_name', 'emp_basics.position', 'emp_basics.basic_salary', 'emp_basics.start_date', 'terminations.*')->where('emp_basics.id', $emp_id)->first();
        $basicSalary = $data['basic_salary'];
        $startDate = $data['start_date'];
        $terminationDate = $data['termination_date'];
        $terminationReason = $data['reason'];
        $numberOfYears = $this->calcNumberOfYears($startDate, $terminationDate);
        $severancePayment = $this->severancePayment($startDate, $terminationDate, $basicSalary, $terminationReason);

        $wholeNumber = $this->payPoint($severancePayment, $basicSalary)['wholeNumber'];
        $decimalNumber = $this->payPoint($severancePayment, $basicSalary)['decimalNumber'];
        $taxWholeNumber = $this->taxWithWholeNumber($basicSalary, $wholeNumber);
        $taxDecimalNumber = $this->taxWithWholeNumber($basicSalary, $decimalNumber);
        $severanceTaxCalc = $this->employeePayment($taxWholeNumber, $taxDecimalNumber, $severancePayment);
        return response([
            'fullname' => $data['first_name'] . " " . $data['middle_name'] . " " . $data['last_name'],
            'start_date' => $startDate,
            'termination_date' => $terminationDate,
            'basic_salary' =>round( $basicSalary,2),
            'y_of_service' =>round( $numberOfYears['totalYears'] + $numberOfYears['decimalYears'],2),
            'total_severance_pay' =>round( $severancePayment,2),
            'severance_tax' =>round( $severanceTaxCalc['tax'],2),
            'severance_net' =>round( $severanceTaxCalc['payment'],2),
            'termination_reason' => $this->formatTerminationReason($terminationReason),

        ]);
    }

    public function getSeverancePayReport()
    {
        $count=0;
        $result = EmpBasic::join('terminations', 'emp_basics.id', '=', 'terminations.emp_basic_id')->select('emp_basics.id', 'emp_basics.first_name', 'emp_basics.middle_name', 'emp_basics.last_name', 'emp_basics.position', 'emp_basics.basic_salary', 'emp_basics.start_date', 'terminations.*',)->where('severance_payment_status', '=', 0)->get()->map(function ($data) use(&$count) {
            $basicSalary = $data['basic_salary'];
            $startDate = $data['start_date'];
            $terminationDate = $data['termination_date'];
            $terminationReason = $data['reason'];
            $numberOfYears = $this->calcNumberOfYears($startDate, $terminationDate);
            $severancePayment = $this->severancePayment($startDate, $terminationDate, $basicSalary, $terminationReason);
            $count++;
            $wholeNumber = $this->payPoint($severancePayment, $basicSalary)['wholeNumber'];
            $decimalNumber = $this->payPoint($severancePayment, $basicSalary)['decimalNumber'];
            $taxWholeNumber = $this->taxWithWholeNumber($basicSalary, $wholeNumber);
            $taxDecimalNumber = $this->taxWithWholeNumber($basicSalary, $decimalNumber);
            $severanceTaxCalc = $this->employeePayment($taxWholeNumber, $taxDecimalNumber, $severancePayment);
            return [
                'id'=>$count,
                'fullname' => $data['first_name'] . " " . $data['middle_name'] . " " . $data['last_name'],
                'start_date' => $startDate,
                'termination_date' => $terminationDate,
                'basic_salary' =>round( $basicSalary,2),
                'y_of_service' =>round( $numberOfYears['totalYears'] + $numberOfYears['decimalYears'],2),
                'total_severance_pay' =>round( $severancePayment,2),
                'severance_tax' =>round( $severanceTaxCalc['tax'],2),
                'severance_net' =>round( $severanceTaxCalc['payment'],2),
                'termination_reason' => $this->formatTerminationReason($terminationReason),

            ];
        });

        return response($result);
    }
    public function getSeverancePayed()
    {
        $data = SeverancePayment::all();

        return response($data);
    }

    public function severanceLiablity()
    {
        $count=0;
        $employees = EmpBasic::where('termination_status', 0)->get()->map(function ($data) use(&$count) {

            $count++;
            $basicSalary = $data['basic_salary'];
            $startDate = $data['start_date'];
            $numberOfYears = $this->calcNumberOfYears($startDate, now());
            $severancePayment = $this->severancePaymentLiablity($startDate, $basicSalary);
            return [
                'id'=>$count,
                'full_name' => $data['first_name'] . " " . $data['middle_name'] . " " . $data['last_name'],
                'start_date' => $startDate,
                'basic_salary' =>round($basicSalary,2),
                'y_of_service' =>round($numberOfYears['totalYears'] + $numberOfYears['decimalYears'],2),
                'total_severance_pay' => round($severancePayment,2),
            ];
        });
        $allSeverance = $employees->sum('total_severance_pay');
        return response(['employees' => $employees, 'total_severance' => round($allSeverance,2)]);
    }
    public function formatTerminationReason($terminationReason)
    {
        $reason = TerminationReason::where('id', $terminationReason)->first();

        return $reason['termination_reason'];
    }

    public function isEmployeePassedProhibation($startDate)
    {
        $startDate = Carbon::parse($startDate);
        if ($startDate->diffInDays(now()) >= 0) {
            return true;
        } else {
            return false;
        }
    }

    public function oneThird($basicSalary)
    {
        return ($basicSalary / 30) * 10;
    }
    public function calcNumberOfYears($startDate, $terminationDate)
    {

        $terminationDate = new DateTime($terminationDate);
        $startDate = new DateTime($startDate);

        $years = $startDate->diff($terminationDate)->y;
        $months = $startDate->diff($terminationDate)->m;
        $days = $startDate->diff($terminationDate)->d;
        $monthsInYears = $months / 12;
        $daysInYears = $days / 365;

        $totalYears = $years + $monthsInYears + $daysInYears;

        $totalYear = floor($totalYears);
        $decimalYear = round(fmod($totalYears, 1), 2);
        return [
            'totalYears' => $totalYear,
            'decimalYears' => $decimalYear,
        ];
    }

    public function severancePayment($startDate, $terminationDate, $basicSalary, $terminationReason)
    {

        $yearOfService = $this->calcNumberOfYears($startDate, $terminationDate);
        $sumOfSeverancePay = 0;
        if ($yearOfService['totalYears'] >= 5) {
            $sumOfSeverancePay = $basicSalary + ($yearOfService['totalYears'] - 1) * $this->oneThird($basicSalary) + $yearOfService['decimalYears'] * $this->oneThird($basicSalary);
        } else {
            if ($this->isEmployeePassedProhibation($startDate)) {
                if ($yearOfService['totalYears'] < 1 && ($terminationReason == 2 ||  $terminationReason == 6)) {
                    $sumOfSeverancePay = $basicSalary;
                } else if ($yearOfService['totalYears'] > 1 && $yearOfService['totalYears'] < 5 && $terminationReason == 2 ||  $terminationReason == 6) {
                    $sumOfSeverancePay = $basicSalary + ($yearOfService['totalYears'] - 1) * $this->oneThird($basicSalary) + $yearOfService['decimalYears'] * $this->oneThird($basicSalary);
                } else {
                    $sumOfSeverancePay = 0;
                }
            } else {
                $sumOfSeverancePay = 0;
            }
        }

        return $sumOfSeverancePay;
    }
    public function severancePaymentLiablity($startDate, $basicSalary)
    {

        $yearOfService = $this->calcNumberOfYears($startDate, now());
        $sumOfSeverancePay = 0;

        if ($this->isEmployeePassedProhibation($startDate)) {
            if ($yearOfService['totalYears'] <= 1) {
                $sumOfSeverancePay = $basicSalary;
            } else {
                $sumOfSeverancePay = $basicSalary + ($yearOfService['totalYears'] - 1) * $this->oneThird($basicSalary) + $yearOfService['decimalYears'] * $this->oneThird($basicSalary);
            }
        } else {
            $sumOfSeverancePay = 0;
        }


        return $sumOfSeverancePay;
    }

    public function payPoint($severancePayment, $basicSalary)
    {
        $fractionNumber = $severancePayment / $basicSalary;
        $wholeNumber = floor($fractionNumber);
        $decimalNumber = round(fmod($fractionNumber, 1), 2);

        return [
            'wholeNumber' => $wholeNumber,
            'decimalNumber' => $decimalNumber,
        ];
    }

    public function taxWithWholeNumber($basicSalary, $wholeNumber)
    {
        $salary = $basicSalary * $wholeNumber;
        $income_tax = 0;
        if ($salary > 0 && $salary <= 600)
            return $income_tax;
        else if ($salary > 601 && $salary <= 1650) {
            $income_tax = ($salary * 10) / 100 - 60;
            return $income_tax;
        } else if ($salary > 1651 && $salary <= 3200) {
            $income_tax = ($salary * 15) / 100 - 142.50;
            return $income_tax;
        } else if ($salary > 3201 && $salary <= 5250) {
            $income_tax = ($salary * 20) / 100 - 302.50;
            return $income_tax;
        } else if ($salary > 5251 && $salary <= 7800) {
            $income_tax = ($salary * 25) / 100 - 565.00;
            return $income_tax;
        } else if ($salary > 7801 && $salary <= 10900) {
            $income_tax = ($salary * 30) / 100 - 955.00;
            return $income_tax;
        } else if ($salary > 10900) {
            $income_tax = ($salary * 35) / 100 - 1500.00;
            return $income_tax;
        } else
            return $income_tax;
    }

    public function taxWithDecimal($basicSalary, $decimalNumber) //decimal number is came from paypoint function
    {
        $salary = $basicSalary;
        $decimal = $decimalNumber;
        $salary = $salary * $decimal;

        $income_tax = 0;
        if ($salary > 0 && $salary <= 600)
            return $income_tax;
        else if ($salary > 601 && $salary <= 1650) {
            $income_tax = ($salary * 10) / 100 - 60;
            return $income_tax;
        } else if ($salary > 1651 && $salary <= 3200) {
            $income_tax = ($salary * 15) / 100 - 142.50;
            return $income_tax;
        } else if ($salary > 3201 && $salary <= 5250) {
            $income_tax = ($salary * 20) / 100 - 302.50;
            return $income_tax;
        } else if ($salary > 5251 && $salary <= 7800) {
            $income_tax = ($salary * 25) / 100 - 565.00;
            return $income_tax;
        } else if ($salary > 7801 && $salary <= 10900) {
            $income_tax = ($salary * 30) / 100 - 955.00;
            return $income_tax;
        } else if ($salary > 10900) {
            $income_tax = ($salary * 35) / 100 - 1500.00;
            return $income_tax;
        } else
            return $income_tax;
    }

    public function employeePayment($taxWholeNumber, $taxDecimal, $severancePayment)
    {

        $firstTax = $taxWholeNumber;
        $secondTax = $taxDecimal;

        $sumTax = $firstTax + $secondTax;

        $payment = $severancePayment - $sumTax;

        return [
            'tax' => $sumTax,
            'payment' => $payment
        ];
    }

    public function getTerminated()
    {
        $data = Termination::join('emp_basics', 'terminations.emp_basic_id', 'emp_basics.id')->select('emp_basics.first_name', 'emp_basics.middle_name', 'emp_basics.last_name', 'emp_basics.id')->where('severance_payment_status', '=', 0)->get();

        return response($data);
    }

//for metrics
    public function severanceLiablityTotal()
    {
        $employees = EmpBasic::where('termination_status', 0)->get()->map(function ($data) {
            $basicSalary = $data['basic_salary'];
            $startDate = $data['start_date'];
            $severancePayment = $this->severancePaymentLiablity($startDate, $basicSalary);
            return [
                'total_severance_pay' => $severancePayment,
            ];
        });
        return $employees->sum('total_severance_pay');

    }
}
