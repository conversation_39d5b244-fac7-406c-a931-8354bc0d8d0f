<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\FamilyInfo;
use Illuminate\Http\Request;

class FamilyInfoController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $familyInfo = FamilyInfo::all();

        return response($familyInfo);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // $data = $request->validate([
        //     'emp_basic_id'=>'required',
        //     'relationship' => 'required',
        //     'name' => 'required'
        // ]);
        $data=$request->all();

        $familyInfo= FamilyInfo::create([
            'emp_basic_id'=>$data['emp_basic_id'],
            'name' => $data['name'],
            'relationship' => $data['relationship'],
            'age' => $request->age,
            'office_phone' => $request->office_phone,
            'phone' => $request->phone
        ]);

        return response($familyInfo);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\FamilyInfo  $familyInfo
     * @return \Illuminate\Http\Response
     */
    public function show(FamilyInfo $familyInfo)
    {
        return response($familyInfo);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\FamilyInfo  $familyInfo
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, FamilyInfo $familyInfo)
    {
        // $data = $request->validate([
        //     'emp_basic_id'=>'required',
        //     'relationship' => 'required',
        //     'name' => 'required'
        // ]);
        $data=$request->all();
        $familyInfo->update([
            'emp_basic_id'=>$data['emp_basic_id'],
            'name' => $data['name'],
            'relationship' => $data['relationship'],
            'age' => $request->age,
            'office_phone' => $request->office_phone,
            'phone' => $request->phone
        ]);

        return response($familyInfo);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\FamilyInfo  $familyInfo
     * @return \Illuminate\Http\Response
     */
    public function destroy(FamilyInfo $familyInfo)
    {
        $familyInfo->delete();

        return response('', 204);
    }
}
