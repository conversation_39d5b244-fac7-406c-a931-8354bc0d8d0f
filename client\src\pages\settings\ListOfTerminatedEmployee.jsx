import React from "react";
import { useTheme, CircularProgress, Box } from "@mui/material";
import { tokens } from "../../utils/theme";
import { useQuery } from "@tanstack/react-query";
import api from "../../api/config/axiosConfig";
import { DataGrid } from "@mui/x-data-grid";

function ListOfTerminatedEmployee() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { data: terminated, isLoading } = useQuery(
    ["terminated"],
    async () => await api.get("terminate").then(({ data }) => data),
    {
      refetchOnWindowFocus: false,
      staleTime: 60000,
    }
  );
  
  const columns = [
    {
      field: "id",
      headerName: "Id",
    },
    {
      field: "first_name",
      headerName: "FirstName",
      flex: 1,
    },
    {
      field: "company_name",
      headerName: "Company Name",
      flex: 1,
    },
    {
      field: "termination_date",
      headerName: "Termination Date",
      flex: 1,
    },
    {
      field: "reason",
      headerName: "Reason",
      flex: 1,
    },
    {
      field: "ref_no",
      headerName: "Refrence Number",
      flex: 1,
    },
  ];
  if (isLoading) {
    return <CircularProgress />;
  }
  return (
    <Box
      height="75vh"
      sx={{
        "& .MuiDataGrid-root": {
          border: "none",
        },
        "& .MuiDataGrid-cell": {
          borderBottom: "none",
        },
        "& .name-column--cell": {
          color: colors.greenAccent[300],
        },
        "& .MuiDataGrid-columnHeaders": {
          backgroundColor: colors.blueAccent[700],
          borderBottom: "none",
        },
        "& .MuiDataGrid-virtualScroller": {
          backgroundColor: colors.primary[400],
        },
        "& .MuiDataGrid-footerContainer": {
          borderTop: "none",
          backgroundColor: colors.blueAccent[700],
        },
        "& .MuiCheckBox-root": {
          color: `${colors.greenAccent[200]}!important`,
        },
        "& .MuiDataGrid-toolbarContainer .MuiButton-text": {
          color: `${colors.grey[100]}!important`,
        },
      }}
    >
      <DataGrid rows={terminated} columns={columns} />
    </Box>
  );
}

export default ListOfTerminatedEmployee;
