import { Box, Grid, Paper } from '@mui/material';
import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useEmployeeData } from '../../../api/userApi/clinetHook';
import Header from '../../../components/Header';
import InfoCard from '../../../components/InfoCard';

function LeaveSummary() {
  const location = useLocation();
  const navigate = useNavigate();
  const data = location.state;

  console.log('console.log', data);

  const handleComulative = () => {
    console.log('Entering', data);
    navigate('/employees/leave-summary-lookup', {
      state: { leave: data.leave, formData: data.formData },
    });
  };

  const handleLeaveTakenUntil = () => {
    navigate('/employees/leave-detail', {
      state: data.formData,
    });
  };

  const handleLeaveIntwoYears = () => {
    navigate('/employees/leave-detail-two', {
      state: data.formData,
    });
  };

  const { data: emp, isFetched } = useEmployeeData(data.formData.emp_basic_id);

  return (
    <Box marginX="20px">
      <Header
        title="Employee Leave Summary"
        subtitle={
          isFetched &&
          `Employee Name: ${emp.first_name} ${emp.middle_name} ${emp.last_name}`
        }
      />
      <Grid container spacing={2} sx={{ marginTop: 2 }}>
        <Grid item xs={12} sm={3}>
          <InfoCard
            title={data.leave.totalLeave.toFixed(3)}
            content="Total Leave Employee Could Have"
            onClick={handleComulative}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <InfoCard
            title={data.leave.totalLeaveTaken.toFixed(3)}
            content="Leave Taken Until Now"
            onClick={handleLeaveTakenUntil}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <InfoCard
            title={data.leave.leaveTakenTwoYears.toFixed(3)}
            content="Leave Taken In Two Years"
            onClick={handleLeaveIntwoYears}
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <InfoCard
            isavailableLeft={true}
            title={data.leave.availableLeaveEntitled.toFixed(3)}
            content="Entitled Leave In Two Years"
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <InfoCard
            isavailableLeft={true}
            isDanger={data.leave.availableLeave.toFixed(3) < 0}
            title={data.leave.availableLeave.toFixed(3)}
            content="The  Available Leave Left"
          />
        </Grid>
        <Grid item xs={12} sm={3}>
          <InfoCard
            title={data.leave.expiredLeave.toFixed(3)}
            content="The Expired Leave "
            isDanger={true}
          />
        </Grid>
      </Grid>
    </Box>
  );
}

export default LeaveSummary;
