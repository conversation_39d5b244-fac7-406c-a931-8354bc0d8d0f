<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('additionals', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('emp_basic_id');
            $table->unsignedBigInteger('client_id');
            $table->foreign('emp_basic_id')->references('id')->on('emp_basics')->onDelete('cascade');
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->string('working_time');
            $table->string('minutes');
            $table->float('salary');
            $table->float('total');
            $table->string('rate_type');
            $table->date('end_date');
            $table->date('effective_date');
            $table->tinyInteger('paid')->default(0);
            $table->string('reason');
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('additionals');
    }
};
