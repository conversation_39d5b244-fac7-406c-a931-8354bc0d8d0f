import React from "react";
import LinearProgress, {
  linearProgressClasses,
} from "@mui/material/LinearProgress";
;
import { Box, useTheme } from "@mui/material";
import { tokens } from "../utils/theme";

function _LinearProgress() {
  const theme = useTheme();
const colors=tokens(theme.palette.mode)
  return (
    <Box
    
      sx={{
        [`&.${linearProgressClasses.colorPrimary}`]: {
          backgroundColor: colors.primary[400],
        },
        [`& .${linearProgressClasses.bar}`]: {
          borderRadius: 5,
          backgroundColor: colors.blueAccent[500],
        },
        height:'1px'
      }}
      
    >
      <LinearProgress  />
    </Box>
  );
}

export default _LinearProgress;
