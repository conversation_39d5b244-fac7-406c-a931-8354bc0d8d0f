import React, { useState } from "react";
import CustomPopOver from "../../components/CustomPopOver";
import { Box, TextField, Button, useTheme } from "@mui/material";
import { tokens } from "../../utils/theme";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import api from "../../api/config/axiosConfig";
import { useStateContext } from "../../context/ContextProvider";
function SetMedicalShare({ isopen, anchorEl, handleClose }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [medicalShare, setMedicalShare] = useState("");
  const { setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const { data: medical_share } = useQuery(
    ["medical"],
    async () =>
      await api.get("medical_share").then(({ data }) => {
        setMedicalShare(data.medical_share);

        return data;
      }),
    {
      staleTime: 600000,
      refetchOnWindowFocus: false,
    }
  );

  const createMedical = useMutation((data) => api.post("medical_share", data), {
    onSuccess: () => {
      setNotification("medical_share updated successfully");
      queryClient.invalidateQueries(["medical"]);
    },
  });
  const handleSubmit = (e) => {
    e.preventDefault();
    const payload = {
      medical_share: medicalShare,
    };
    if (medicalShare.id) {
      api.put(`medical_share/${medical_share.id}`, payload).then(() => {
        setNotification("medical_share updated successfully");
        queryClient.invalidateQueries(["medical"]);
        handleClose();
      });
    } else {
      createMedical.mutate(payload);
      handleClose();
    }

    // api.post('medical_share',payload)
  };
  return (
    <CustomPopOver
      anchorEl={anchorEl}
      isopen={isopen}
      handleClose={handleClose}
    >
      <Box
        component="form"
        onSubmit={handleSubmit}
        sx={{
          background: colors.primary[400],
          color: colors.grey[100],
          padding: 2,
        }}
      >
        <TextField
          label="set medical share"
          name="medical_share"
          InputLabelProps={{
            color: "success",
          }}
          value={medicalShare}
          onChange={(val) => setMedicalShare(val.target.value)}
          type="number"
        />
        <Box sx={{ pt: 1, display: "flex", justifyContent: "space-between" }}>
          <Button onClick={handleClose} sx={{ color: colors.grey[100] }}>
            cancel
          </Button>
          <Button type="submit" sx={{ color: colors.grey[100] }}>
            submit
          </Button>
        </Box>
      </Box>
    </CustomPopOver>
  );
}

export default SetMedicalShare;
