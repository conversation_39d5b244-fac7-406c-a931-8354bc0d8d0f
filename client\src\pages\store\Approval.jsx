import React from 'react';
import { <PERSON>, Typography, Button, useTheme } from '@mui/material';
import { useParams } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import api from '../../api/config/axiosConfig';
import Header from '../../components/Header';
import { tokens } from '../../utils/theme';
import _CircularProgress from '../../components/_CircularProgress';
import { useStateContext } from '../../context/ContextProvider';

function Approval() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { id } = useParams();
  const queryClient = useQueryClient();
  const { setNotification } = useStateContext();

  const { data: notification, isLoading } = useQuery(
    ['notification', id],
    async () =>
      await api.get(`notifications/${id}`).then(({ data }) => {
        return data;
      })
  );

  const approveMutation = useMutation(
    (requestId) => api.post(`store/requests/${requestId}/approve`),
    {
      onSuccess: () => {
        setNotification('Request Approved Successfully');
        queryClient.invalidateQueries(['notification', id]);
      },
    }
  );

  const disapproveMutation = useMutation(
    (requestId) => api.post(`store/requests/${requestId}/disapprove`),
    {
      onSuccess: () => {
        setNotification('Request Disapproved Successfully');
        queryClient.invalidateQueries(['notification', id]);
      },
    }
  );

  if (isLoading) {
    return <_CircularProgress />;
  }

  const { data } = notification;

  return (
    <Box m="10px">
      <Header title="Approve Request" subtitle="Approve or disapprove the item request" />
      <Box
        sx={{
          backgroundColor: colors.primary[400],
          padding: '20px',
          borderRadius: '5px',
        }}
      >
        <Typography variant="h6">Item: {data.item_name}</Typography>
        <Typography>Quantity: {data.quantity}</Typography>
        <Typography>Requested By: {data.requested_by}</Typography>
        <Box mt="20px">
          <Button
            variant="contained"
            color="success"
            onClick={() => approveMutation.mutate(data.request_id)}
          >
            Approve
          </Button>
          <Button
            variant="contained"
            color="error"
            sx={{ ml: '10px' }}
            onClick={() => disapproveMutation.mutate(data.request_id)}
          >
            Disapprove
          </Button>
        </Box>
      </Box>
    </Box>
  );
}

export default Approval;
