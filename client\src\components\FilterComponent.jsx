import {
  Box,
  Checkbox,
  FormControl,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  ListItemText,
  Grid,
  FormControlLabel,
  TextField,
} from '@mui/material';

import React, { useState } from 'react';
import DateDiffrenceComponent from './DateDiffrenceComponent';
import SearchBar from './SearchBar';
import CompanySearch from './CompanySearch';
import YearDropDown from './YearDropDown';
import TaxCenterDropDown from './TaxCenterDropDown';
import BankCodeDropDown from './BankCodeDropDown';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;

const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

const FilterComponent = ({
  passFromDate,
  passToDate,
  passProjectId,
  passYear,
  passTaxCenter,
  passCompany,
  passCurrent,
  passRegion,
  passBank
}) => {
  const [selectedComponents, setSelectedComponents] = useState([]);
  const [isCurrentDateChecked, setIsCurrentDateChecked] = useState(false);
  const [region, setRegion] = useState('');

  const handleChange = (event) => {
    const {
      target: { value },
    } = event;
    setSelectedComponents(value);
  };

  const handleDateChanges = (fromDate, toDate) => {
    passFromDate(fromDate);
    passToDate(toDate);
  };

  const handleClientSelect = ({ value, clicked }) => {
    passProjectId(value.id);
  };

  const handleTaxSelect = ({ value, clicked }) => {
    console.log(value);
    passTaxCenter(value);
  };

  const handleSelectedYear = (year) => {
    console.log(year);
    passYear(year);
  };

  const handleRegionSelect = (region) => {
    console.log(region);
    setRegion(region);
    passRegion(region);
  };

  const handleCompanySelect = ({ value, clicked }) => {
    console.log(value);
    passCompany(value);
  };

  const handleBankCodeSelect = ({ value, clicked }) => {
    passBank(value);
  }

  const handleCurrent = (event) => {
    const { checked } = event.target;
    setIsCurrentDateChecked(checked);
    passCurrent(checked ? 1 : 0);
    console.log(checked);
  };

  const regionList = [
    'Addis Ababa',
    'Afar',
    'Amhara',
    'Benishangul-Gumuz',
    'Dire Dawa',
    'Gambela',
    'Harari',
    'Oromia',
    'Sidama',
    'Somali',
    'South West Ethiopia Peoples',
    'Tigray',
    'Central Ethiopia',
    'South Ethiopia',
  ];

  const componentList = [
    {
      name: 'Site',
      component: <SearchBar onClientSelect={handleClientSelect} />,
    },
    {
      name: 'DateDiffrence',
      component: (
        <DateDiffrenceComponent
          labelOne="fromDate"
          labelTwo="toDate"
          onDateChange={handleDateChanges}
        />
      ),
    },
    {
      name: 'fullYear',
      component: <YearDropDown onSelectedYear={handleSelectedYear} />,
    },
    {
      name: 'Tax Center',
      component: <TaxCenterDropDown onSelectedCenter={handleTaxSelect} />,
    },
    {
      name: 'Bank Name List',
      component: <BankCodeDropDown onSelectedCenter={handleBankCodeSelect} />,
    },
    {
      name: 'Current Date',
      component: (
        <FormControlLabel
          control={
            <Checkbox checked={isCurrentDateChecked} onChange={handleCurrent} />
          }
          label="Current Date"
        />
      ),
    },
    {
      name: 'Region',
      component: (
        <TextField
          select
          //error={!errors.region == ''}
          id="region"
          margin="normal"
          fullWidth
          label="Region"
          name="region"
          InputLabelProps={{
            color: 'success',
          }}
          value={region}
          onChange={(e) => handleRegionSelect(e.target.value)}
          //helperText={errors.region ? errors.region[0] : null}
        >
          {regionList.map((region) => (
            <MenuItem key={region} value={region}>
              {region}
            </MenuItem>
          ))}
        </TextField>
      ),
    },
  ];

  return (
    <div style={{ display: 'flex' }}>
      <div>
        <FormControl sx={{ m: 1, width: 300 }}>
          <InputLabel id="demo-multiple-checkbox-label">
            Select Components
          </InputLabel>
          <Select
            labelId="demo-multiple-checkbox-label"
            id="demo-multiple-checkbox"
            multiple
            value={selectedComponents}
            onChange={handleChange}
            input={<OutlinedInput label="Select Filtering Methods" />}
            renderValue={(selected) => selected.join(', ')}
            MenuProps={MenuProps}
          >
            {componentList.map((component) => (
              <MenuItem key={component.name} value={component.name}>
                <Checkbox
                  checked={selectedComponents.indexOf(component.name) > -1}
                />
                <ListItemText primary={component.name} />
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </div>
      <Grid container spacing={1}>
        {componentList.map((component) => {
          if (selectedComponents.includes(component.name)) {
            return (
              <Grid item xs={12} sm={6} md={4} key={component.name}>
                {component.component}
              </Grid>
            );
          }
          return null;
        })}
      </Grid>
    </div>
  );
};

export default FilterComponent;
