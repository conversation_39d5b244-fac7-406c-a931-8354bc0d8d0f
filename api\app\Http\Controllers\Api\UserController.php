<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\EmployeeResource;
use App\Models\User;
use App\Http\Requests\StoreUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // $user->load('userPermission');
        // return UserResource::collection(User::all());
        return response(User::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreUserRequest  $request
     * @return \Illuminate\Http\Response
     * @return  \App\Http\Resources\UserResource
     */
    public function store(StoreUserRequest $request)
    {
        $data = $request->validated();
        $imagePath = '';
        if ($request->hasFile('image')) {

            $imagePath = $request->file('image')->store('public/images');
        }


        $user = User::create([
            'firstName' => $data['firstName'],
            'lastName' => $data['lastName'],
            'username' => $data['username'],
            'role' => $data['role'],
            'phone' => $data['phone'],
            'email' => $data['email'],
            'image' => $imagePath ? $imagePath : null,
            'password' => bcrypt($data['password']),
        ]);
        $user->load('userPermission');
        return new UserResource($user);
        // return response($user);
    }

    /**
     * Display the specified resource.
     * 
     * @param  \App\Models\User  $user
     * @return \App\Http\Resources\UserResource
     */
    public function show(User $user)
    {
        $user->load('userPermission');
        return new UserResource($user);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateUserRequest  $request
     * @param  \App\Models\User  $user
     * @return \App\Http\Resources\UserResource
     */
    public function update(UpdateUserRequest $request, User $user)
    {
        $data = $request->validated();

        $user->update($data);

        $user->load('userPermission');
        return new UserResource($user);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function destroy(User $user)
    {
        $user->delete();

        return response('', 204);
    }
}
