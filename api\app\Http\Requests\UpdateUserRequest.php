<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'firstName' => 'required|string|max:55',
            'lastName' => 'required|string|max:55',
            'username'=>[
                'required',
                'string',
                Rule::unique('users', 'username')->ignore($this->user),
              ],  
            'phone' => 'required',
            'role' => 'required|string',
            'email' => 'required|string|email|unique:users,email,' . $this->id,
        ];
    }
}
