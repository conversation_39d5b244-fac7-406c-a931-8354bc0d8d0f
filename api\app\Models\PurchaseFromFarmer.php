<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseFromFarmer extends Model
{
    use HasFactory;

    protected $fillable = [
        'farmer_id',
        'coffee_batch_id',
        'coffee_type',
        'quantity_kg',
        'price_per_kg',
        'total_amount',
        'purchase_date',
    ];

    public function farmer()
    {
        return $this->belongsTo(Farmer::class);
    }

    public function coffeeBatch()
    {
        return $this->belongsTo(CoffeeBatch::class);
    }
}
