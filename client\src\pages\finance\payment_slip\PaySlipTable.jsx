import React from "react";
import edomiasLogo from "../../../assets/edomias_log.png";
import { Box, Typography, colors, useTheme } from "@mui/material";
import { tokens } from "../../../utils/theme";

const CustomTypography = ({ children, isbold }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  return (
    <Typography
      fontSize="11px"
      fontWeight={isbold ? "bold" : undefined}
      color={colors.grey[100]}
    >
      {children}
    </Typography>
  );
};
export const PaySlipTable = ({ employee ,date}) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const payrolldate=new Date(date);
  const formattedDate=payrolldate.toLocaleDateString('en-US',{year:"numeric",month:'long'})
  return (
    <Box alignContent="center" alignItems="center" mb={1}>
      <Box display="flex" justifyContent="center">
        <img
          src={edomiasLogo}
          alt="logo"
          color={colors.grey[100]}
          width="40px"
          height="40px"
        />
        <Typography style={{ fontSize: "24px", fontWeight: "bold" }}>
          Edomias International PLC
        </Typography>
      </Box>
      <Typography variant="h4"  display="flex" justifyContent="center">
        Payroll Slip for {formattedDate}
      </Typography>
      <Box display="flex" justifyContent="start">
        <table border="1px solid black" style={{width:'100%'}}>
          <thead></thead>
          <tbody>
            <tr>
              <td style={{ width: "40%" }}>
                <CustomTypography children="fullName" isbold={true} />
              </td>
              <td colSpan={7} style={{ width: "60%" }}>
                <CustomTypography
                  children={`${employee.first_name} ${employee.middle_name} ${employee.last_name}`}
                  isbold={true}
                />
              </td>
            </tr>
            <tr>
              <td>
                <CustomTypography children="Position" isbold={true} />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={employee.company_name}
                  isbold={true}
                />
              </td>
            </tr>
            <tr>
              <td>
                <CustomTypography
                  children={`Pc-rate work type:${employee.pc_type}`}
                 
                />
              </td>
              <td colSpan={3}>
                <CustomTypography
                  children={`NoPc:${employee.no_pc ?? 0}`}
                 
                />
              </td>
              <td colSpan={3}>
                <CustomTypography
                  children={`pcRate:${employee.pc_rate}`}
                 
                />
              </td>
              <td colSpan={3}>
                <CustomTypography
                  children={`pcSalary:${employee.no_pc * employee.pc_rate}`}
                 
                />
              </td>
            </tr>
            <tr>
              <td align="right">
                <CustomTypography children="additional" isbold={true} />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.additional ?? 0}`}
                  isbold={true}
                />
              </td>
            </tr>
            <tr>
              <td align="right">
                <CustomTypography children="basicSalary" isbold={true} />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.basic_salary ?? 0}`}
                 
                />
              </td>
            </tr>
          </tbody>
        </table>
        <table border="1px solid black">
          <thead></thead>
          <tbody>
          <tr>
              <td align="right">
                <CustomTypography children="TotalbasicSalary" isbold={true} />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.total_basic_salary ?? 0}`}
                  isbold={true}
                />
              </td>
            </tr>
            <tr>
              <td align="right">
                <CustomTypography children="Overtime(OT)"  />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.over_time ?? 0}`}
                 
                />
              </td>
            </tr>
            <tr>
              <td align="right">
                <CustomTypography children="Incentives" />
              </td>
              <td colSpan={7}>{0}</td>
            </tr>
            <tr>
              <td>
                <CustomTypography
                  children="Transport allowance"
                 
                />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.transport_allowance}`}
                 
                />
              </td>
            </tr>
            <tr>
              <td>
                <CustomTypography
                  children="Other allowance"
                 
                />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.other_transport_allowance}`}
                 
                />
              </td>
            </tr>
          </tbody>
        </table>
        <table border='1px solid black'>
          <thead></thead>
          <tbody>
          <tr>
              <td>
                <CustomTypography
                  children="Dsa Allowance"
                 
                />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.tax_dsa_allowance}`}
                 
                />
              </td>
            </tr>
            <tr>
              <td>
                <CustomTypography children="Medical Allowance" isbold={true} />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.nontax_medical_allowance}`}
                  isbold={true}
                />
              </td>
            </tr>
            <tr>
              <td>
                <CustomTypography
                  children="Non Taxable(TA,DA,PA,OA,MA,CA,DSA & AL)"
                  isbold={true}
                />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.nontax_allowance}`}
                  isbold={true}
                />
              </td>
            </tr>
            <tr>
              <td>
                <CustomTypography children="taxable salary" isbold={true} />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.gross_salary}`}
                  isbold={true}
                />
              </td>
            </tr>
            <tr>
              <td>
                <CustomTypography children="gross salary" isbold={true} />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.gross_salary}`}
                  isbold={true}
                />
              </td>
            </tr>
          </tbody>
        </table>
        <table border='1px solid orange'>
          <thead></thead>
          <tbody>
          <tr>
              <td>
                <CustomTypography
                  children="Income Tax"
                 
                />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.income_tax}`}
                 
                />
              </td>
            </tr>
            <tr>
              <td>
                <CustomTypography
                  children="Pension 7%"
                 
                />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.pension_emp}`}
                 
                />
              </td>
            </tr>
            <tr>
              <td>
                <CustomTypography
                  children="diffrentDeduction"
                 
                />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.deffrent_deduction}`}
                 
                />
              </td>
            </tr>
            <tr>
              <td>
                <CustomTypography children="TotalDeduction" isbold={true} />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.total_deduction}`}
                  isbold={true}
                />
              </td>
            </tr>
            <tr>
              <td>
                <CustomTypography children="netPay" isbold={true} />
              </td>
              <td colSpan={7}>
                <CustomTypography
                  children={`${employee.net_pay}`}
                  isbold={true}
                />
              </td>
            </tr>
          </tbody>
        </table>
      </Box>
    </Box>
  );
};
