import React from "react";
import Header from "../../../components/Header";
import {
  Button,
  Typography,
  useTheme,
  Box,
  CircularProgress,
  Alert,
} from '@mui/material';
import { DataGrid } from "@mui/x-data-grid";
import { tokens } from "../../../utils/theme";
import api from "../../../api/config/axiosConfig";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useStateContext } from "../../../context/ContextProvider";
function OverTimeTakenList({ emp_basic_id, selectedRow }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const queryclinet = useQueryClient();
  const { setNotification } = useStateContext();
  const {
    data: overtime,
    isFetched,
    isLoading,
  } = useQuery(
    ["overtime", emp_basic_id],
    async () =>
      await api.get(`overtime/${emp_basic_id}`).then(({ data }) => data),
    {
      staleTime: 60000,
      enabled: !!emp_basic_id,
    }
  );

  

  const columns = [
    {
      field: "id",
      headerName: "ID",
      flex: 0.2,
    },
    {
      field: "start_date",
      headerName: "StartDate",
      flex: 0.5,
    },
    {
      field: "end_date",
      headerName: "To Date",
      flex: 0.5,
    },
    {
      field: "hours",
      headerName: "no of days or hours worked",
      flex: 0.8,
    },
    {
      field: "minutes",
      headerName: "minutes",
      flex: 0.5,
    },
    {
      field: "rate_type",
      headerName: "Rate Type",
      flex: 0.5,
    },

    {
      field: "day_type",
      headerName: "Day Type",
      flex: 0.5,
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 0.7,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          selectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "delete",
      headerName: "Delete",
      flex: 0.8,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deletOvertime = useMutation(
          (id) => api.delete(`overtime/${id}`),
          {
            onSuccess: () => {
              queryclinet.invalidateQueries({ queryKey: "overtime" });
              setNotification("overtime record deleted successfully");
            },
          }
        );
        const deleteOverTime = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              "Are you sure you want to delete the selected  overtime record?"
            )
          ) {
            return;
          }

          deletOvertime.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deleteOverTime}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];

  return (
    <Box>
      <Header title="list of employee overtime record" heading="h5" />
      {isLoading && <CircularProgress />}
      {isFetched && (
        <>
          {overtime.overTimeCount > 19 && (
            <Alert variant="filled" severity="warning">
              This Employee has more than 19 unpaid overtime.
            </Alert>
          )}

          <DataGrid
            columns={columns}
            rows={overtime.overTimeData}
            hideFooter={true}
            autoHeight={true}
          />
        </>
      )}
    </Box>
  );
}

export default OverTimeTakenList;
