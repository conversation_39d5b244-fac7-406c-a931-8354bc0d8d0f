<?php

namespace App\Http\Controllers\Api\finance;

use App\Http\Controllers\Controller;
use App\Http\Resources\AbsenceResource;
use App\Models\finance\AbsenceList;
use Illuminate\Http\Request;

class AbsenceListController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index()
    {
        return AbsenceResource::collection(AbsenceList::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \App\Http\Resources\AbsenceResource
     */
    public function store(Request $request)
    {
        $data = AbsenceList::create([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'client_id' => $request->input('client_id'),
            'start_date' => $request->input('start_date'),
            'end_date' => $request->input('end_date'),
            'hour_day' => $request->input('hour_day'),
            'minutes' => $request->input('minutes'),
            'rate_type' => $request->input('rate_type'),
            'penality_rate' => $request->input('penality_rate'),
            'penalized' => 0,
            'absence_remark' => $request->input('absence_remark'),
            'user_id' => $request->input('user_id'),
        ]);

        return  new AbsenceResource($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\finance\AbsenceList  $absenceList
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function show($id)
    {
        $absenceList=AbsenceList::where('emp_basic_id','=',$id)->get();
        return AbsenceResource::collection($absenceList);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\finance\AbsenceList  $absenceList
     * @return \App\Http\Resources\AbsenceResource
     
     */
    public function update(Request $request,$id)
    {

        $absenceList= AbsenceList::find($id);

        $absenceList->update([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'client_id' => $request->input('client_id'),
            'start_date' => $request->input('start_date'),
            'end_date' => $request->input('end_date'),
            'hour_day' => $request->input('hour_day'),
            'minutes' => $request->input('minutes'),
            'rate_type' => $request->input('rate_type'),
            'penality_rate' => $request->input('penality_rate'),
            'penalized' => 0,
            'absence_remark' => $request->input('absence_remark'),
            'user_id' => $request->input('user_id'),
        ]);
        return new AbsenceResource($absenceList);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\finance\AbsenceList  $absenceList
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $absenceList=AbsenceList::find($id);
        $absenceList->delete();
        return response('',204);
    }
}
