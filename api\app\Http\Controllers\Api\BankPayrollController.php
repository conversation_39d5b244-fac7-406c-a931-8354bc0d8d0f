<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\settings\PayrollDate;
use Illuminate\Support\Facades\Log;

class BankPayrollController extends Controller
{
    public function getBankPayroll(Request $request)
    {
        //Log::info('Request received at BankPayrollController');
        //Log::info('Request parameters: ' . json_encode($request->all()));

        $firstDate = $request->input('firstDate');
        $lastDate = $request->input('lastDate');
        $clientId = $request->input('clientId');
        $year = $request->input('year');
        $status = $request->input('status');
        $taxCenter = $request->input('taxCenter');
        $company = $request->input('company');
        $currentPayrollDate = $request->input('current');
        $region = $request->input('region');
        $bankCode = $request->input('bankCode');

        $nextYear = Carbon::parse($year)->addYear()->format('Y-m-d');

        //Log::info('Next year: ' . $nextYear);

        $query = DB::table('payrolls')
        ->join('emp_basics', 'payrolls.emp_basic_id', '=', 'emp_basics.id')
        ->join('clients', 'payrolls.client_id', '=', 'clients.id')
        ->join('users', 'payrolls.user_id', '=', 'users.id')
        ->leftJoin('emp_banks', 'emp_banks.emp_basic_id', '=', 'emp_basics.id')
        ->where('emp_basics.termination_status', '=', 0)
        ->select(
            'emp_basics.first_name',
            'emp_basics.id as employee_id',
            'payrolls.id',
            'emp_basics.middle_name',
            'emp_basics.last_name',
            DB::raw("CONCAT_WS(' ', emp_basics.first_name, emp_basics.middle_name, emp_basics.last_name) as fullname"),
            'emp_basics.gender',
            DB::raw("CAST(emp_banks.bank_account AS CHAR) as bank_account"),
            'emp_banks.bank_code',
            'emp_banks.bank_branch',
            'clients.company_name',
            'clients.company_code',
            'clients.tax_center',
            'payrolls.net_pay',
            'payrolls.id as payid',
            DB::raw("CONCAT_WS(' ', users.firstName, users.lastName) as userName"),
        );

        //Log::info('Query: ' . $query->toSql());

        $payrollDate = PayrollDate::latest()->first();

        //Log::info('Payment date: ' . $payrollDate);

        // Apply conditions based on input parameters
        if ($status != 1) {
            // Apply additional conditions only if status is not 1
            if ($firstDate && $lastDate) {
                $query->whereBetween('payrolls.pay_date', [$firstDate, $lastDate]);
            }
            if ($clientId) {
                $query->where('payrolls.client_id', $clientId);
            }
            if ($company) {
                $query->where('clients.company_code', 'like', $company);
            }
            if ($taxCenter) {
                $query->where('clients.tax_center', $taxCenter);
            }
            if ($year) {
                $query->whereBetween('payrolls.pay_date', [$year, $nextYear]);
            }
            if ($currentPayrollDate != 0) {
                //Log::info('Current payroll date: ' . $currentPayrollDate);
                $payrollStart = $payrollDate['start_date'];
                $payrollEnd = $payrollDate['end_date'];
                //Log::info('Current payroll date: ' . $payrollStart . " " . $payrollEnd);
                $query->whereBetween('payrolls.pay_date', [$payrollStart, $payrollEnd]);
            }
            if ($region) {
                $query->where('clients.region', 'like', "%$region%");
            }
            if ($bankCode) {
                $query->where('emp_banks.bank_code', $bankCode);
            }

        }

        $results = $query->get();

        //Log::info('Results: ' . json_encode($results));

        return response($results);
    }

}
