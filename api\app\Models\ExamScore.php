<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExamScore extends Model
{
    use HasFactory;

    protected $table = 'exam_scores';

    protected $fillable = [
        'applicant_id',
        'exam_date',
        'score',
    ];

    public function applicant()
    {
        return $this->belongsTo(Applicant::class);
    }
}