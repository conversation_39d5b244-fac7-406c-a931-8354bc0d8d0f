<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\HireRequisition;
use Illuminate\Http\Request;

class HireRequisitionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return HireRequisition::all();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'requisition_date' => 'required|date',
            'department' => 'required|string',
            'position' => 'required|string',
            'salary_level' => 'required|string',
            'salary_range_from' => 'required|numeric',
            'salary_range_to' => 'required|numeric',
            'approved' => 'boolean',
            'approved_by' => 'nullable|exists:users,id',
        ]);

        $hireRequisition = HireRequisition::create($validatedData);
        return $hireRequisition;
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        return HireRequisition::find($id);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $hireRequisition = HireRequisition::find($id);

        $validatedData = $request->validate([
            'requisition_date' => 'sometimes|required|date',
            'department' => 'sometimes|required|string',
            'position' => 'sometimes|required|string',
            'salary_level' => 'sometimes|required|string',
            'salary_range_from' => 'sometimes|required|numeric',
            'salary_range_to' => 'sometimes|required|numeric',
            'approved' => 'sometimes|boolean',
            'approved_by' => 'nullable|exists:users,id',
        ]);

        $hireRequisition->update($validatedData);
        return $hireRequisition;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        return HireRequisition::destroy($id);
    }
}
