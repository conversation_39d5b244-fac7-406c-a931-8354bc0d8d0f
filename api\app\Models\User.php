<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Models\settings\UserPermission;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'firstName',
        'lastName',
        'username',
        'image',
        'phone',
        'role',
        'email',
        'password',
    ];
    // protected $guarded = [];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function userPermission()
    {
        return $this->hasMany(UserPermission::class);
    }
    protected static function boot()
    {
        parent::boot();
        static::created(function ($user) {
            if ($user->role == 'admin') {
                $user->userPermission()->create([
                    'routes' => json_encode(['/dashboard', 'reset_passwords', 'settings', 'settings/users/:id', 'dashboard', '/user/change-password', 'reports', 'settings', 'settings/tax-center', 'settings/terminate', 'settings/pcrate-type', 'settings/users', 'settings/users/new', 'settings/users/:id', 'employees', 'employees/edit', 'employees/:id', 'employees/new', 'employees/position', '/employees/assign', '/employees/transfer', '/employees/transfer/:id', '/employees/leave', '/employees/leaves', '/employees/add-leaves', '/employees/add-leaves/:id', '/employees/leave-detail', '/employees/leave-detail-two', '/employees/leave-summary', '/employees/leave-summary-lookup', 'clients', 'clients/list', 'clients/deduct', 'clients/new', 'clients/:id', '/clients/show/:id', 'finance', 'finance/draft', 'finance/overtime', 'finance/absence', 'finance/additional', 'finance/pcrate', 'finance/additionalpcrate', 'finance/dsa', 'finance/nontaxable', 'finance/setdeductable', 'finance/add-deductable', 'finance/dailyrate', '/finance/payroll', '/finance/slip', '/finance/slip/list', '/finance/payroll/calc', '/clients/payment-follow-up', '/clients/contract-follow-up','/settings/users/reset-password',"operations","/finance/sevrenace"]),
                    'roles' => $user->role,
                ]);
            } else if ($user->role == 'storekeeper') {
                $user->userPermission()->create([
                    'routes' => json_encode(['/dashboard', '/user/change-password', 'settings/category', 'store/items', 'store/stock', 'store/requests']),
                    'roles' => $user->role,
                ]);
            } else {
                $user->userPermission()->create([
                    'routes' => json_encode(['/dashboard', 'reset_passwords']),
                    'roles' => $user->role,
                ]);
            }
        });
    }
}
