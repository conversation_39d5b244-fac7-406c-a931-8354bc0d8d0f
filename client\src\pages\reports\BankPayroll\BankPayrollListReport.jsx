import { useQuery } from "@tanstack/react-query";
import React from "react";
import { useLocation } from "react-router-dom";
import api from "../../../api/config/axiosConfig";
import { Box } from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import _CircularProgress from "../../../components/_CircularProgress";
import CustomToolBar from "../../../helper/CustomToolBar";
import Header from "../../../components/Header";
import { useState } from "react";
import {formatedMessage}from "../formattedMessage"
const BankPayrollListReport = () => {
  const location = useLocation();
  const recivedData = location.state;
  const [companyName, setCompanyName] = useState("");
  const [region, setRegion] = useState("");

  const {
    data: response,
    isLoading,
    isFetched,
  } = useQuery(["bankpayroll"], () =>
    api.get("bankpayroll", { params: recivedData }).then(({ data }) => {

      //console.log('received data',recivedData);
      //console.log('Data',data);
      if (data.length > 0) {
        setCompanyName(data[0].company_name);
        setRegion(recivedData.region);
      }
      return data;
    })
  );
  const columns = [
    {
      field: "id",
      headerName: "ID",
    },
    {
      field: "first_name",
      headerName: "First Name",
      flex: 1,
    },
    {
      field: "middle_name",
      headerName: "Middle Name",
      flex: 1,
    },
    {
      field: "last_name",
      headerName: "Last Name",
      flex: 1,
    },
    {
      field: "bank_account",
      headerName: "Bank Account",
      flex: 1,
    },
    {
      field: "bank_code",
      headerName: "Bank Name",
      flex: 1,
    },
    {
      field: "bank_branch",
      headerName: "Bank Branch",
      flex: 1,
    },
    {
      field: "net_pay",
      headerName: "Net Payment",
      flex: 1,
      cellClassName: "custom-pay",
      headerClassName: "custom-pay",
    },
  ];
  
  return (
    <Box margin="10px">
      {recivedData && recivedData.clientId  ? (
        <Header
          title={`${companyName} BANK PAYROLL REPORT `}
          subtitle={formatedMessage(recivedData.firstDate,recivedData.lastDate,recivedData.year)}
        />
      ) : (
        <Header title="BANK PAYROLL REPORT" subtitle={formatedMessage(recivedData.firstDate,recivedData.lastDate,recivedData.year)} />
      )}

      {recivedData.region && (
        <Header
          title={`${ region } BANK PAYROLL REPORT `}
          subtitle={formatedMessage(recivedData.firstDate, recivedData.lastDate, recivedData.year)}
        />
      )}

      {isLoading && <_CircularProgress />}
      {isFetched && (
        <DataGrid
          columns={columns}
          rows={response}
          hideFooter
          autoHeight
          components={{ Toolbar: CustomToolBar }}
        />
      )}
    </Box>
  );
};

export default BankPayrollListReport;
