<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Models\Certification;
use Illuminate\Http\Request;

class CertificationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return Certification::with('coffeeBatch')->latest()->paginate();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'coffee_batch_id' => 'required|exists:coffee_batches,id',
            'certificate_number' => 'required|string|unique:certifications|max:255',
            'certification_type' => 'required|string|max:255',
            'issue_date' => 'nullable|date',
            'expiry_date' => 'nullable|date',
            'certifying_body' => 'nullable|string|max:255',
        ]);

        return Certification::create($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Certification  $certification
     * @return \Illuminate\Http\Response
     */
    public function show(Certification $certification)
    {
        return $certification->load('coffeeBatch');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Certification  $certification
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Certification $certification)
    {
        $data = $request->validate([
            'coffee_batch_id' => 'sometimes|required|exists:coffee_batches,id',
            'certificate_number' => 'sometimes|required|string|unique:certifications,certificate_number,' . $certification->id . '|max:255',
            'certification_type' => 'sometimes|required|string|max:255',
            'issue_date' => 'nullable|date',
            'expiry_date' => 'nullable|date',
            'certifying_body' => 'nullable|string|max:255',
        ]);

        $certification->update($data);

        return $certification;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Certification  $certification
     * @return \Illuminate\Http\Response
     */
    public function destroy(Certification $certification)
    {
        $certification->delete();

        return response()->noContent();
    }
}
