export const formatedMessage = (firstDate = "", lastDate = "", year = "") => {
  let message = "";
  if (firstDate && lastDate) {
    const formatedDate = new Date(firstDate).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
    });
    message = `${formatedDate} PAYROLL REPORT FOR A BANK`;
  } else if (year) {
    const date = new Date(year).toLocaleDateString("en-US", {
      year: "numeric",
    });
    message = `${date} PAYROLL REPORT FOR A BANK`;
  }
  return message;
};
export const formatedMessageTermination = (
  firstDate = "",
  lastDate = "",
  year = ""
) => {
  let message = "";
  if (firstDate && lastDate) {
    const formatedDate = new Date(firstDate).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
    });
    message = `${formatedDate} TERMINATED EMPLOYEE REPORT`;
  } else if (year) {
    const date = new Date(year).toLocaleDateString("en-US", {
      year: "numeric",
    });
    message = `${date} TERMINATED EMPLOYEE REPORT`;
  }
  return message;
};
export const incomeTaxMessage = (
  firstDate = "",
  lastDate = "",
  center = ""
) => {
  let message = "";
  if (firstDate && lastDate) {
    const formatedDate = new Date(firstDate).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
    });
    message = `${formatedDate} INCOME TAX REPORT`;
  } else if (center) {
    message = `${center} TAX CENTER INCOME TAX REPORT`;
  } else if (firstDate && lastDate && center) {
    const date = new Date(firstDate).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
    });
    message = `${date} INCOME TAX REPORT IN ${center} Tax CENTER`;
  }
  return message;
};
export const pensionTaxMessage = (
  firstDate = "",
  lastDate = "",
  center = ""
) => {
  let message = "";
  if (firstDate && lastDate) {
    const formatedDate = new Date(firstDate).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
    });
    message = `${formatedDate} PENSION TAX REPORT`;
  } else if (center) {
    message = `${center} TAX CENTER PENSION TAX REPORT`;
  } else if (firstDate && lastDate && center) {
    const date = new Date(firstDate).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
    });
    message = `${date} PENSION TAX REPORT IN ${center} Tax CENTER`;
  }
  return message;
};
const formateDate = (date) => 
   new Date(date).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
  });
const formatYear=(year)=>new Date(year).toLocaleDateString("en-US", {
  year: "numeric",
});
export const employeeReportTaxMessage = (
  firstDate = "",
  lastDate = "",
  fullYear = "",
  gender = "",
  region = ""
) => {
  let message = "";
  const date1 = formateDate(firstDate);
  const date2 = formateDate(lastDate);
  const year=formatYear(fullYear)
  if (firstDate && gender && region) {
    message = `${gender.toUpperCase()} EMPLOYEE REPORT  From ${date1} TO ${date2} FOUND IN ${region} REGION`;
  } else if (firstDate && gender) {
    message = `${gender.toUpperCase()} EMPLOYEE REPORT  From ${date1} TO ${date2}`;
  } else if (firstDate && region) {
    message = `EMPLOYEE REPORT  FROM ${date1} TO ${date2} FOUND IN ${region} REGION`;
  } else if (gender && region && fullYear) {
    
    message = `${year} ${gender.toUpperCase()} EMPLOYEE REPORT FOUND IN ${region} REGION`;
  }
   else if (gender && region) {
    message = `${gender.toUpperCase()} EMPLOYEE REPORT FOUND IN ${region} REGION`;
  } 
   else if (gender && fullYear) {
    message = `${year} ${gender.toUpperCase()} EMPLOYEE REPORT`;
  } 
   else if (fullYear && region) {
    message = `${year} EMPLOYEE REPORT FOUND IN ${region} REGION`;
  } 
   else if (gender) {
    message = `${gender.toUpperCase()} EMPLOYEE REPORT`;
  } 
  else if (region) {
    message = ` EMPLOYEE REPORT FOUND IN ${region} REGION`;
  } 
  else if (fullYear) {
    message = `${year} EMPLOYEE REPORT `;
  } 
  else if (firstDate) {
    message = `EMPLOYEE REPORT  From ${date1} TO ${date2}`;
  }
  return message;
};
