import { FormControl, InputLabel, TextField } from "@mui/material";
import React from "react";
function CustomTextField(props) {
  const { label, ...rest } = props;
  return (
    <FormControl
      sx={{ display: "flex", flexDirection: "row", alignItems: "center" }}
    >
      <InputLabel sx={{ mr: 1 }}>{label}</InputLabel>
      <TextField {...rest} />
    </FormControl>
  );
}

export default CustomTextField;
