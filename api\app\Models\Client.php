<?php

namespace App\Models;

use App\Models\finance\Payroll;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Client extends Model
{
    use HasFactory;
    protected $guarded = [];

    public function contracts()
    {
        return $this->hasMany(ContractFollowUp::class);
    }
    public function payments()
    {
        return $this->hasMany(PaymentFollowUp::class);
    }

    public function assign()
    {
        return $this->hasOne(Assign::class);
    }
}
