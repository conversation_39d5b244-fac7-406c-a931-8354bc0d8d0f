import React from "react";
import { <PERSON><PERSON>, Box, TextField, MenuItem, Button } from "@mui/material";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { useStateContext } from "../../../../context/ContextProvider";
import { useEffect } from "react";
import api from "../../../../api/config/axiosConfig";

const DisciplineForm = ({empId, data }) => {
  const { errors, setErrors, setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    emp_basic_id: "",
    record_type: "",
    subject: "",
    reason: "",
    to_date: "",
    ref_no: "",
  });
  if (data) {
    useEffect(() => {
      setFormData(data);
    }, [data]);
  }
  const updateDiscipline = useMutation(
    (formdata) => api.put(`discipline/${formdata.id}`, formdata),
    {
      onSuccess: () => {
        setNotification("discipline update successfully");
        queryClient.invalidateQueries({ queryKey: "discipline" });
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );
  const addDiscipline = useMutation(
    (payload) => api.post("discipline", payload),
    {
      onSuccess: () => {
        setNotification("discipline created successfully");
        queryClient.invalidateQueries({ queryKey: "discipline" });
      },
      onError: (err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      },
    }
  );

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  const handleSubmit = (e) => {
    e.preventDefault();

    const form = new FormData(e.currentTarget);
    if (data && data.id) {
      updateDiscipline.mutate(formData);
    } else {
      const payload = {
        emp_basic_id: empId,
        record_type: form.get("record_type"),
        subject: form.get("subject"),
        reason: form.get("reason"),
        ref_no: form.get("ref_no"),
        to_date: form.get("to_date"),
      };
      addDiscipline.mutate(payload);
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.record_type == ""}
            margin="normal"
            label="Recored Type"
            name="record_type"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.record_type}
            helperText={errors.record_type ? errors.record_type[0] : null}
          />
        </Grid>
      
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.ref_no == ""}
            margin="normal"
            label="Reference Number"
            name="ref_no"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.ref_no}
            helperText={errors.ref_no ? errors.ref_no[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.to_date == ""}
            margin="normal"
            label="To Date"
            name="to_date"
            fullWidth
            variant="standard"
            type="date"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.to_date}
            helperText={errors.to_date ? errors.to_date[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={3} sm={4}>
          <TextField
            error={!errors.subject == ""}
            margin="normal"
            label="Subject"
            multiline 
            maxRows={4}
            name="subject"
            fullWidth
            variant="outlined"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.subject}
            helperText={errors.subject ? errors.subject[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={3} sm={4}>
          <TextField
            error={!errors.reason == ""}
            margin="normal"
            label="Reason"
            multiline 
            maxRows={4}
            name="reason"
            fullWidth
            variant="outlined"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.reason}
            helperText={errors.reason ? errors.reason[0] : null}
          />
        </Grid>
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
          {data && data.id ? <div>update</div> : <div>create</div>}
        </Button>
      </Box>
    </Box>
  );
};

export default DisciplineForm;
