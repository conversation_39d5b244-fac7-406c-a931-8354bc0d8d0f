import React from "react";
import Header from "../../../components/Header";
import {
  Box,
  Paper,
  Typography,
  useTheme,
  Grid,
  Button,
  TextField,
} from "@mui/material";
import { tokens } from "../../../utils/theme";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";
import SearchBar from "../../../components/SearchBar";
import EmployeeSelecter from "../../../components/EmployeeSelecter";
import OverTimeForm from "./OverTimeForm";
import OverTimeTakenList from "./OverTimeTakenList";

const SearchComponent = ({ ClientSearch, EmployeeSelecter }) => {
  return (
    <Grid container spacing={1}>
      <Grid item xs={12} sm={7}>
        {ClientSearch}
      </Grid>
      <Grid item xs={12} sm={5}>
        {EmployeeSelecter}
      </Grid>
    </Grid>
  );
};

function OverTime() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [clicked, setClicked] = useState(false);
  const [editData, setEditData] = useState({});
  const [formData, setFormData] = useState({
    client_id: "",
    emp_basic_id: "",
  });

  const handleClietSelect = ({value}) => {
    setFormData({
      ...formData,
      client_id: value.id,
    });
  };

  const handleEmployeeSelect = ({ value, clicked }) => {
    
    setFormData({
      ...formData,
      emp_basic_id: value.id,
    });

    setClicked(clicked);
  };
  const onSelectedRow = (value) => {
    setEditData(value);
  };
  const { data: assignedEmployee ,isFetched} = useQuery(
    ["assignedEmployee", formData.client_id],
    async () =>
      await api
        .get(`assign/client/${formData.client_id}`)
        .then(({ data }) => data.data),
    {
      enabled: !!formData.client_id,
    }
  );
  
  
  return (
    <Box m="5px">
      <Header title="Add overtime" subtitle="add overtime here" />
      <Box>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4}>
            <Paper
              sx={{
                padding: '1rem',
                color: colors.grey[100],
                background: colors.primary[400],
              }}
            >
              <SearchComponent
                ClientSearch={<SearchBar onClientSelect={handleClietSelect} />}
                EmployeeSelecter={
                  <EmployeeSelecter
                    assignedEmployee={assignedEmployee}
                    isFetched={isFetched}
                    onEmployeeSelect={handleEmployeeSelect}
                  />
                }
              />
            </Paper>
          </Grid>
          <Grid item xs={12} sm={8}>
            {clicked && (
              <>
                <Paper
                  sx={{
                    padding: '1rem',
                    color: colors.grey[100],
                    background: colors.primary[400],
                  }}
                >
                  <OverTimeForm
                    formdata={formData}
                    editData={editData}
                    setEditData={setEditData}
                  />
                </Paper>
                <Paper
                  sx={{
                    marginTop: '30px',
                    padding: '1rem',
                    color: colors.grey[100],
                    background: colors.primary[400],
                  }}
                >
                  <OverTimeTakenList
                    emp_basic_id={formData.emp_basic_id}
                    selectedRow={onSelectedRow}
                  />
                </Paper>
              </>
            )}
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
}

export default OverTime;
