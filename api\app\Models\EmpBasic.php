<?php

namespace App\Models;

use App\Models\finance\AddPcrate;
use App\Models\finance\EmployeeDeductable;
use App\Models\finance\OverTime;
use App\Models\finance\AbsenceList;
use App\Models\finance\Additional;
use App\Models\finance\PcRate;
use App\Models\finance\AdditionalDeductable;
use App\Models\finance\Dsa;
use App\Models\finance\DailyRate;
use App\Models\finance\Payroll;
use Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class EmpBasic extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function empDetail()
    {
        return $this->HasOne(EmpDetail::class);
    }
    public function empBank()
    {
        return $this->HasOne(EmpBank::class);
    }
    public function empAllowance()
    {
        return $this->hasOne(EmpAllowance::class);
    }
    public function empLoan()
    {
        return $this->hasOne(EmployeeLoan::class);
    }
    public function empSponser()
    {
        return $this->hasOne(EmployeeSponser::class);
    }

    public function empStatus()
    {
        return $this->hasOne(EmpStatus::class);
    }
    public function empEducation()
    {
        return $this->hasMany(Education::class);
    }
    public function empWorkExperiance()
    {
        return $this->hasMany(WorkExperiance::class);
    }
    public function empEmergencyContact()
    {

        return $this->hasOne(EmpEmergencyContact::class);
    }
    public function language()
    {
        return $this->hasOne(Language::class);
    }
    public function familyInfo()
    {
        return $this->hasOne(FamilyInfo::class);
    }
    public function sponsoredEmployee()
    {
        return $this->hasOne(SponsoredEmployee::class);
    }
    public function award()
    {
        return $this->hasMany(Award::class);
    }

    public function  discipline()
    {

        return $this->hasMany(Discipline::class);
    }

    public function overtime()
    {
        return $this->hasMany(OverTime::class);
    }
    public function absence()
    {
        return  $this->hasMany(AbsenceList::class);
    }
    public function pcrate()
    {
        return $this->hasMany(PcRate::class);
    }
    public function additional()
    {
        return $this->hasMany(Additional::class);
    }
    public function additionalDeductable()
    {
        return $this->hasMany(AdditionalDeductable::class);
    }
    public function dsa()
    {
        return $this->hasMany(Dsa::class);
    }
    public function dailyRate()
    {
        return $this->hasMany(DailyRate::class);
    }

    public function addPcrate()
    {
        return $this->hasMany(AddPcrate::class);
    }

    public function employeeDeductable()
    {
        return $this->hasMany(EmployeeDeductable::class);
    }
    public function payroll()
    {
        return $this->hasMany(Payroll::class);
    }
    public function assign()
    {
        return $this->hasOne(Assign::class);
    }
   
    public function getFullNameAttribute()
    {
        return $this->first_name . " " . $this->middle_name . " " . $this->last_name;
    }
}
