# Authentication & Notification System Analysis - ERP System

## Overview

This document provides a comprehensive analysis of the authentication and notification systems in the ERP, including current implementation, security features, and recommended improvements.

## Authentication System

### 1. Backend Authentication Architecture

#### Laravel Sanctum Implementation

The system uses **Laravel Sanctum** for API authentication with token-based security:

```php
// AuthController.php - Core authentication logic
class AuthController extends Controller {
    public function register(RegisterRequest $request) {
        $user = User::create([
            'firstName' => $data['firstName'],
            'lastName' => $data['lastName'],
            'username' => $data['username'],
            'phone' => $data['phone'],
            'role' => $data['role'],
            'email' => $data['email'],
            'password' => bcrypt($data['password']),
        ]);

        $token = $user->createToken('token')->plainTextToken;
        return response(['token' => $token, 'user' => $user]);
    }

    public function login(LoginRequest $request) {
        if (!Auth::attempt($request->validated())) {
            return response(['message' => 'Incorrect credentials'], 422);
        }

        $user = Auth::user();
        $token = $user->createToken('token')->plainTextToken;
        return response(['token' => $token, 'user' => $user]);
    }

    public function logout(Request $request) {
        $user = $request->user();
        $user->currentAccessToken()->delete();
        return response('', 204);
    }
}
```

#### User Model & Relationships

```php
// User.php - Enhanced user model
class User extends Authenticatable {
    use HasApiTokens, HasFactory, Notifiable;

    protected $fillable = [
        'firstName', 'lastName', 'username', 'image',
        'phone', 'role', 'email', 'password'
    ];

    // Relationship with permissions
    public function userPermission() {
        return $this->hasMany(UserPermission::class);
    }

    // Auto-create permissions on user creation
    protected static function boot() {
        parent::boot();
        static::created(function ($user) {
            if ($user->role == 'admin') {
                $user->userPermission()->create([
                    'routes' => json_encode([/* admin routes */]),
                    'roles' => $user->role,
                ]);
            }
            // Additional role-based permission creation...
        });
    }
}
```

### 2. Frontend Authentication Implementation

#### React Context-Based State Management

```javascript
// ContextProvider.jsx - Global authentication state
const StateContext = createContext({
  user: null,
  token: null,
  errors: null,
  notification: null,
  setUser: () => {},
  setToken: () => {},
  setErrors: () => {},
  setNotification: () => {},
});

export const ContextProvider = ({ children }) => {
  const [user, setUser] = useState({});
  const [token, _setToken] = useState(localStorage.getItem('ACCESS_TOKEN'));

  const setToken = (token) => {
    _setToken(token);
    if (token) {
      localStorage.setItem('ACCESS_TOKEN', token);
    } else {
      localStorage.removeItem('ACCESS_TOKEN');
    }
  };

  return (
    <StateContext.Provider
      value={{
        user,
        token,
        setUser,
        setToken,
        errors,
        setErrors,
        notification,
        setNotification,
      }}
    >
      {children}
    </StateContext.Provider>
  );
};
```

#### Axios Interceptors for Token Management

```javascript
// axiosConfig.js - Automatic token handling
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('ACCESS_TOKEN');
  config.headers.Authorization = `Bearer ${token}`;
  return config;
});

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response.status === 401) {
      localStorage.removeItem('ACCESS_TOKEN');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

### 3. Role-Based Access Control (RBAC)

#### Permission System Architecture

```php
// UserPermission.php - Route-based permissions
class UserPermission extends Model {
    protected $guarded = [];

    public function user() {
        return $this->belongsTo(User::class);
    }
}
```

#### Predefined Role Permissions

```php
// User.php - Role-based route assignments
static::created(function ($user) {
    if ($user->role == 'admin') {
        $routes = [
            '/dashboard', 'settings', 'employees', 'clients',
            'finance', 'reports', 'operations'
        ];
    } elseif ($user->role == 'storekeeper') {
        $routes = [
            '/dashboard', 'settings/category', 'store/items',
            'store/stock', 'store/requests'
        ];
    } else {
        $routes = ['/dashboard', 'reset_passwords'];
    }

    $user->userPermission()->create([
        'routes' => json_encode($routes),
        'roles' => $user->role,
    ]);
});
```

#### Frontend Route Protection

```javascript
// ProtectedRoute.jsx - Route-level access control
function ProtectedRoute({ children, roles, path }) {
  const { user } = useStateContext();
  const userRole = user.role;
  const paths = user.user_permission;

  if (userRole == null) {
    return <CircularProgress />;
  }

  if (roles.includes(userRole) && paths[0].routes.includes(path)) {
    return children;
  } else {
    return <Navigate to="/unauthorizedpage" />;
  }
}
```

### 4. Password Security & Validation

#### Backend Validation Rules

```php
// RegisterRequest.php - Strong password requirements
public function rules() {
    return [
        'firstName' => 'required|string|max:55',
        'lastName' => 'required|string|max:55',
        'email' => 'required|string|email|unique:users,email',
        'username' => ['required', 'string', Rule::unique('users')],
        'password' => [
            'required',
            'confirmed',
            Password::min(8)->letters()->symbols()
        ]
    ];
}
```

#### Current Security Issues

```php
// LoginRequest.php - WEAK VALIDATION (NEEDS IMPROVEMENT)
public function rules() {
    return [
        'username' => [], // No validation rules!
        'password' => []  // No validation rules!
    ];
}
```

## Notification System

### 1. Backend Notification Architecture

#### Laravel Notification System

```php
// NotificationController.php - Comprehensive notification management
class NotificationController extends Controller {
    // Get all notifications
    public function all() {
        $user = User::find(auth()->id());
        $notifications = $user->notifications;

        foreach ($notifications as $notification) {
            $notificationsData[] = [
                'type' => $notification->type,
                'id' => $notification->id,
                'message' => $notification->data['message'],
                'read_at' => $notification->read_at,
                'created_at' => Carbon::parse($notification->created_at)->diffForHumans()
            ];
        }
        return response($notificationsData);
    }

    // Unread notifications count
    public function countUnreadNotifications() {
        $user = User::find(auth()->id());
        return response($user->unreadNotifications->count());
    }

    // Mark as read
    public function markAsReadNotification($notification_id) {
        $user = User::find(auth()->id());
        $notification = $user->notifications->where('id', $notification_id)->first();

        if ($notification && !$notification->read_at) {
            $notification->markAsRead();
        }
        return response($notification);
    }
}
```

#### Database Schema

```sql
-- notifications table (Laravel standard)
CREATE TABLE notifications (
    id UUID PRIMARY KEY,
    type VARCHAR(255),
    notifiable_type VARCHAR(255),
    notifiable_id BIGINT,
    data TEXT,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### 2. Notification Types & Implementation

#### Contract Follow-up Notifications

```php
// ContractFollowUpNotification.php
class ContractFollowUpNotification extends Notification {
    public function __construct(
        public $contract_id, public $client_id, public $daysLeftUntilDue,
        public $start_date, public $end_date, public $contract_document,
        public $message, public $company_name
    ) {}

    public function via($notifiable) {
        return ['database']; // Only database notifications
    }

    public function toDatabase($notifiable) {
        return [
            'message' => [
                'contract_id' => $this->contract_id,
                'client_id' => $this->client_id,
                'daysLeftUntilDue' => $this->daysLeftUntilDue,
                'contract_document' => $this->contract_document,
                'message' => $this->message,
                'company_name' => $this->company_name,
                'type' => 'contract'
            ]
        ];
    }
}
```

#### Payment Follow-up Notifications

```php
// PaymentFollowupNotification.php
class PaymentFollowupNotification extends Notification {
    // Similar structure for payment reminders
    public function toDatabase($notifiable) {
        return [
            'message' => [
                'client_id' => $this->client_id,
                'amount_due' => $this->amount_due,
                'due_date' => $this->due_date,
                'collection_schedule' => $this->collection_schedule,
                'type' => 'payment'
            ]
        ];
    }
}
```

### 3. Frontend Notification System

#### Notification Components

```javascript
// Notifications.jsx - Main notification display
const Notifications = () => {
  const { data: notifications, isLoading } = allNotifications();

  return (
    <List>
      {notifications &&
        notifications.map((notification, index) => {
          if (
            notification.type ===
            'App\\Notifications\\ContractFollowUpNotification'
          ) {
            return (
              <MessageItem
                key={index}
                createdAt={notification.created_at}
                readAt={notification.read_at}
                id={notification.id}
                companyName={notification.message.client_id}
                contractSchedule={notification.message.contract_schedule}
              />
            );
          } else if (
            notification.type ===
            'App\\Notifications\\PaymentFollowupNotification'
          ) {
            return (
              <PaymentFMessageItem
                key={index}
                createdAt={notification.created_at}
                readAt={notification.read_at}
                id={notification.id}
                companyName={notification.message.client_id}
                collectionSchedule={notification.message.collection_schedule}
              />
            );
          }
        })}
    </List>
  );
};
```

## Current System Strengths

### Authentication Strengths

1. **Laravel Sanctum Integration**: Robust token-based authentication
2. **Role-Based Access Control**: Granular permission system
3. **Frontend State Management**: Centralized authentication state
4. **Automatic Token Handling**: Axios interceptors for seamless API calls
5. **Route Protection**: Component-level access control

### Notification Strengths

1. **Laravel Notification System**: Built on Laravel's robust notification framework
2. **Database Storage**: Persistent notification storage
3. **Read/Unread Tracking**: Complete notification state management
4. **Multiple Notification Types**: Contract and payment notifications
5. **Real-time Updates**: Notification count and status updates

## Critical Security Issues & Improvements Needed

### 1. Authentication Security Issues

#### CRITICAL: Weak Login Validation

```php
// CURRENT - LoginRequest.php (INSECURE)
public function rules() {
    return [
        'username' => [], // NO VALIDATION!
        'password' => []  // NO VALIDATION!
    ];
}

// RECOMMENDED FIX
public function rules() {
    return [
        'username' => [
            'required',
            'string',
            'max:255',
            Rule::exists('users', 'username')
        ],
        'password' => [
            'required',
            'string',
            'min:8'
        ]
    ];
}
```

#### Missing Security Features

1. **No Rate Limiting**: Login attempts not throttled
2. **No Account Lockout**: No protection against brute force
3. **No Password Expiry**: Passwords never expire
4. **No Session Management**: No concurrent session control
5. **No Audit Logging**: Authentication events not logged

### 2. Notification System Issues

#### Limited Notification Channels

```php
// CURRENT - Only database notifications
public function via($notifiable) {
    return ['database']; // Missing email, SMS, push notifications
}
```

#### No Real-time Notifications

- No WebSocket or Server-Sent Events implementation
- No push notification support
- Manual refresh required for new notifications

## Comprehensive Security Improvements

### 1. Enhanced Authentication Security

#### Rate Limiting Implementation

```php
// Add to AuthController.php
use Illuminate\Support\Facades\RateLimiter;

public function login(LoginRequest $request) {
    $key = 'login.' . $request->ip();

    if (RateLimiter::tooManyAttempts($key, 5)) {
        $seconds = RateLimiter::availableIn($key);
        return response([
            'message' => "Too many login attempts. Try again in {$seconds} seconds."
        ], 429);
    }

    if (!Auth::attempt($request->validated())) {
        RateLimiter::hit($key, 300); // 5 minute lockout
        return response(['message' => 'Incorrect credentials'], 422);
    }

    RateLimiter::clear($key);
    // ... rest of login logic
}
```

#### Account Lockout System

```php
// Add to User model
class User extends Authenticatable {
    protected $fillable = [
        // ... existing fields
        'failed_login_attempts',
        'locked_until',
        'last_login_at',
        'password_changed_at'
    ];

    public function isLocked() {
        return $this->locked_until && $this->locked_until > now();
    }

    public function incrementFailedAttempts() {
        $this->increment('failed_login_attempts');

        if ($this->failed_login_attempts >= 5) {
            $this->update(['locked_until' => now()->addMinutes(30)]);
        }
    }

    public function resetFailedAttempts() {
        $this->update([
            'failed_login_attempts' => 0,
            'locked_until' => null,
            'last_login_at' => now()
        ]);
    }
}
```

#### Two-Factor Authentication (2FA)

```php
// New migration: add_2fa_to_users_table.php
Schema::table('users', function (Blueprint $table) {
    $table->string('two_factor_secret')->nullable();
    $table->text('two_factor_recovery_codes')->nullable();
    $table->timestamp('two_factor_confirmed_at')->nullable();
    $table->boolean('two_factor_enabled')->default(false);
});

// TwoFactorController.php
class TwoFactorController extends Controller {
    public function enable(Request $request) {
        $user = $request->user();
        $user->two_factor_secret = encrypt(Google2FA::generateSecretKey());
        $user->save();

        $qrCodeUrl = Google2FA::getQRCodeUrl(
            config('app.name'),
            $user->email,
            decrypt($user->two_factor_secret)
        );

        return response(['qr_code' => $qrCodeUrl]);
    }

    public function verify(Request $request) {
        $request->validate(['code' => 'required|string']);

        $user = $request->user();
        $valid = Google2FA::verifyKey(
            decrypt($user->two_factor_secret),
            $request->code
        );

        if ($valid) {
            $user->update([
                'two_factor_enabled' => true,
                'two_factor_confirmed_at' => now()
            ]);
            return response(['message' => '2FA enabled successfully']);
        }

        return response(['message' => 'Invalid code'], 422);
    }
}
```

#### Password Policy Enhancement

```php
// Enhanced RegisterRequest.php
public function rules() {
    return [
        'firstName' => 'required|string|max:55',
        'lastName' => 'required|string|max:55',
        'email' => 'required|string|email|unique:users,email',
        'username' => [
            'required',
            'string',
            'min:3',
            'max:20',
            'regex:/^[a-zA-Z0-9_]+$/',
            Rule::unique('users')
        ],
        'password' => [
            'required',
            'confirmed',
            'min:12',
            'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/',
        ],
        'phone' => 'required|string|regex:/^[0-9+\-\s()]+$/',
        'role' => 'required|string|in:admin,hr_manager,finance_manager,employee,storekeeper'
    ];
}

public function messages() {
    return [
        'password.regex' => 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.',
        'username.regex' => 'Username can only contain letters, numbers, and underscores.'
    ];
}
```

#### Session Management

```php
// SessionController.php
class SessionController extends Controller {
    public function activeSessions(Request $request) {
        $user = $request->user();
        $sessions = $user->tokens()->where('name', 'token')->get();

        return response($sessions->map(function ($token) {
            return [
                'id' => $token->id,
                'ip_address' => $token->last_used_at ? 'Unknown' : 'Current',
                'user_agent' => 'Unknown',
                'last_activity' => $token->last_used_at,
                'current' => $token->id === request()->user()->currentAccessToken()->id
            ];
        }));
    }

    public function revokeSession(Request $request, $tokenId) {
        $user = $request->user();
        $user->tokens()->where('id', $tokenId)->delete();

        return response(['message' => 'Session revoked successfully']);
    }

    public function revokeAllSessions(Request $request) {
        $user = $request->user();
        $currentToken = $user->currentAccessToken();

        $user->tokens()->where('id', '!=', $currentToken->id)->delete();

        return response(['message' => 'All other sessions revoked']);
    }
}
```

### 2. Advanced Notification System

#### Multi-Channel Notification Support

```php
// Enhanced notification channels
public function via($notifiable) {
    $channels = ['database'];

    if ($notifiable->email_notifications_enabled) {
        $channels[] = 'mail';
    }

    if ($notifiable->sms_notifications_enabled) {
        $channels[] = 'sms';
    }

    if ($notifiable->push_notifications_enabled) {
        $channels[] = 'push';
    }

    return $channels;
}

// Email notification
public function toMail($notifiable) {
    return (new MailMessage)
        ->subject('Contract Expiration Reminder')
        ->line("Contract for {$this->company_name} expires in {$this->daysLeftUntilDue} days.")
        ->action('View Contract', url("/contracts/{$this->contract_id}"))
        ->line('Please take necessary action.');
}

// SMS notification (using Twilio/Nexmo)
public function toSms($notifiable) {
    return (new SmsMessage)
        ->content("Contract for {$this->company_name} expires in {$this->daysLeftUntilDue} days. Check your dashboard for details.");
}
```

#### Real-time Notifications with WebSockets

```php
// Install Laravel WebSockets: composer require pusher/pusher-php-server

// Broadcasting notification
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class ContractFollowUpNotification extends Notification implements ShouldBroadcast {
    public function via($notifiable) {
        return ['database', 'broadcast'];
    }

    public function toBroadcast($notifiable) {
        return new BroadcastMessage([
            'type' => 'contract_expiry',
            'message' => $this->message,
            'contract_id' => $this->contract_id,
            'company_name' => $this->company_name
        ]);
    }

    public function broadcastOn() {
        return new PrivateChannel('user.' . $this->notifiable->id);
    }
}
```

#### Frontend Real-time Integration

```javascript
// Install Laravel Echo: npm install laravel-echo pusher-js

// echo.js - WebSocket configuration
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

window.Echo = new Echo({
  broadcaster: 'pusher',
  key: process.env.REACT_APP_PUSHER_APP_KEY,
  cluster: process.env.REACT_APP_PUSHER_APP_CLUSTER,
  forceTLS: true,
  auth: {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('ACCESS_TOKEN')}`,
    },
  },
});

// NotificationProvider.jsx - Real-time notification handling
export const NotificationProvider = ({ children }) => {
  const { user, setNotification } = useStateContext();
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    if (user.id) {
      window.Echo.private(`user.${user.id}`).notification((notification) => {
        setNotification(notification.message);
        setUnreadCount((prev) => prev + 1);

        // Show browser notification
        if (Notification.permission === 'granted') {
          new Notification(notification.type, {
            body: notification.message,
            icon: '/logo.png',
          });
        }
      });
    }

    return () => {
      window.Echo.leaveChannel(`user.${user.id}`);
    };
  }, [user.id]);

  return (
    <NotificationContext.Provider value={{ unreadCount, setUnreadCount }}>
      {children}
    </NotificationContext.Provider>
  );
};
```

#### Notification Preferences System

```php
// NotificationPreference model
class NotificationPreference extends Model {
    protected $fillable = [
        'user_id', 'type', 'email_enabled', 'sms_enabled',
        'push_enabled', 'in_app_enabled'
    ];

    public function user() {
        return $this->belongsTo(User::class);
    }
}

// NotificationPreferenceController.php
class NotificationPreferenceController extends Controller {
    public function index(Request $request) {
        $preferences = $request->user()->notificationPreferences;
        return response($preferences);
    }

    public function update(Request $request) {
        $request->validate([
            'preferences' => 'required|array',
            'preferences.*.type' => 'required|string',
            'preferences.*.email_enabled' => 'boolean',
            'preferences.*.sms_enabled' => 'boolean',
            'preferences.*.push_enabled' => 'boolean'
        ]);

        foreach ($request->preferences as $pref) {
            NotificationPreference::updateOrCreate(
                ['user_id' => $request->user()->id, 'type' => $pref['type']],
                $pref
            );
        }

        return response(['message' => 'Preferences updated successfully']);
    }
}
```

### 3. Audit Logging System

#### Comprehensive Activity Logging

```php
// ActivityLog model (already exists, enhance it)
class ActivityLog extends Model {
    protected $fillable = [
        'user_id', 'action', 'model_type', 'model_id',
        'changes', 'ip_address', 'user_agent', 'timestamp'
    ];

    protected $casts = [
        'changes' => 'array',
        'timestamp' => 'datetime'
    ];
}

// ActivityLogger trait
trait LogsActivity {
    protected static function bootLogsActivity() {
        static::created(function ($model) {
            static::logActivity('created', $model);
        });

        static::updated(function ($model) {
            static::logActivity('updated', $model, $model->getChanges());
        });

        static::deleted(function ($model) {
            static::logActivity('deleted', $model);
        });
    }

    protected static function logActivity($action, $model, $changes = null) {
        ActivityLog::create([
            'user_id' => auth()->id(),
            'action' => $action,
            'model_type' => get_class($model),
            'model_id' => $model->id,
            'changes' => $changes,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()
        ]);
    }
}

// Apply to User model
class User extends Authenticatable {
    use LogsActivity;
    // ... rest of the model
}
```

### 4. Frontend Security Enhancements

#### Enhanced Route Protection

```javascript
// EnhancedProtectedRoute.jsx
function EnhancedProtectedRoute({ children, requiredPermissions, path }) {
  const { user, token } = useStateContext();
  const [isAuthorized, setIsAuthorized] = useState(null);

  useEffect(() => {
    const checkPermissions = async () => {
      if (!token) {
        setIsAuthorized(false);
        return;
      }

      try {
        // Verify token is still valid
        const response = await api.get('/user');
        const userData = response.data.user;

        // Check if user has required permissions
        const userPermissions = userData.user_permission[0].routes;
        const hasPermission = requiredPermissions.every((permission) =>
          userPermissions.includes(permission)
        );

        setIsAuthorized(hasPermission);
      } catch (error) {
        setIsAuthorized(false);
        localStorage.removeItem('ACCESS_TOKEN');
      }
    };

    checkPermissions();
  }, [token, requiredPermissions]);

  if (isAuthorized === null) {
    return <CircularProgress />;
  }

  if (!isAuthorized) {
    return <Navigate to="/login" />;
  }

  return children;
}
```

#### Security Headers & CSP

```javascript
// Add to index.html
<meta http-equiv="Content-Security-Policy"
      content="default-src 'self';
               script-src 'self' 'unsafe-inline';
               style-src 'self' 'unsafe-inline';
               img-src 'self' data: https:;">

// Add security middleware in Laravel
// SecurityHeadersMiddleware.php
class SecurityHeadersMiddleware {
    public function handle($request, Closure $next) {
        $response = $next($request);

        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Strict-Transport-Security', 'max-age=********; includeSubDomains');

        return $response;
    }
}
```

## Implementation Priority

### Phase 1: Critical Security Fixes (Week 1-2)

1. ✅ Fix LoginRequest validation
2. ✅ Implement rate limiting
3. ✅ Add account lockout mechanism
4. ✅ Enhance password policies
5. ✅ Add audit logging

### Phase 2: Enhanced Authentication (Week 3-4)

1. ✅ Implement 2FA
2. ✅ Add session management
3. ✅ Enhance route protection
4. ✅ Add security headers

### Phase 3: Advanced Notifications (Week 5-6)

1. ✅ Multi-channel notifications
2. ✅ Real-time WebSocket integration
3. ✅ Notification preferences
4. ✅ Push notifications

### Phase 4: Monitoring & Analytics (Week 7-8)

1. ✅ Security dashboard
2. ✅ Failed login monitoring
3. ✅ User activity analytics
4. ✅ Notification analytics

This comprehensive enhancement plan will transform the current basic authentication and notification system into a robust, secure, and feature-rich solution suitable for enterprise-level applications.

## User Permission System Analysis

### 1. Current Permission Architecture

#### Database Schema

```sql
-- user_permissions table
CREATE TABLE user_permissions (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    routes JSON,           -- Array of allowed routes
    roles VARCHAR(255),    -- User role (redundant with users.role)
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### Permission Model Structure

```php
// UserPermission.php - Simple permission model
class UserPermission extends Model {
    use HasFactory;
    protected $guarded = [];

    public function user() {
        return $this->belongsTo(User::class);
    }
}
```

### 2. Role-Based Permission Assignment

#### Automatic Permission Creation

The system automatically creates permissions when a user is created:

```php
// User.php - Boot method with hardcoded permissions
protected static function boot() {
    parent::boot();
    static::created(function ($user) {
        if ($user->role == 'admin') {
            $user->userPermission()->create([
                'routes' => json_encode([
                    '/dashboard', 'reset_passwords', 'settings',
                    'settings/users/:id', 'reports', 'employees',
                    'clients', 'finance', 'operations'
                    // ... 50+ hardcoded routes
                ]),
                'roles' => $user->role,
            ]);
        } else if ($user->role == 'storekeeper') {
            $user->userPermission()->create([
                'routes' => json_encode([
                    '/dashboard', '/user/change-password',
                    'settings/category', 'store/items',
                    'store/stock', 'store/requests'
                ]),
                'roles' => $user->role,
            ]);
        } else {
            $user->userPermission()->create([
                'routes' => json_encode(['/dashboard', 'reset_passwords']),
                'roles' => $user->role,
            ]);
        }
    });
}
```

#### Current Role Definitions

1. **Admin**: Full system access (50+ routes)
2. **Storekeeper**: Limited to inventory management
3. **Default**: Basic dashboard and password reset only

### 3. Frontend Permission Enforcement

#### Route Protection Component

```javascript
// ProtectedRoute.jsx - Basic route protection
function ProtectedRoute({ childern, roles, path }) {
  const { user } = useStateContext();
  const userRole = user.role;
  const paths = user.user_permission;

  if (userRole == null) {
    return (
      <Container sx={{ display: 'flex', justifyContent: 'center' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (roles.includes(userRole) && paths[0].routes.includes(path)) {
    return childern;
  } else {
    return <Navigate to="/unauthorizedpage" />;
  }
}
```

#### Unauthorized Access Handling

```javascript
// UnauthorizedPage.jsx - 403 error page
function UnauthorizedPage() {
  return (
    <Container maxWidth="sm">
      <Typography variant="h2" align="center" gutterBottom>
        403 Forbidden Resource Access
      </Typography>
      <Typography variant="body1" align="center">
        You do not have permission to access this page. Please contact the
        administrator.
      </Typography>
    </Container>
  );
}
```

### 4. Backend Permission Enforcement

#### API Route Protection

```php
// api.php - Sanctum middleware protection
Route::prefix('v1')->group(function () {
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('user', [AuthUser::class, 'user']);
        Route::apiResource('users-permission', UserPermissionController::class);
        Route::apiResource('/users', UserController::class);
        // ... all protected routes
    });

    Route::middleware('guest')->group(function () {
        Route::post('login', [AuthController::class, 'login']);
        Route::post('register', [AuthController::class, 'register']);
    });
});
```

## Current Permission System Issues

### 1. Critical Design Flaws

#### Hardcoded Route Permissions

```php
// PROBLEM: Routes hardcoded in User model boot method
'routes' => json_encode([
    '/dashboard', 'reset_passwords', 'settings',
    'settings/users/:id', 'dashboard', '/user/change-password',
    // 50+ hardcoded routes with inconsistent formatting
])
```

**Issues:**

- Routes are hardcoded and difficult to maintain
- Inconsistent route formatting (`/dashboard` vs `dashboard`)
- No way to modify permissions without code changes
- Duplicate routes in the array
- No granular permission control

#### Oversimplified Role System

```php
// PROBLEM: Only 3 basic roles
if ($user->role == 'admin') {
    // Full access
} else if ($user->role == 'storekeeper') {
    // Limited access
} else {
    // Minimal access
}
```

**Issues:**

- Only 3 predefined roles
- No role hierarchy or inheritance
- No custom role creation
- No permission granularity within roles

### 2. Security Vulnerabilities

#### Frontend-Only Permission Checks

```javascript
// PROBLEM: Permission checking only on frontend
if (roles.includes(userRole) && paths[0].routes.includes(path)) {
  return childern; // User can bypass this with dev tools
}
```

**Issues:**

- No backend permission validation
- API endpoints not protected by permissions
- Users can bypass frontend restrictions
- No audit trail for permission violations

#### Inconsistent Permission Storage

```php
// PROBLEM: Role stored in both users and user_permissions tables
users.role = 'admin'
user_permissions.roles = 'admin' // Redundant and can become inconsistent
```

## Advanced Permission System Implementation

### 1. Comprehensive RBAC System

#### Enhanced Database Schema

```sql
-- roles table
CREATE TABLE roles (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) UNIQUE,
    display_name VARCHAR(255),
    description TEXT,
    is_system_role BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- permissions table
CREATE TABLE permissions (
    id BIGINT PRIMARY KEY,
    name VARCHAR(255) UNIQUE,
    display_name VARCHAR(255),
    description TEXT,
    module VARCHAR(255),
    action VARCHAR(255),
    resource VARCHAR(255),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- role_permissions pivot table
CREATE TABLE role_permissions (
    id BIGINT PRIMARY KEY,
    role_id BIGINT,
    permission_id BIGINT,
    created_at TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    UNIQUE(role_id, permission_id)
);

-- user_roles table (support multiple roles per user)
CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    role_id BIGINT,
    assigned_by BIGINT,
    assigned_at TIMESTAMP,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id),
    UNIQUE(user_id, role_id)
);

-- user_permissions table (direct user permissions)
CREATE TABLE user_permissions (
    id BIGINT PRIMARY KEY,
    user_id BIGINT,
    permission_id BIGINT,
    granted_by BIGINT,
    granted_at TIMESTAMP,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id),
    UNIQUE(user_id, permission_id)
);
```

#### Enhanced Models

```php
// Role.php
class Role extends Model {
    protected $fillable = [
        'name', 'display_name', 'description', 'is_system_role'
    ];

    public function permissions() {
        return $this->belongsToMany(Permission::class, 'role_permissions');
    }

    public function users() {
        return $this->belongsToMany(User::class, 'user_roles');
    }

    public function hasPermission($permission) {
        return $this->permissions()->where('name', $permission)->exists();
    }
}

// Permission.php
class Permission extends Model {
    protected $fillable = [
        'name', 'display_name', 'description', 'module', 'action', 'resource'
    ];

    public function roles() {
        return $this->belongsToMany(Role::class, 'role_permissions');
    }

    public function users() {
        return $this->belongsToMany(User::class, 'user_permissions');
    }
}

// Enhanced User.php
class User extends Authenticatable {
    public function roles() {
        return $this->belongsToMany(Role::class, 'user_roles')
                    ->withPivot(['assigned_by', 'assigned_at', 'expires_at'])
                    ->withTimestamps();
    }

    public function permissions() {
        return $this->belongsToMany(Permission::class, 'user_permissions')
                    ->withPivot(['granted_by', 'granted_at', 'expires_at'])
                    ->withTimestamps();
    }

    public function hasRole($role) {
        return $this->roles()->where('name', $role)->exists();
    }

    public function hasPermission($permission) {
        // Check direct permissions
        if ($this->permissions()->where('name', $permission)->exists()) {
            return true;
        }

        // Check role-based permissions
        return $this->roles()->whereHas('permissions', function ($query) use ($permission) {
            $query->where('name', $permission);
        })->exists();
    }

    public function getAllPermissions() {
        $directPermissions = $this->permissions()->get();
        $rolePermissions = Permission::whereHas('roles', function ($query) {
            $query->whereIn('id', $this->roles()->pluck('id'));
        })->get();

        return $directPermissions->merge($rolePermissions)->unique('id');
    }
}
```

### 2. Permission Middleware System

#### Backend Permission Middleware

```php
// PermissionMiddleware.php
class PermissionMiddleware {
    public function handle($request, Closure $next, $permission) {
        if (!auth()->check()) {
            return response(['message' => 'Unauthenticated'], 401);
        }

        $user = auth()->user();

        if (!$user->hasPermission($permission)) {
            ActivityLog::create([
                'user_id' => $user->id,
                'action' => 'permission_denied',
                'model_type' => 'Permission',
                'model_id' => null,
                'changes' => ['permission' => $permission, 'route' => $request->path()],
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'timestamp' => now()
            ]);

            return response(['message' => 'Insufficient permissions'], 403);
        }

        return $next($request);
    }
}

// Register middleware in Kernel.php
protected $routeMiddleware = [
    // ... existing middleware
    'permission' => \App\Http\Middleware\PermissionMiddleware::class,
];
```

#### Enhanced API Route Protection

```php
// api.php - Granular permission-based protection
Route::prefix('v1')->group(function () {
    Route::middleware('auth:sanctum')->group(function () {

        // User management routes
        Route::middleware('permission:users.view')->group(function () {
            Route::get('/users', [UserController::class, 'index']);
            Route::get('/users/{user}', [UserController::class, 'show']);
        });

        Route::middleware('permission:users.create')->group(function () {
            Route::post('/users', [UserController::class, 'store']);
        });

        Route::middleware('permission:users.edit')->group(function () {
            Route::put('/users/{user}', [UserController::class, 'update']);
        });

        Route::middleware('permission:users.delete')->group(function () {
            Route::delete('/users/{user}', [UserController::class, 'destroy']);
        });

        // HR module routes
        Route::middleware('permission:hr.employees.view')->group(function () {
            Route::get('/emp_basics', [EmpBasicController::class, 'index']);
        });

        Route::middleware('permission:hr.employees.create')->group(function () {
            Route::post('/emp_basics', [EmpBasicController::class, 'store']);
        });

        // Finance module routes
        Route::middleware('permission:finance.payroll.view')->group(function () {
            Route::get('/payroll', [PayrollController::class, 'index']);
        });

        Route::middleware('permission:finance.payroll.process')->group(function () {
            Route::post('/payroll', [PayrollController::class, 'store']);
        });
    });
});
```

### 3. Permission Seeder System

#### Comprehensive Permission Seeder

```php
// PermissionSeeder.php
class PermissionSeeder extends Seeder {
    public function run() {
        $permissions = [
            // User Management
            ['name' => 'users.view', 'display_name' => 'View Users', 'module' => 'users', 'action' => 'view', 'resource' => 'user'],
            ['name' => 'users.create', 'display_name' => 'Create Users', 'module' => 'users', 'action' => 'create', 'resource' => 'user'],
            ['name' => 'users.edit', 'display_name' => 'Edit Users', 'module' => 'users', 'action' => 'edit', 'resource' => 'user'],
            ['name' => 'users.delete', 'display_name' => 'Delete Users', 'module' => 'users', 'action' => 'delete', 'resource' => 'user'],

            // HR Module
            ['name' => 'hr.employees.view', 'display_name' => 'View Employees', 'module' => 'hr', 'action' => 'view', 'resource' => 'employee'],
            ['name' => 'hr.employees.create', 'display_name' => 'Create Employees', 'module' => 'hr', 'action' => 'create', 'resource' => 'employee'],
            ['name' => 'hr.employees.edit', 'display_name' => 'Edit Employees', 'module' => 'hr', 'action' => 'edit', 'resource' => 'employee'],
            ['name' => 'hr.employees.delete', 'display_name' => 'Delete Employees', 'module' => 'hr', 'action' => 'delete', 'resource' => 'employee'],
            ['name' => 'hr.leave.view', 'display_name' => 'View Leave Records', 'module' => 'hr', 'action' => 'view', 'resource' => 'leave'],
            ['name' => 'hr.leave.approve', 'display_name' => 'Approve Leave', 'module' => 'hr', 'action' => 'approve', 'resource' => 'leave'],

            // Finance Module
            ['name' => 'finance.payroll.view', 'display_name' => 'View Payroll', 'module' => 'finance', 'action' => 'view', 'resource' => 'payroll'],
            ['name' => 'finance.payroll.process', 'display_name' => 'Process Payroll', 'module' => 'finance', 'action' => 'process', 'resource' => 'payroll'],
            ['name' => 'finance.reports.view', 'display_name' => 'View Financial Reports', 'module' => 'finance', 'action' => 'view', 'resource' => 'reports'],

            // Client Module
            ['name' => 'clients.view', 'display_name' => 'View Clients', 'module' => 'clients', 'action' => 'view', 'resource' => 'client'],
            ['name' => 'clients.create', 'display_name' => 'Create Clients', 'module' => 'clients', 'action' => 'create', 'resource' => 'client'],
            ['name' => 'clients.edit', 'display_name' => 'Edit Clients', 'module' => 'clients', 'action' => 'edit', 'resource' => 'client'],

            // Settings
            ['name' => 'settings.view', 'display_name' => 'View Settings', 'module' => 'settings', 'action' => 'view', 'resource' => 'settings'],
            ['name' => 'settings.edit', 'display_name' => 'Edit Settings', 'module' => 'settings', 'action' => 'edit', 'resource' => 'settings'],

            // Reports
            ['name' => 'reports.view', 'display_name' => 'View Reports', 'module' => 'reports', 'action' => 'view', 'resource' => 'reports'],
            ['name' => 'reports.export', 'display_name' => 'Export Reports', 'module' => 'reports', 'action' => 'export', 'resource' => 'reports'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission['name']], $permission);
        }
    }
}

// RoleSeeder.php
class RoleSeeder extends Seeder {
    public function run() {
        $roles = [
            [
                'name' => 'super_admin',
                'display_name' => 'Super Administrator',
                'description' => 'Full system access',
                'is_system_role' => true
            ],
            [
                'name' => 'hr_manager',
                'display_name' => 'HR Manager',
                'description' => 'Human Resources management',
                'is_system_role' => false
            ],
            [
                'name' => 'finance_manager',
                'display_name' => 'Finance Manager',
                'description' => 'Financial operations management',
                'is_system_role' => false
            ],
            [
                'name' => 'employee',
                'display_name' => 'Employee',
                'description' => 'Basic employee access',
                'is_system_role' => false
            ]
        ];

        foreach ($roles as $roleData) {
            $role = Role::firstOrCreate(['name' => $roleData['name']], $roleData);

            // Assign permissions based on role
            if ($roleData['name'] === 'super_admin') {
                $role->permissions()->sync(Permission::all());
            } elseif ($roleData['name'] === 'hr_manager') {
                $permissions = Permission::where('module', 'hr')
                    ->orWhere('module', 'users')
                    ->orWhere('name', 'reports.view')
                    ->get();
                $role->permissions()->sync($permissions);
            } elseif ($roleData['name'] === 'finance_manager') {
                $permissions = Permission::where('module', 'finance')
                    ->orWhere('module', 'clients')
                    ->orWhere('name', 'reports.view')
                    ->get();
                $role->permissions()->sync($permissions);
            }
        }
    }
}
```

### 4. Enhanced Frontend Permission System

#### Permission Context Provider

```javascript
// PermissionProvider.jsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useStateContext } from './ContextProvider';
import api from '../api/config/axiosConfig';

const PermissionContext = createContext();

export const usePermissions = () => {
  const context = useContext(PermissionContext);
  if (!context) {
    throw new Error('usePermissions must be used within PermissionProvider');
  }
  return context;
};

export const PermissionProvider = ({ children }) => {
  const { user, token } = useStateContext();
  const [permissions, setPermissions] = useState([]);
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (token && user.id) {
      fetchUserPermissions();
    }
  }, [token, user.id]);

  const fetchUserPermissions = async () => {
    try {
      const response = await api.get('/user/permissions');
      setPermissions(response.data.permissions);
      setRoles(response.data.roles);
    } catch (error) {
      console.error('Failed to fetch permissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const hasPermission = (permission) => {
    return permissions.some((p) => p.name === permission);
  };

  const hasRole = (role) => {
    return roles.some((r) => r.name === role);
  };

  const hasAnyPermission = (permissionList) => {
    return permissionList.some((permission) => hasPermission(permission));
  };

  const hasAllPermissions = (permissionList) => {
    return permissionList.every((permission) => hasPermission(permission));
  };

  return (
    <PermissionContext.Provider
      value={{
        permissions,
        roles,
        loading,
        hasPermission,
        hasRole,
        hasAnyPermission,
        hasAllPermissions,
        refreshPermissions: fetchUserPermissions,
      }}
    >
      {children}
    </PermissionContext.Provider>
  );
};
```

#### Enhanced Protected Route Component

```javascript
// EnhancedProtectedRoute.jsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { CircularProgress, Container } from '@mui/material';
import { usePermissions } from '../context/PermissionProvider';

function EnhancedProtectedRoute({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  requireAll = false,
}) {
  const {
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    loading,
  } = usePermissions();

  if (loading) {
    return (
      <Container sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Container>
    );
  }

  // Check role-based access
  if (requiredRoles.length > 0) {
    const hasRequiredRole = requiredRoles.some((role) => hasRole(role));
    if (!hasRequiredRole) {
      return <Navigate to="/unauthorized" replace />;
    }
  }

  // Check permission-based access
  if (requiredPermissions.length > 0) {
    const hasAccess = requireAll
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions);

    if (!hasAccess) {
      return <Navigate to="/unauthorized" replace />;
    }
  }

  return children;
}

export default EnhancedProtectedRoute;
```

#### Permission-Based UI Components

```javascript
// PermissionGate.jsx - Conditional rendering based on permissions
import React from 'react';
import { usePermissions } from '../context/PermissionProvider';

function PermissionGate({
  children,
  permissions = [],
  roles = [],
  requireAll = false,
  fallback = null,
}) {
  const { hasPermission, hasRole, hasAnyPermission, hasAllPermissions } =
    usePermissions();

  // Check roles
  if (roles.length > 0) {
    const hasRequiredRole = roles.some((role) => hasRole(role));
    if (!hasRequiredRole) {
      return fallback;
    }
  }

  // Check permissions
  if (permissions.length > 0) {
    const hasAccess = requireAll
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions);

    if (!hasAccess) {
      return fallback;
    }
  }

  return children;
}

// Usage examples:
// <PermissionGate permissions={['users.create']}>
//     <Button>Create User</Button>
// </PermissionGate>

// <PermissionGate roles={['admin', 'hr_manager']}>
//     <AdminPanel />
// </PermissionGate>

export default PermissionGate;
```

### 5. Permission Management Interface

#### Role Management Controller

```php
// RoleController.php
class RoleController extends Controller {
    public function index() {
        return response(Role::with('permissions')->get());
    }

    public function store(Request $request) {
        $request->validate([
            'name' => 'required|unique:roles',
            'display_name' => 'required',
            'description' => 'nullable',
            'permissions' => 'array'
        ]);

        $role = Role::create($request->only(['name', 'display_name', 'description']));

        if ($request->has('permissions')) {
            $role->permissions()->sync($request->permissions);
        }

        return response($role->load('permissions'), 201);
    }

    public function update(Request $request, Role $role) {
        if ($role->is_system_role) {
            return response(['message' => 'Cannot modify system roles'], 403);
        }

        $request->validate([
            'display_name' => 'required',
            'description' => 'nullable',
            'permissions' => 'array'
        ]);

        $role->update($request->only(['display_name', 'description']));

        if ($request->has('permissions')) {
            $role->permissions()->sync($request->permissions);
        }

        return response($role->load('permissions'));
    }

    public function destroy(Role $role) {
        if ($role->is_system_role) {
            return response(['message' => 'Cannot delete system roles'], 403);
        }

        if ($role->users()->count() > 0) {
            return response(['message' => 'Cannot delete role with assigned users'], 422);
        }

        $role->delete();
        return response('', 204);
    }
}
```

#### User Permission Management Controller

```php
// UserPermissionController.php
class UserPermissionController extends Controller {
    public function getUserPermissions(User $user) {
        return response([
            'user' => $user,
            'roles' => $user->roles()->with('permissions')->get(),
            'direct_permissions' => $user->permissions()->get(),
            'all_permissions' => $user->getAllPermissions()
        ]);
    }

    public function assignRole(Request $request, User $user) {
        $request->validate([
            'role_id' => 'required|exists:roles,id',
            'expires_at' => 'nullable|date|after:now'
        ]);

        $user->roles()->attach($request->role_id, [
            'assigned_by' => auth()->id(),
            'assigned_at' => now(),
            'expires_at' => $request->expires_at
        ]);

        return response(['message' => 'Role assigned successfully']);
    }

    public function revokeRole(Request $request, User $user) {
        $request->validate(['role_id' => 'required|exists:roles,id']);

        $user->roles()->detach($request->role_id);

        return response(['message' => 'Role revoked successfully']);
    }

    public function grantPermission(Request $request, User $user) {
        $request->validate([
            'permission_id' => 'required|exists:permissions,id',
            'expires_at' => 'nullable|date|after:now'
        ]);

        $user->permissions()->attach($request->permission_id, [
            'granted_by' => auth()->id(),
            'granted_at' => now(),
            'expires_at' => $request->expires_at
        ]);

        return response(['message' => 'Permission granted successfully']);
    }

    public function revokePermission(Request $request, User $user) {
        $request->validate(['permission_id' => 'required|exists:permissions,id']);

        $user->permissions()->detach($request->permission_id);

        return response(['message' => 'Permission revoked successfully']);
    }
}
```

#### Frontend Permission Management Components

```javascript
// RoleManagement.jsx
import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
} from '@mui/material';
import { Edit, Delete, Add } from '@mui/icons-material';
import api from '../api/config/axiosConfig';

const RoleManagement = () => {
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [open, setOpen] = useState(false);
  const [editingRole, setEditingRole] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    display_name: '',
    description: '',
    permissions: [],
  });

  useEffect(() => {
    fetchRoles();
    fetchPermissions();
  }, []);

  const fetchRoles = async () => {
    try {
      const response = await api.get('/roles');
      setRoles(response.data);
    } catch (error) {
      console.error('Failed to fetch roles:', error);
    }
  };

  const fetchPermissions = async () => {
    try {
      const response = await api.get('/permissions');
      setPermissions(response.data);
    } catch (error) {
      console.error('Failed to fetch permissions:', error);
    }
  };

  const handleSubmit = async () => {
    try {
      if (editingRole) {
        await api.put(`/roles/${editingRole.id}`, formData);
      } else {
        await api.post('/roles', formData);
      }
      fetchRoles();
      handleClose();
    } catch (error) {
      console.error('Failed to save role:', error);
    }
  };

  const handleDelete = async (roleId) => {
    if (window.confirm('Are you sure you want to delete this role?')) {
      try {
        await api.delete(`/roles/${roleId}`);
        fetchRoles();
      } catch (error) {
        console.error('Failed to delete role:', error);
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
    setEditingRole(null);
    setFormData({
      name: '',
      display_name: '',
      description: '',
      permissions: [],
    });
  };

  const handlePermissionChange = (permissionId) => {
    setFormData((prev) => ({
      ...prev,
      permissions: prev.permissions.includes(permissionId)
        ? prev.permissions.filter((id) => id !== permissionId)
        : [...prev.permissions, permissionId],
    }));
  };

  return (
    <Box>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      >
        <Typography variant="h4">Role Management</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setOpen(true)}
        >
          Create Role
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Display Name</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Permissions</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {roles.map((role) => (
              <TableRow key={role.id}>
                <TableCell>{role.name}</TableCell>
                <TableCell>{role.display_name}</TableCell>
                <TableCell>{role.description}</TableCell>
                <TableCell>
                  <Box display="flex" flexWrap="wrap" gap={0.5}>
                    {role.permissions?.slice(0, 3).map((permission) => (
                      <Chip
                        key={permission.id}
                        label={permission.display_name}
                        size="small"
                      />
                    ))}
                    {role.permissions?.length > 3 && (
                      <Chip
                        label={`+${role.permissions.length - 3} more`}
                        size="small"
                        variant="outlined"
                      />
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <IconButton
                    onClick={() => {
                      setEditingRole(role);
                      setFormData({
                        name: role.name,
                        display_name: role.display_name,
                        description: role.description,
                        permissions: role.permissions?.map((p) => p.id) || [],
                      });
                      setOpen(true);
                    }}
                    disabled={role.is_system_role}
                  >
                    <Edit />
                  </IconButton>
                  <IconButton
                    onClick={() => handleDelete(role.id)}
                    disabled={role.is_system_role}
                  >
                    <Delete />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>{editingRole ? 'Edit Role' : 'Create Role'}</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Name"
            value={formData.name}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, name: e.target.value }))
            }
            margin="normal"
            disabled={!!editingRole}
          />
          <TextField
            fullWidth
            label="Display Name"
            value={formData.display_name}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, display_name: e.target.value }))
            }
            margin="normal"
          />
          <TextField
            fullWidth
            label="Description"
            value={formData.description}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, description: e.target.value }))
            }
            margin="normal"
            multiline
            rows={3}
          />

          <Typography variant="h6" mt={2} mb={1}>
            Permissions
          </Typography>
          <FormGroup>
            {permissions.map((permission) => (
              <FormControlLabel
                key={permission.id}
                control={
                  <Checkbox
                    checked={formData.permissions.includes(permission.id)}
                    onChange={() => handlePermissionChange(permission.id)}
                  />
                }
                label={`${permission.display_name} (${permission.name})`}
              />
            ))}
          </FormGroup>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingRole ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RoleManagement;
```

## Permission System Implementation Roadmap

### Phase 1: Database Migration (Week 1)

1. ✅ Create new permission system tables
2. ✅ Migrate existing user permissions to new structure
3. ✅ Create seeders for roles and permissions
4. ✅ Test data migration integrity

### Phase 2: Backend Implementation (Week 2-3)

1. ✅ Implement new models (Role, Permission)
2. ✅ Create permission middleware
3. ✅ Update API routes with permission protection
4. ✅ Implement role and permission management controllers
5. ✅ Add comprehensive validation and error handling

### Phase 3: Frontend Implementation (Week 4-5)

1. ✅ Create PermissionProvider context
2. ✅ Implement enhanced ProtectedRoute component
3. ✅ Create PermissionGate for conditional rendering
4. ✅ Build role and permission management interfaces
5. ✅ Update existing components to use new permission system

### Phase 4: Testing & Deployment (Week 6)

1. ✅ Comprehensive testing of permission system
2. ✅ User acceptance testing
3. ✅ Performance optimization
4. ✅ Documentation and training
5. ✅ Production deployment

## Benefits of Enhanced Permission System

### 1. Security Improvements

- **Granular Control**: Fine-grained permissions for specific actions
- **Backend Validation**: API-level permission enforcement
- **Audit Trail**: Complete logging of permission violations
- **Role Hierarchy**: Structured permission inheritance

### 2. Maintainability

- **Database-Driven**: No hardcoded permissions in code
- **Dynamic Management**: Runtime permission modifications
- **Consistent Structure**: Standardized permission naming
- **Easy Extension**: Simple addition of new permissions

### 3. User Experience

- **Flexible Roles**: Custom role creation and management
- **Conditional UI**: Hide/show features based on permissions
- **Clear Feedback**: Proper error messages for unauthorized access
- **Self-Service**: Users can see their own permissions

### 4. Administrative Control

- **Role Management**: Easy role creation and modification
- **User Assignment**: Simple user-role assignments
- **Permission Tracking**: Who granted what permissions when
- **Temporary Access**: Time-limited permissions and roles

This enhanced permission system transforms the current basic role-based access into a comprehensive, secure, and maintainable authorization framework suitable for enterprise applications.
