<?php

namespace App\Http\Controllers\Store;

use App\Http\Controllers\Controller;
use App\Models\QualityInspection;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class QualityInspectionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return QualityInspection::with('coffeeBatch')->latest()->paginate();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'coffee_batch_id' => 'required|exists:coffee_batches,id',
            'moisture_content' => 'nullable|numeric',
            'defect_count' => 'nullable|integer',
            'cup_score' => 'nullable|numeric',
            'certification' => 'nullable|string|max:255',
            'pass_fail_status' => ['required', Rule::in(['pass', 'fail'])],
        ]);

        return QualityInspection::create($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\QualityInspection  $qualityInspection
     * @return \Illuminate\Http\Response
     */
    public function show(QualityInspection $qualityInspection)
    {
        return $qualityInspection->load('coffeeBatch');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\QualityInspection  $qualityInspection
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, QualityInspection $qualityInspection)
    {
        $data = $request->validate([
            'coffee_batch_id' => 'sometimes|required|exists:coffee_batches,id',
            'moisture_content' => 'nullable|numeric',
            'defect_count' => 'nullable|integer',
            'cup_score' => 'nullable|numeric',
            'certification' => 'nullable|string|max:255',
            'pass_fail_status' => ['sometimes', 'required', Rule::in(['pass', 'fail'])],
        ]);

        $qualityInspection->update($data);

        return $qualityInspection;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\QualityInspection  $qualityInspection
     * @return \Illuminate\Http\Response
     */
    public function destroy(QualityInspection $qualityInspection)
    {
        $qualityInspection->delete();

        return response()->noContent();
    }
}
