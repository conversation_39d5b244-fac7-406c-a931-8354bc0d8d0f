<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class TerminationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            'id' => $this->id,
            'emp_basic_id'=>$this->emp_basic_id,
            'client_id'=>$this->client_id,
            'reason'=>$this->reason,
            'termination_date'=>$this->termination_date,
            'ref_no'=>$this->ref_no,
        ];
    }
}
