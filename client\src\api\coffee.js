import api from './config/axiosConfig';

// Coffee Batches
export const getCoffeeBatches = () => api.get('/store/coffee-batches');
export const getCoffeeBatch = (id) => api.get(`/store/coffee-batches/${id}`);
export const createCoffeeBatch = (data) => api.post('/store/coffee-batches', data);
export const updateCoffeeBatch = (id, data) => api.put(`/store/coffee-batches/${id}`, data);
export const deleteCoffeeBatch = (id) => api.delete(`/store/coffee-batches/${id}`);

// Farmers
export const getFarmers = () => api.get('/store/farmers');
export const getFarmer = (id) => api.get(`/store/farmers/${id}`);
export const createFarmer = (data) => api.post('/store/farmers', data);
export const updateFarmer = (id, data) => api.put(`/store/farmers/${id}`, data);
export const deleteFarmer = (id) => api.delete(`/store/farmers/${id}`);

// Purchases From Farmers
export const getPurchases = () => api.get('/store/purchases-from-farmers');
export const getPurchase = (id) => api.get(`/store/purchases-from-farmers/${id}`);
export const createPurchase = (data) => api.post('/store/purchases-from-farmers', data);
export const updatePurchase = (id, data) => api.put(`/store/purchases-from-farmers/${id}`, data);
export const deletePurchase = (id) => api.delete(`/store/purchases-from-farmers/${id}`);

// Export Orders
export const getExportOrders = () => api.get('/store/export-orders');
export const getExportOrder = (id) => api.get(`/store/export-orders/${id}`);
export const createExportOrder = (data) => api.post('/store/export-orders', data);
export const updateExportOrder = (id, data) => api.put(`/store/export-orders/${id}`, data);
export const deleteExportOrder = (id) => api.delete(`/store/export-orders/${id}`);
export const allocateBatch = (id, data) => api.post(`/store/export-orders/${id}/allocate-batch`, data);
export const shipOrder = (id) => api.post(`/store/export-orders/${id}/ship`);
export const deliverOrder = (id) => api.post(`/store/export-orders/${id}/deliver`);

// Shipments
export const getShipments = () => api.get('/store/shipments');
export const getShipment = (id) => api.get(`/store/shipments/${id}`);
export const createShipment = (data) => api.post('/store/shipments', data);
export const updateShipment = (id, data) => api.put(`/store/shipments/${id}`, data);
export const deleteShipment = (id) => api.delete(`/store/shipments/${id}`);

// Certifications
export const getCertifications = () => api.get('/store/certifications');
export const getCertification = (id) => api.get(`/store/certifications/${id}`);
export const createCertification = (data) => api.post('/store/certifications', data);
export const updateCertification = (id, data) => api.put(`/store/certifications/${id}`, data);
export const deleteCertification = (id) => api.delete(`/store/certifications/${id}`);

// Stock Movements
export const getStockMovements = () => api.get('/store/stock-movements');
export const createStockMovement = (data) => api.post('/store/stock-movements', data);
export const deleteStockMovement = (id) => api.delete(`/store/stock-movements/${id}`);
