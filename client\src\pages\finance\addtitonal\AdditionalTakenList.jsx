import {
  Box,
  useTheme,
  Button,
  Typography,
  CircularProgress,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import React from "react";
import api from "../../../api/config/axiosConfig";
import Header from "../../../components/Header";
import { useStateContext } from "../../../context/ContextProvider";
import { tokens } from "../../../utils/theme";

function AdditionalTakenList({  emp_basic_id, selectedRow }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const { setNotification } = useStateContext();
  const queryClient = useQueryClient();
  const {
    data: additional,
    isFetched,
    isLoading,
  } = useQuery(
    ["additional", emp_basic_id],
    async () =>
      await api
        .get(`/additional/${emp_basic_id}`)
        .then(({ data }) => data.data),

    {
      enabled: !!emp_basic_id,
      staleTime: 60000,
    }
  );
  const columns = [
    {
      field: "id",
      headerName: "ID",
      flex: 0.2,
    },
    {
      field: "effective_date",
      headerName: "StartDate",
      flex: 0.5,
    },
    {
      field: "end_date",
      headerName: "End Date",
      flex: 0.5,
    },
    {
      field: "working_time",
      headerName: "no of days or hours worked",
      flex: 0.8,
    },
    {
      field: "minutes",
      headerName: "minutes",
      flex: 0.5,
    },
    {
      field: "rate_type",
      headerName: "Rate Type",
      flex: 0.5,
    },
    {
      field: "total",
      headerName: "Total",
      cellClassName: "custom-cell",
      flex: 0.5,
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 0.7,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          selectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "delete",
      headerName: "Delete",
      flex: 0.8,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deletAdditional = useMutation(
          (id) => api.delete(`additional/${id}`),
          {
            onSuccess: () => {
              setNotification("additional record deleted successfully");
              queryClient.invalidateQueries({ queryKey: "additional" });
            },
          }
        );
        const deleteAdditional = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              "Are you sure you want to delete the selected  additional record?"
            )
          ) {
            return;
          }
          deletAdditional.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={deleteAdditional}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];
  return (
    <Box>
      <Header title="List of Additional Record" heading="h5" />
      {isLoading && <CircularProgress />}
      {isFetched && (
        <DataGrid columns={columns} hideFooter autoHeight rows={additional} />
      )}
    </Box>
  );
}

export default AdditionalTakenList;
