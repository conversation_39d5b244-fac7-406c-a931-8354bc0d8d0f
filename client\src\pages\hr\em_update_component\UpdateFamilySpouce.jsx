import { <PERSON>, TextField, Grid, MenuI<PERSON>, But<PERSON> } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import { useEffect } from "react";
import { useState } from "react";
import api from "../../../api/config/axiosConfig";
import { useStateContext } from "../../../context/ContextProvider";

function UpdateFamilySpouce({ id }) {
  const family = [
    {
      lable: "spouce",
      value: "spouce",
    },
    {
      lable: "Father",
      value: "father",
    },
    {
      lable: "Mother",
      value: "mother",
    },
    {
      lable: "Brother",
      value: "brother",
    },
    {
      lable: "Child",
      value: "child",
    },
    {
      lable: "Sister",
      value: "sister",
    },
  ];
  const [isEmp, setIsEmp] = useState(false);
  const { errors, setErrors, setNotification } = useStateContext();
  const [formData, setFormData] = useState({
    emp_basic_id: "",
    name: "",
    age: "",
    relationship: "",
    phone: "",
    office_phone: "",
  });
  useEffect(() => {
    api
      .get(`emp_basics/${id}?all=true`, { params: { all: 'family' } })
      .then(({ data }) => {
        if (data.data.family_info) {
          setFormData(data.data.family_info);
          setIsEmp(true);
        }
      });
  }, [id]);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (isEmp) {
      api.put(`family_info/${(formData, id)}`, formData).then(() => {
        setNotification('family info updated successfully');
      }).catch((err) => {
        if (err.response && err.response.status === 422) {
          if (err.response.data.errors) {
            setErrors(err.response.data.errors);
          }
        }
      });
    } else {
        const data=new FormData(e.currentTarget)
        const payload={
            emp_basic_id:id,
            name: data.get('name'),
            age: data.get('age'),
            relationship: data.get('relationship'),
            phone: data.get('phone'),
            office_phone: data.get('office_phone'),
        }
        api.post(`family_info`,payload).then(()=>{
            setNotification('family info created successfully');
        }).catch((err) => {
            if (err.response && err.response.status === 422) {
              if (err.response.data.errors) {
                setErrors(err.response.data.errors);
              }
            }
          });
    }
  };
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.name == ""}
            margin="normal"
            label="Full Name"
            name="name"
            fullWidth
            variant="standard"
            type="text"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.name}
            helperText={errors.name ? errors.name[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.age == ""}
            margin="normal"
            label="Age"
            name="age"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.age}
            helperText={errors.age ? errors.age[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.phone == ""}
            margin="normal"
            label="Phone Number"
            name="phone"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.phone}
            helperText={errors.phone ? errors.phone[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.office_phone == ""}
            margin="normal"
            label="Office Number"
            name="office_phone"
            fullWidth
            variant="standard"
            type="number"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.office_phone}
            helperText={errors.office_phone ? errors.office_phone[0] : null}
          />
        </Grid>
        <Grid item xs={12} md={2} sm={4}>
          <TextField
            error={!errors.relationship == ""}
            margin="normal"
            label="relationship"
            name="relationship"
            fullWidth
            variant="standard"
            type="number"
            defaultValue={family[0][1]}
            select
            // size="small"
            InputLabelProps={{
              color: "success",
            }}
            onChange={handleChange}
            value={formData.relationship}
            helperText={errors.relationship ? errors.relationship[0] : null}
          >
            {family.map((val) => (
              <MenuItem key={val.value} value={val.value}>
                {val.lable}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
          {isEmp ? <div>update</div> : <div>create</div>}
        </Button>
      </Box>
    </Box>
  );
}

export default UpdateFamilySpouce;
