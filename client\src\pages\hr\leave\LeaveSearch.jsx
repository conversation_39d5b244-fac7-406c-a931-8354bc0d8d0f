import { Box, Grid, useTheme, Paper } from "@mui/material";
import React from "react";
import Header from "../../../components/Header";
import CeSearchComponent from "../../../components/CeSearchComponent";
import SearchBar from "../../../components/SearchBar";
import EmployeeSelecter from "../../../components/EmployeeSelecter";
import { useQuery } from "@tanstack/react-query";
import api from "../../../api/config/axiosConfig";
import { tokens } from "../../../utils/theme";
import { useState } from "react";
import {  useNavigate } from "react-router-dom";

function LeaveSearch() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const [formData, setFormData] = useState({});
  const navigate = useNavigate();
  const { data: client, isFetched } = useQuery(
    ["client"],
    async () => await api.get("client").then(({ data }) => data.data),
    {
      refetchOnWindowFocus: false,
      staleTime: 60000,
    }
  );
  const handleClietSelect = ({ value }) => {
    setFormData({
      ...formData,
      client_id: value.id,
    });
  };

  const { data: assignedEmployee } = useQuery(
    ["assignedEmployee", formData.client_id],
    async () =>
      await api.get(`assign/client/${formData.client_id}`).then(({ data }) => {
        return data.data;
      }),
    {
      enabled: !!formData.client_id,
    }
  );
  const handleEmployeeSelect = ({ value, clicked }) => {
    setFormData({
      ...formData,
      emp_basic_id: value.id,
    });
    if (clicked) {
      navigate("/employees/leaves", {
        state: {
          emp_basic_id: value.id,
          client_id: formData.client_id,
        },
      });
    }
  };
  return (
    <Box mx="40px">
      <Header
        title="Employee Leave Page"
        subtitle="select clinet and employee to give leave to employee"
      />
      <Grid container spacing={1}>
        <Grid item xs={12} sm={4}>
          <Paper
            sx={{
              padding: "1rem",
              color: colors.grey[100],
              background: colors.primary[400],
            }}
          >
            <CeSearchComponent
              ClientSearch={
                <SearchBar client={client} onClientSelect={handleClietSelect} />
              }
              EmployeeSelecter={
                <EmployeeSelecter
                  assignedEmployee={assignedEmployee}
                  isFetched={isFetched}
                  onEmployeeSelect={handleEmployeeSelect}
                />
              }
            />
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}

export default LeaveSearch;
