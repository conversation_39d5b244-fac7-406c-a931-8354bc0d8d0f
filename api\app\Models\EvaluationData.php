<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EvaluationData extends Model
{
    use HasFactory;

    protected $table = 'evaluation_data';

    protected $fillable = [
        'applicant_id',
        'committee_member_id',
        'evaluation_date',
        'work_ethic_score',
        'comments',
    ];

    public function applicant()
    {
        return $this->belongsTo(Applicant::class);
    }

    public function committeeMember()
    {
        return $this->belongsTo(CommitteeMember::class);
    }
}
