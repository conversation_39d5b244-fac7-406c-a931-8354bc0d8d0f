<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pc_rates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('emp_basic_id');
            $table->foreign('emp_basic_id')->references('id')->on('emp_basics')->onDelete('cascade');
            $table->integer('no_pc');
            $table->float('total');
            $table->string('pc_type');
            $table->float('salary');
            $table->date('from_date')->format('Y-m-d');
            $table->date('to_date')->format('Y-m-d');
            $table->tinyInteger('paid')->default(0);
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pc_rates');
    }
};
