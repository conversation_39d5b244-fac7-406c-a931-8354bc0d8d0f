import { <PERSON><PERSON>, <PERSON>, Typo<PERSON>, <PERSON><PERSON><PERSON>, Button } from '@mui/material';
import React from 'react';
import { useState, useEffect } from 'react';
import api from '../../../api/config/axiosConfig';
import { useStateContext } from '../../../context/ContextProvider';
import { useQuery } from '@tanstack/react-query';

function EmployeeAllowance({ onNext, onBack, clicked }) {
  const { errors, setErrors, empBasic, empAllowance, setEmpAllowance, user } =
    useStateContext();
  
  const [formData, setFormData] = useState({
    transport_allowance: '',
    cleaning_allowance: '',
    mobile_allowance: '',
    housing_allowance: '',
    position_allowance: '',
    non_tax_ta: '',
    non_tax_deseret: '',
    non_tax_position: '',
    non_tax_mobile: '',
    non_tax_cleaning: '',
    non_tax_other: '',
    user_id: user.id,
  });

  useEffect(() => {
    setFormData({
      ...formData,
      user_id: user.id,
    });
  }, [user]);

  if (clicked && empAllowance) {
    useQuery(
      ['allowanceInfo'],
      () => api.get(`emp_allowance/${empAllowance}`),
      {
        onSuccess: ({ data }) => {
          setFormData(data);
        },
      }
    );
  }
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const data = new FormData(e.currentTarget);
    
    const payload = {
      emp_basic_id: empBasic,
      transport_allowance: data.get('transport_allowance'),
      cleaning_allowance: data.get('cleaning_allowance'),
      mobile_allowance: data.get('mobile_allowance'),
      housing_allowance: data.get('housing_allowance'),
      position_allowance: data.get('position_allowance'),
      non_tax_ta: data.get('non_tax_ta'),
      non_tax_deseret: data.get('non_tax_deseret'),
      non_tax_position: data.get('non_tax_position'),
      non_tax_cleaning: data.get('non_tax_cleaning'),
      non_tax_mobile: data.get('non_tax_mobile'),
      non_tax_other: data.get('non_tax_other'),
      user_id: user.id,
    };

    if (clicked && empAllowance) {
      api
        .put(`emp_allowance/${empAllowance}`, payload)
        .then(() => {
          onNext();
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
        });
    } else {
      api
        .post('emp_allowance', payload)
        .then(({ data }) => {
          onNext();
          setEmpAllowance(data.id);
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
        });
    }
  };

  return (
    <Box>
      <Typography>Employee Allowance Info Form</Typography>
      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.transport_allowance == ''}
              margin="normal"
              label="Transport Allowance"
              name="transport_allowance"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.transport_allowance}
              helperText={
                errors.transport_allowance
                  ? errors.transport_allowance[0]
                  : null
              }
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.cleaning_allowance == ''}
              margin="normal"
              label="Cleaning Allowance"
              name="cleaning_allowance"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.cleaning_allowance}
              helperText={
                errors.cleaning_allowance ? errors.cleaning_allowance[0] : null
              }
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.mobile_allowance == ''}
              margin="normal"
              label="Mobile Allowance"
              name="mobile_allowance"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.mobile_allowance}
              helperText={
                errors.mobile_allowance ? errors.mobile_allowance[0] : null
              }
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.housing_allowance == ''}
              margin="normal"
              label="Housing Allowance"
              name="housing_allowance"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.housing_allowance}
              helperText={
                errors.housing_allowance ? errors.housing_allowance[0] : null
              }
            />
          </Grid>

          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.position_allowance == ''}
              margin="normal"
              label="Position Allowance"
              name="position_allowance"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.position_allowance}
              helperText={
                errors.position_allowance ? errors.position_allowance[0] : null
              }
            />
          </Grid>

          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.non_tax_ta == ''}
              margin="normal"
              label="None Taxiable Transport Allowance"
              name="non_tax_ta"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.non_tax_ta}
              helperText={errors.non_tax_ta ? errors.non_tax_ta[0] : null}
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.non_tax_deseret == ''}
              margin="normal"
              label="None Tx Deseret"
              name="non_tax_deseret"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.non_tax_deseret}
              helperText={
                errors.non_tax_deseret ? errors.non_tax_deseret[0] : null
              }
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.non_tax_position == ''}
              margin="normal"
              label="None Tax Position"
              name="non_tax_position"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.non_tax_position}
              helperText={
                errors.non_tax_position ? errors.non_tax_position[0] : null
              }
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.non_tax_mobile == ''}
              margin="normal"
              label="None Tax Mobile"
              name="non_tax_mobile"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.non_tax_mobile}
              helperText={
                errors.non_tax_mobile ? errors.non_tax_mobile[0] : null
              }
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.non_tax_cleaning == ''}
              margin="normal"
              label="None Tax Cleaning"
              name="non_tax_cleaning"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.non_tax_cleaning}
              helperText={
                errors.non_tax_cleaning ? errors.non_tax_cleaning[0] : null
              }
            />
          </Grid>
          <Grid item xs={12} md={2} sm={4}>
            <TextField
              error={!errors.non_tax_other == ''}
              margin="normal"
              label="None Tax Others"
              name="non_tax_other"
              fullWidth
              variant="standard"
              type="number"
              InputLabelProps={{
                color: 'success',
              }}
              onChange={handleChange}
              value={formData.non_tax_other}
              helperText={errors.non_tax_other ? errors.non_tax_other[0] : null}
            />
          </Grid>
        </Grid>

        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button onClick={onBack} sx={{ mt: 3, ml: 1 }} variant="contained">
            Back
          </Button>
          <Button type="submit" sx={{ mt: 3, ml: 1 }} variant="contained">
            next
          </Button>
        </Box>
      </Box>
    </Box>
  );
}

export default EmployeeAllowance;
