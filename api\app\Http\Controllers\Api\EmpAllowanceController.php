<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\EmpAllowance;
use App\Http\Requests\StoreEmpAllowanceRequest;
use App\Http\Requests\UpdateEmpAllowanceRequest;
use App\Http\Resources\EmpAllowanceResource;

class EmpAllowanceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index()
    {
        return  EmpAllowanceResource::collection(EmpAllowance::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreEmpAllowanceRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreEmpAllowanceRequest $request)
    {
        $data = $request->validated();
        $empAllowance=EmpAllowance::create([
            'emp_basic_id'=>$data['emp_basic_id'],
            'transport_allowance'=>$request->input('transport_allowance'),
            'cleaning_allowance'=>$request->input('cleaning_allowance'),
            'mobile_allowance'=>$request->input('mobile_allowance'),
            'housing_allowance'=>$request->input('housing_allowance'),
            'position_allowance'=>$request->input('position_allowance'),
            'non_tax_ta'=>$request->input('non_tax_ta'),
            'non_tax_deseret'=>$request->input('non_tax_deseret'),
            'non_tax_position'=>$request->input('non_tax_position'),
            'non_tax_mobile'=>$request->input('non_tax_mobile'),
            'non_tax_cleaning'=>$request->input('non_tax_cleaning'),
            'non_tax_other'=>$request->input('non_tax_other'),
            'user_id'=>$request->user_id,
        ]);
        return response(new EmpAllowanceResource($empAllowance));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EmpAllowance  $empAllowance
     * @return \Illuminate\Http\Response
     */
    public function show(EmpAllowance $empAllowance)
    {
        return response(new EmpAllowanceResource($empAllowance));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateEmpAllowanceRequest  $request
     * @param  \App\Models\EmpAllowance  $empAllowance
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateEmpAllowanceRequest $request, EmpAllowance $empAllowance)
    {

        $validated = $request->validated();
        $passed=$request->all();
        $data=array_merge($validated,$passed);

        $empAllowance->update($data);

        return response(new EmpAllowanceResource($empAllowance));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EmpAllowance  $empAllowance
     * @return \Illuminate\Http\Response
     */
    public function destroy(EmpAllowance $empAllowance)
    {
        $empAllowance->delete();

        return response('', 204);
    }
}
