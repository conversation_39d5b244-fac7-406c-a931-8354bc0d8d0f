<?php

namespace App\Http\Controllers\Api\finance;

use App\Http\Controllers\Api\helper\PayrollHelper;
use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\EmpBasic;
use App\Models\finance\Payroll;
use Illuminate\Http\Request;
use App\Http\Controllers\Api\AssignController;
use Illuminate\Support\Carbon;

class DraftPayrollController extends Controller
{
    public  $rateType;
    public $guardSalary;
    public $overTimeGuardSalary;
    public $leaderSalary;
    public $guardLsalary;
    public $clientId;
    public $leaderLsalary;
    public $guardTransport;
    public $totalWorkingHour;

    public $emp_client_additional;
    public $otherTransportAllowance;
    public $basicSalary;
    public $totalWorkingDuration;
    public $transportDeductionNewEmployee;
    public $transportAllowanceDeduction;
    public $currency;
    public $nonTaxTransportAllowance;
    public $nonTaxDesertAllowance;
    public $nonTaxMobileAllowance;
    public $nonTaxCleaningAllowance;
    public $nonTaxOtherAllowance;
    public $nonTaxPositionAllowance;
    public $totaltaxableDsaSalary;
    public $totalNontaxableDsaSalary;
    public $laborUnion;
    public $pcratSalarySummary;
    public $basicSalarySummary;
    public $addPcratSalarySummary;
    public $overtimeSummary;
    public $additionalHourSummary;
    public $absenceDeductionSummary;
    public $taxableOtherAllowanceSummary;
    public $transportAllowanceSummary;
    public $transportAllowanceDeductionSummary;
    public  $totalAddeductionSummary;
    public $taxableDsaSalarySummary;
    public $taxableIncome;
    public $providentEmpSummary;
    public $pensionEmpSummary;
    public $pensionCompSummary;
    public $providentCompSummary;
    public $totalOtherLoanSummary;
    public $totalDeductionSummary;
    public $additionalNontaxableSummary;
    public $nontaxTransportAllowanceSummary;
    public $nontaxDesertAllowanceSummary;
    public $nontaxMobileAllowanceSummary;
    public $nontaxCleaningAllowanceSummary;
    public $nontaxPositionAllowanceSummary;
    public $nontaxOtherAllowanceSummary;
    public $nontaxDsaAllowanceSummary;
    public $netPaySummary;
    public function __construct()
    {
        $this->rateType = '';
        $this->guardSalary = 0;
        $this->overTimeGuardSalary = 0;
        $this->leaderSalary = 0;
        $this->guardLsalary = 0;
        $this->guardLsalary = 0;
        $this->leaderLsalary = 0;
        $this->guardTransport = 0;
        $this->totalWorkingHour = 0;
        $this->otherTransportAllowance = 0;
        $this->basicSalary = 0;
        $this->totalWorkingDuration = 1;
        $this->transportDeductionNewEmployee = 0;
        $this->transportAllowanceDeduction = 0;
        $this->currency = 0;
        $this->nonTaxTransportAllowance = 0;
        $this->nonTaxDesertAllowance = 0;
        $this->nonTaxMobileAllowance = 0;
        $this->nonTaxCleaningAllowance = 0;
        $this->nonTaxPositionAllowance = 0;
        $this->nonTaxOtherAllowance = 0;
        $this->totaltaxableDsaSalary = 0;
        $this->totalNontaxableDsaSalary = 0;
        $this->laborUnion = 0;
        $this->pcratSalarySummary = 0;
        $this->basicSalarySummary = 0;
        $this->addPcratSalarySummary = 0;
        $this->overtimeSummary = 0;
        $this->additionalHourSummary = 0;
        $this->absenceDeductionSummary = 0;
        $this->taxableOtherAllowanceSummary = 0;
        $this->transportAllowanceSummary = 0;
        $this->transportAllowanceDeductionSummary = 0;
        $this->totalAddeductionSummary = 0;
        $this->taxableDsaSalarySummary = 0;
        $this->taxableIncome = 0;
        $this->providentEmpSummary = 0;
        $this->pensionEmpSummary = 0;
        $this->pensionCompSummary = 0;
        $this->providentCompSummary = 0;
        $this->totalOtherLoanSummary = 0;
        $this->totalDeductionSummary = 0;
        $this->additionalNontaxableSummary = 0;
        $this->nontaxTransportAllowanceSummary = 0;
        $this->nontaxDesertAllowanceSummary = 0;
        $this->nontaxMobileAllowanceSummary = 0;
        $this->nontaxCleaningAllowanceSummary = 0;
        $this->nontaxPositionAllowanceSummary = 0;
        $this->nontaxOtherAllowanceSummary = 0;
        $this->nontaxDsaAllowanceSummary = 0;
        $this->netPaySummary = 0;
        $this->emp_client_additional = 0;
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $clientId = request()->input('client_id');
        $assigend = new AssignController;
        $exs = $assigend->clientAssignedId($clientId);
        $emps = EmpBasic::whereIn('id', $exs)->get();
        $empPayroll = collect([]);
        $pHelper = new PayrollHelper();
        $payrollDate = $pHelper->setPayrollDate();
        $provident = $pHelper->provident();
        $pension = $pHelper->pension();
        $dateInHumans =  Carbon::parse($payrollDate['startDate']);
        $year = $dateInHumans->year;
        $month = $dateInHumans->format('F');

        foreach ($emps as $emp) {

            $actualDateLength = $pHelper->dateLength($emp->start_date);
            $dateLength = $pHelper->actualDateLength();
            $this->rateType = $emp->rate_type;
            if ($emp->start_date == $payrollDate['endDate'] || $emp->start_date < $payrollDate['startDate']) {
                $this->overTimeGuardSalary = $emp->basic_salary;
                if ($emp->position == 'Regular Guard') {
                    $this->guardSalary = $emp->basic_salary;
                    $this->overTimeGuardSalary = $emp->basic_salary;
                } else {
                    $this->leaderSalary = $emp->basic_salary;
                }
                $emp_additional = $emp->additional;
                $emp_holiyday = $emp->holiyday;
            } else {
                if ($emp->position == 'Regular Guard') {
                    $this->guardSalary = ($emp->basic_salary / $dateLength) * $actualDateLength;
                    // $this->overTimeGuardSalary = $emp->basic_salary;
                } else {
                    $this->leaderSalary = ($emp->basic_salary / $dateLength) * $actualDateLength;
                }
            }
            $client = Client::where('id', $clientId)->first();
            if ($emp->start_date == $payrollDate['startDate'] || $emp->start_date < $payrollDate['startDate']) {
                if ($emp->position == 'Regular Guard') {
                    if ($this->guardSalary == null || $this->guardSalary == 0) {
                        $this->guardSalary = $client->guard_salary;
                        $this->guardLsalary = $client->guard_lsalary;
                    }
                } else if ($emp->position == 'Shift Leader') {
                    if ($this->leaderSalary == null || $this->leaderSalary == 0) {
                        $this->leaderSalary = $client->leader_salary;
                        $this->leaderLsalary = $client->leader_lsalary;
                    }
                } else if ($emp->position == 'Relief Guard') {
                    if ($this->leaderSalary == null || $this->leaderSalary == 0) {
                        $this->leaderSalary = $client->leader_salary;
                        $this->leaderLsalary = $client->leader_lsalary;
                    }
                }
            } else {
                if ($emp->position == 'Regular Guard') {
                    if ($this->guardSalary == null || $this->guardSalary == 0) {
                        $this->guardSalary = ($client->guard_salary / $dateLength) * $actualDateLength;
                        $this->guardLsalary = ($client->guard_lsalary / $dateLength) * $actualDateLength;
                    }
                } else if ($emp->position == 'Shift Leader') {

                    if ($this->leaderSalary == null || $this->leaderSalary == 0) {
                        $this->leaderSalary = ($client->leader_salary / $dateLength) * $actualDateLength;
                        $this->leaderLsalary = ($client->leader_lsalary / $dateLength) * $actualDateLength;
                    }
                } else if ($emp->position == 'Relief Guard') {
                    if ($this->leaderSalary == null || $this->leaderSalary == 0) {
                        $this->leaderSalary = ($client->leader_salary / $dateLength) * $actualDateLength;
                        $this->leaderLsalary = ($client->leader_lsalary / $dateLength) * $actualDateLength;
                    }
                }
            }
            if ($client->guard_transport == null || $client->guard_transport == 0) {
                $this->guardTransport = $client->guard_transport;
            }
            if ($client->rate_type == null || $client->rate_type == 0) {
                $this->rateType = $client->rate_type;
                $this->totalWorkingHour = $client->total_working_hours;
            }
            if ($client->guard_salary == null) {
                $this->guardSalary = $this->leaderSalary;
            }

            //call get holiday but not relevant for now 
            $taxedAllowance = $pHelper->taxable($emp->id, $emp->start_date);
            
            $this->guardTransport = $taxedAllowance['guardTransport'];
            if ($this->rateType == 'Daily') {
                $this->dailyRate($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            }
            //could of call metaholiday but its irrelevant for now
            //hodayrate
            $overtimes = $pHelper->overTime($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $pcRate = $pHelper->pcrate($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $dsa = $pHelper->dsa($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $nontaxable = $pHelper->nonTaxable($emp->id, $emp->start_date);
            $additionalNonTaxableAllowance = $pHelper->additionalNonTaxAllowance($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $addPcrate = $pHelper->addpcrate($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $additionals = $pHelper->additional($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            //client additonal could of called here 
            $additonalDeductable = $pHelper->additionalDeductable($emp->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $absence = $pHelper->absenceList($emp->id, $payrollDate['startDate'], $payrollDate['endDate'], $client->total_working_hours, $this->guardSalary);
            $deduct = $pHelper->deduct($emp->id);
            $clientDeduct = $pHelper->clientDeduct($emp->id, $client->id);
            $medicalShare = $pHelper->medicalShare();

            $empDeductable = $pHelper->empDeductable($emp->id);
            $totalLeaveTakenduration = $pHelper->totalLeaveDuration($emp->id, $client->id, $payrollDate['startDate'], $payrollDate['endDate']);
            $totalLeaveTakenWithoutPay = $pHelper->totalLeaveTakenWithoutPayTaken($emp->id, $client->id, $payrollDate['startDate'], $payrollDate['endDate']);

            $totalLeaveTransDeduction = round(($taxedAllowance['guardTransport'] / $actualDateLength) * $totalLeaveTakenduration['daysduration'], 2);
            $this->otherTransportAllowance = $taxedAllowance['taxMobileAllowance'] + $taxedAllowance['taxCleaningAllowance'];

            if ($emp->position == 'Regular Guard') {
                if ($this->rateType == 'Month') {
                    $lWoutpayDed = round((($this->guardSalary / $actualDateLength) * $totalLeaveTakenWithoutPay), 2);
                    $leaveWithoutPayMess = $lWoutpayDed . 'Leave with out pay';
                } else {
                    $lWoutpayDed = round(($totalLeaveTakenWithoutPay * $this->guardSalary), 2);
                    $leaveWithoutPayMess = $lWoutpayDed . 'Leave with out pay';
                }
            } else {
                if ($this->rateType == 'Month') {
                    $lWoutpayDed = round((($this->leaderSalary / $actualDateLength) * $totalLeaveTakenWithoutPay), 2);
                    $leaveWithoutPayMess = $lWoutpayDed . 'Leave with out pay';
                } else {
                    $lWoutpayDed = round(($totalLeaveTakenWithoutPay * $this->leaderSalary), 2);
                    $leaveWithoutPayMess = $lWoutpayDed . 'Leave with out pay';
                }
            }
            if ($emp->position == 'Regular Guard') {
                if ($this->rateType == 'Hour' || $this->rateType == 'Daily') {
                    $this->basicSalary = ($this->guardSalary * $this->totalWorkingDuration - $lWoutpayDed) + $this->totalWorkingDuration;
                } else {
                    $this->basicSalary = ($this->guardSalary - $lWoutpayDed) + $pcRate['totalpcrateSalary'];
                }
            } else {
                if ($this->rateType == 'Hour' || $this->rateType == 'Daily') {
                    $this->basicSalary = ($this->guardSalary * $this->totalWorkingDuration - $lWoutpayDed) +  $pcRate['totalpcrateSalary'];
                } else {
                    $this->basicSalary = ($this->guardSalary - $lWoutpayDed) +  $pcRate['totalpcrateSalary'];
                }
            }

            if ($taxedAllowance['guardTransport'] == null || $taxedAllowance['guardTransport'] == 0) {
                if ($client->guard_transport != 0) {
                    $this->guardTransport = $client->guard_transport / 100 * $this->basicSalary;
                }
            }


            if ($emp->start_date != $payrollDate['startDate']) {
                $noDays = $pHelper->nodays($emp->start_date, $payrollDate['startDate']);
                $this->transportDeductionNewEmployee = $noDays * ($this->guardTransport / $actualDateLength);
            }
            $this->transportAllowanceDeduction = $this->transportDeductionNewEmployee;
            if ($emp->position == 'Regular Guard') {

                if ($this->currency == 1) {
                    $this->guardSalary = $this->guardSalary * $pHelper->exchangeRate()['exchange'];
                }
            } else {
                if ($this->currency == 1) {
                    $this->guardSalary = $this->leaderSalary * $pHelper->exchangeRate()['exchange'];
                }
                $this->guardLsalary = $this->leaderLsalary;
            }
            if ($this->currency == 1) {
                $this->nonTaxTransportAllowance = $nontaxable['nonTaxTransportAllowance'] * $pHelper->exchangeRate()['exchange'];
                $this->nonTaxDesertAllowance = $nontaxable['nonTaxDesertAllowance'] * $pHelper->exchangeRate()['exchange'];
                $this->nonTaxMobileAllowance = $nontaxable['nonTaxMobileAllowance'] * $pHelper->exchangeRate()['exchange'];
                $this->nonTaxCleaningAllowance = $nontaxable['nonTaxCleaningAllowance'] * $pHelper->exchangeRate()['exchange'];
                $this->nonTaxOtherAllowance = $nontaxable['nonTaxOtherAllowance'] * $pHelper->exchangeRate()['exchange'];
                $this->nonTaxPositionAllowance = $nontaxable['nonTaxPositionAllowance'] * $pHelper->exchangeRate()['exchange'];
                $this->guardTransport = $this->guardTransport * $pHelper->exchangeRate()['exchange'];
                $this->totaltaxableDsaSalary = $dsa['totaltaxableDsaSalary'] * $pHelper->exchangeRate()['exchange'];
                $this->totalNontaxableDsaSalary = $dsa['totalNontaxableDsaSalary'] * $pHelper->exchangeRate()['exchange'];
            } else {
                $this->nonTaxTransportAllowance = $nontaxable['nonTaxTransportAllowance'];
                $this->nonTaxDesertAllowance = $nontaxable['nonTaxDesertAllowance'];
                $this->nonTaxMobileAllowance = $nontaxable['nonTaxMobileAllowance'];
                $this->nonTaxCleaningAllowance = $nontaxable['nonTaxCleaningAllowance'];
                $this->nonTaxOtherAllowance = $nontaxable['nonTaxOtherAllowance'];
                $this->nonTaxPositionAllowance = $nontaxable['nonTaxPositionAllowance'];
                $this->totaltaxableDsaSalary = $dsa['totaltaxableDsaSalary'];
                $this->totalNontaxableDsaSalary = $dsa['totalNontaxableDsaSalary'];
            }
            if ($clientDeduct['laborUnionType']) {
                $this->laborUnion = ($clientDeduct['laborUnion'] * $this->guardSalary / 100) + $empDeductable['totalLaborLoan'];
            } else {
                $this->laborUnion = $clientDeduct['laborUnion']  + $empDeductable['totalLaborLoan'];
            }
            // if ($this->rateType == 'Hour') {
                $this->overTimeGuardSalary =   ($this->overTimeGuardSalary * $overtimes['totalOvertimeHours']/($client->total_working_hours));
            // } 
            // else {
                // if ($actualDateLength != 0) {
                //     $this->overTimeGuardSalary = ($this->guardSalary / $actualDateLength) * $dateLength;
                // } else {
                //     $this->overTimeGuardSalary = $this->guardSalary * $overtimes['totalOvertimeHours'] / $this->totalWorkingDuration;
                // }
            // }

            $taxable_transport_allowance = $this->guardTransport - $this->transportDeductionNewEmployee;
            $taxable_transport_allowance = $this->guardTransport;
            if ($emp->pension) {
                $pensionEmp = $pension['pensionEmp'] * $this->basicSalary / 100;
                $pensionComp = $pension['pensionEdomias'] * $this->basicSalary / 100;
            } else {
                $pensionEmp = 0;
                $pensionComp = 0;
            }
            if ($emp->provident) {
                $providentEmp = $provident['providentEmp'] * $this->basicSalary / 100;
                $providentComp = $provident['providentEdomias'] * $this->basicSalary / 100;
            } else {
                $providentEmp = 0;
                $providentComp = 0;
            }


            if ($client->client_additional > 0) {
                $emp_client_additional = ($client->client_additional > 0)
                    ? ($emp->basic_salary * $client->client_additional) / 100
                    : 0;
            } else {
                $emp_client_additional = 0;
            }

            $this->otherTransportAllowance = $taxedAllowance['taxMobileAllowance'] + $taxedAllowance['taxCleaningAllowance'];

            $sumofIncomTax = $this->basicSalary+$this->overTimeGuardSalary + $additionals['totalAdditionalHours'] + $this->guardTransport + $addPcrate['totalAddpcrateSalary'] + $this->totaltaxableDsaSalary + $this->otherTransportAllowance + $taxedAllowance['taxHousingAllowance'] + $emp_client_additional - $additonalDeductable['totalAddDeduction']
                - $this->transportDeductionNewEmployee
                - ($absence['totalAbsences'] * $absence['absenceGuardSalary']);


            $totalAdditionalDeductable =   $deduct['socialFund'] + $empDeductable['laborLoan'] + $empDeductable['totalLoan'] + $clientDeduct['creaditAssociation'] + $clientDeduct['sportAssociation'] + $empDeductable['totalOtherLoan']+$empDeductable['totalCreditLoan'];
            $totalDeduction = $providentEmp + $pensionEmp + $pHelper->incomeTax($sumofIncomTax) + $totalAdditionalDeductable + ($empDeductable['totalMedicalLoan'] * $medicalShare['medicalshare']);

            $totalNonTaxAllowance = $this->totalNontaxableDsaSalary + $additionalNonTaxableAllowance['additionalNonTaxableAllowance'] + $this->nonTaxTransportAllowance + $this->nonTaxDesertAllowance + $this->nonTaxMobileAllowance + $this->nonTaxPositionAllowance + $this->nonTaxCleaningAllowance + $this->nonTaxOtherAllowance;
            $netPay = $sumofIncomTax + $totalNonTaxAllowance - $totalDeduction;

            $this->pcratSalarySummary += $pcRate['totalpcrateSalary'];
            $this->basicSalarySummary += $this->guardSalary;
            $this->addPcratSalarySummary += $addPcrate['totalAddpcrateSalary'];
            $this->overtimeSummary += round($this->overTimeGuardSalary, 2);
            $this->additionalHourSummary += $additionals['totalAdditionalHours'];
            $this->absenceDeductionSummary += ($absence['totalAbsences'] * $absence['absenceGuardSalary']);
            $this->taxableOtherAllowanceSummary += $this->otherTransportAllowance;
            $this->transportAllowanceSummary += $this->guardTransport;
            $this->transportAllowanceDeductionSummary += $this->transportAllowanceDeduction;
            $this->totalAddeductionSummary += $additonalDeductable['totalAddDeduction'];
            $this->taxableDsaSalarySummary += $this->totalNontaxableDsaSalary;
            $this->taxableIncome += $sumofIncomTax;
            $this->pensionEmpSummary += $pensionEmp;
            $this->pensionCompSummary += $pensionComp;
            $this->providentEmpSummary += $providentEmp;
            $this->providentCompSummary += $providentComp;
            $this->totalOtherLoanSummary += $empDeductable['totalOtherLoan'];
            $this->totalDeductionSummary = $totalDeduction;
            $this->additionalNontaxableSummary += $additionalNonTaxableAllowance['additionalNonTaxableAllowance'];
            $this->nontaxTransportAllowanceSummary += $this->nonTaxTransportAllowance;
            $this->nontaxMobileAllowanceSummary += $this->nonTaxMobileAllowance;
            $this->nontaxCleaningAllowanceSummary += $this->nonTaxCleaningAllowance;
            $this->nontaxOtherAllowanceSummary += $this->nonTaxOtherAllowance;
            $this->nontaxDsaAllowanceSummary +=  $this->totalNontaxableDsaSalary;
            $this->nontaxPositionAllowanceSummary += $this->nonTaxPositionAllowance;
            $this->nontaxDesertAllowanceSummary += $this->nonTaxDesertAllowance;
            $this->netPaySummary += $netPay;

            $payrollData = [
                'year' => $year,
                'month' => $month,
                'emp_basic_id' => $emp->id,
                'client_id' => $client->id,
                'company_id' => $emp->company_id,
                'company_name' => $client->company_name,
                'first_name' => $emp->first_name,
                'middle_name' => $emp->middle_name,
                'last_name' => $emp->last_name,
                'total_pcrate_salary' => $pcRate['totalpcrateSalary'],
                'basic_salary' => $this->guardSalary,
                'total_add_pcrate_salary' => $addPcrate['totalAddpcrateSalary'],
                'over_time' => round($this->overTimeGuardSalary, 2),
                'total_additional_hours' => $additionals['totalAdditionalHours'],
                'absence_deduction_summry' =>round($absence['totalAbsences'] * $absence['absenceGuardSalary'],2),
                'absence' => $absence['totalAbsences'],
                'taxable_other_allowance' => $this->otherTransportAllowance,
                'transport_allowance' => round($this->guardTransport,2),
                'trans_ded' => $this->transportAllowanceDeduction,
                'total_add_Deduction' => $additonalDeductable['totalAddDeduction'],
                'total_taxable_dsa_salary' => $this->totaltaxableDsaSalary,
                'sum_taxable_income' =>round($sumofIncomTax,2),
                'pensionEmp' => $pensionEmp,
                'pensionComp' => $pensionComp,
                'providentEmp' => $providentEmp,
                'providentComp' => $providentComp,
                'medical_cost' => $empDeductable['totalMedicalLoan'],
                'loan' => $empDeductable['loan'],
                'total_other_loan' => $empDeductable['totalOtherLoan'],
                'total_deduction_summry' =>round($totalDeduction,2),
                'additional_nontaxable_allowance' => $additionalNonTaxableAllowance['additionalNonTaxableAllowance'] ?? 0,
                'nontax_transport_allowance' => round($this->nonTaxTransportAllowance,2) ?? 0,
                'nontax_desert_allowance' => $this->nonTaxDesertAllowance ?? 0,
                'nontax_mobile_allowance' => $this-> nonTaxMobileAllowance ?? 0,
                'nontax_cleaning_allowance' => $this-> nonTaxCleaningAllowance ?? 0,
                'nontax_other_allowance' => $this-> nonTaxOtherAllowance ?? 0,
                'nontax_position_allowance' => $this-> nonTaxPositionAllowance ?? 0,
                'nontax_dsa_allowance' => $this-> totalNontaxableDsaSalary ?? 0,
                'net_payment' => round($netPay,2),
                'emp_client_additional' => $emp_client_additional,
            ];

            $empPayroll->push($payrollData);
        }

        $summary = [[
            'id'=>1,
            'pcratesalarySummary' => $this->pcratSalarySummary,
            'basicSalarySummary' => $this->basicSalarySummary,
            'addPcratSalarySummary' =>  $this->addPcratSalarySummary,
            'overtimeSummary' => $this->overtimeSummary,
            'additionalHourSummary' => $this->additionalHourSummary,
            'absenceDeductionSummary' => $this->absenceDeductionSummary,
            'taxableOtherAllowanceSummary' => $this->taxableOtherAllowanceSummary,
            'transportAllowanceSummary' => $this->transportAllowanceSummary,
            'transportAllowanceDeductionSummary' => $this->transportAllowanceDeductionSummary,
            'totalAddeductionSummary' => $this->totalAddeductionSummary,
            'taxableDsaSalarySummary' => $this->taxableDsaSalarySummary,
            'taxableIncome' => round($this->taxableIncome,2),
            'pensionEmpSummary' => $this->pensionEmpSummary,
            'pensionCompSummary' => $this->pensionCompSummary,
            'providentEmpSummary' => $this->providentEmpSummary,
            'providentCompSummary' => $this->providentCompSummary,
            'totalOtherLoanSummary' => $this->totalOtherLoanSummary,
            'totalDeductionSummary' => round($this->totalDeductionSummary),
            'additionalNontaxableSummary' => $this->additionalNontaxableSummary,
            'nontaxTransportAllowanceSummary' => $this->nontaxTransportAllowanceSummary,
            'nontaxMobileAllowanceSummary' => $this->nontaxMobileAllowanceSummary,
            'nontaxCleaningAllowanceSummary' => $this->nontaxCleaningAllowanceSummary,
            'nontaxOtherAllowanceSummary' => $this->nontaxOtherAllowanceSummary,
            'nontaxDsaAllowanceSummary' => $this->nontaxDsaAllowanceSummary,
            'nontaxPositionAllowanceSummary' => $this->nontaxPositionAllowanceSummary,
            'nontaxDesertAllowanceSummary' => $this->nontaxDesertAllowanceSummary,
            'netPaySummary' => round($this->netPaySummary,2)
        ]];

        // $empPayroll->add($summary);
        return response([
            'empPayroll' => $empPayroll,
            'summary' => $summary
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\finance\Payroll  $payroll
     * @return \Illuminate\Http\Response
     */
    public function show(Payroll $payroll)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\finance\Payroll  $payroll
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Payroll $payroll)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\finance\Payroll  $payroll
     * @return \Illuminate\Http\Response
     */
    public function destroy(Payroll $payroll)
    {
        //
    }
}
