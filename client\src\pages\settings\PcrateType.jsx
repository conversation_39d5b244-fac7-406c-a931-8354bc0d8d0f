import { AddOutlined } from "@mui/icons-material";
import {
  Button,
  Typography,
  useTheme,
  Box,
  IconButton,
  Grid,
  TextField,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import React, { useState } from "react";
import api from "../../api/config/axiosConfig";
import Header from "../../components/Header";
import { useStateContext } from "../../context/ContextProvider";
import { tokens } from "../../utils/theme";
import _CircularProgress from "../../components/_CircularProgress";
function PcrateType() {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const queryClient = useQueryClient();
  const [clicked, setClicked] = useState(false);
  const [selectedRow, setSelectedRow] = useState({});
  const { setNotification, errors, setErrors } = useStateContext();
  const { data: pcrate, isLoading } = useQuery(
    ["pcrate"],
    async () => await api.get("pc_rate_type").then(({ data }) => data)
  );
  const columns = [
    {
      field: "id",
      headerName: "Id",
    },
    {
      field: "pc_rate_type",
      headerName: "Pc Rate Type",
      flex: 1,
    },
    {
      field: "pc_rate_amount",
      headerName: "Pc Rate Amount",
      flex: 1,
    },
    {
      field: "edit",
      headerName: "Edit",
      flex: 1,
      filterable: false,
      hide: false,
      sortable: false,
      renderCell: (params) => {
        const onRowClicked = (e) => {
          e.stopPropagation();
          setClicked(true);
          setSelectedRow(params.row);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.greenAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onRowClicked}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Edit
              </Typography>
            </Button>
          </Box>
        );
      },
    },
    {
      field: "delete",
      headerName: "Delete",
      flex: 1,
      sortable: false,
      filterable: false,
      hide: false,
      renderCell: (params) => {
        const deletePcRate = useMutation(
          (id) => api.delete(`pc_rate_type/${id}`),
          {
            onSuccess: () => {
              setNotification("Pc Rate Type Deleted Successfully ");
            },
            onSettled: () => {
              queryClient.invalidateQueries(["tax_center"]);
            },
          }
        );
        const onDeletePcRate = (e) => {
          e.stopPropagation();
          if (
            !window.confirm(
              "Are you sure you want to delete the selected  Pc Rate Type?"
            )
          ) {
            return;
          }
          deletePcRate.mutate(params.row.id);
        };
        return (
          <Box
            width="60%"
            m="0 auto"
            p="30x"
            display="flex"
            justifyContent="center"
            backgroundColor={colors.redAccent[700]}
            borderRadius="4px"
          >
            <Button onClick={onDeletePcRate}>
              <Typography color={colors.grey[100]} sx={{ ml: "5px" }}>
                Delete
              </Typography>
            </Button>
          </Box>
        );
      },
    },
  ];

  const handleClick = (e) => {
    e.preventDefault();
    setClicked(true);
  };

  const handleChange = (e) => {
    setSelectedRow({
      ...selectedRow,
      [e.target.name]: e.target.value,
    });
  };
  const postPcRate = useMutation((data) => api.post("pc_rate_type", data), {
    onSuccess: () => {
      queryClient.invalidateQueries(["pcrate"]);
      setNotification("pc rate type  created successfully");
      setClicked(false);
    },
    onError: (err) => {
      setClicked(true);
      if (err.response && err.response.status === 422) {
        if (err.response.data.errors) {
          setErrors(err.response.data.errors);
        }
      }
    },
  });

  const onSubmit = (e) => {
    e.preventDefault();
    const payload = {
      pc_rate_type: selectedRow.pc_rate_type,
      pc_rate_amount: selectedRow.pc_rate_amount,
    };
    if (selectedRow.id) {
      api
        .put(`pc_rate_type/${selectedRow.id}`, payload)
        .then(() => {
          setNotification("pc rate type  updated successfully");
          queryClient.invalidateQueries(["pcrate"]);
        })
        .catch((err) => {
          if (err.response && err.response.status === 422) {
            if (err.response.data.errors) {
              setErrors(err.response.data.errors);
            }
          }
          setClicked(true);
        });
    } else {
      postPcRate.mutate(payload);
    }
  };
  return (
    <Box m="10px">
      <Header title="Pcrate Type Table " subtitle="list of pcrate Type " />
      <Box display="flex" justifyContent="center" alignItems="center">
        <IconButton
          sx={{
            boxShadow: "2px 2px 4px rgba(0,0,0,0.25)",
            background: colors.primary[400],
          }}
          onClick={handleClick}
        >
          <AddOutlined color={colors.grey[100]} />
        </IconButton>
      </Box>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={clicked ? 9 : 12}>
          <Box
            m="10px 0 0 0"
            height="67vh"
            sx={{
              "& .MuiDataGrid-root": {
                border: "none",
              },
              "& .MuiDataGrid-cell": {
                borderBottom: "none",
              },
              "& .name-column--cell": {
                color: colors.greenAccent[300],
              },
              "& .MuiDataGrid-columnHeaders": {
                backgroundColor: colors.blueAccent[700],
                borderBottom: "none",
              },
              "& .MuiDataGrid-virtualScroller": {
                backgroundColor: colors.primary[400],
              },
              "& .MuiDataGrid-footerContainer": {
                borderTop: "none",
                backgroundColor: colors.blueAccent[700],
              },
              "& .MuiCheckBox-root": {
                color: `${colors.greenAccent[200]}!important`,
              },
              "& .MuiDataGrid-toolbarContainer .MuiButton-text": {
                color: `${colors.grey[100]}!important`,
              },
            }}
          >
            {isLoading ? (
              <_CircularProgress />
            ) : (
              <DataGrid columns={columns} rows={pcrate} />
            )}
          </Box>
        </Grid>
        {clicked ? (
          <Grid item xs={12} sm={3} component="form" onSubmit={onSubmit}>
            <TextField
              error={!errors.pc_rate_type == ""}
              id="pc_rate_type"
              margin="normal"
              fullWidth
              label="Pc Rate Type"
              name="pc_rate_type"
              InputLabelProps={{
                color: "success",
              }}
              value={selectedRow.pc_rate_type || ""}
              onChange={handleChange}
              helperText={errors.pc_rate_type ? errors.pc_rate_type[0] : null}
            />
            <TextField
              error={!errors.pc_rate_amount == ""}
              id="pc_rate_type"
              margin="normal"
              fullWidth
              label="Pc Rate Amount"
              name="pc_rate_amount"
              InputLabelProps={{
                color: "success",
              }}
              value={selectedRow.pc_rate_amount || ""}
              onChange={handleChange}
              helperText={
                errors.pc_rate_amount ? errors.pc_rate_amount[0] : null
              }
            />
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 2, py: 2 }}
              color="success"
              size="small"
            >
              submit
            </Button>
          </Grid>
        ) : undefined}
      </Grid>
    </Box>
  );
}

export default PcrateType;
