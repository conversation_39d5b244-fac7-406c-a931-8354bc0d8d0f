<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('emp_allowances', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('emp_basic_id');
            $table->foreign('emp_basic_id')->references('id')->on('emp_basics')->onDelete('cascade');
            $table->float('transport_allowance')->nullable();
            $table->float('cleaning_allowance')->nullable();
            $table->float('mobile_allowance')->nullable();
            $table->float('housing_allowance')->nullable();
            $table->float('non_tax_ta')->nullable();
            $table->float('non_tax_deseret')->nullable();
            $table->float('non_tax_position')->nullable();
            $table->float('non_tax_mobile')->nullable();
            $table->float('non_tax_cleaning')->nullable();
            $table->float('non_tax_other')->nullable();
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
    }

    // Schema::create('emp_allowances', function (Blueprint $table) {
    //     $table->integer('Emp_Allowance_ID')->primary()->unique();
    //     $table->integer('Eid')->index()->unique();
    //     $table->float('Transport_Allowance')->nullable();
    //     $table->float('Cleaning_Allowance');
    //     $table->float('Mobile_Allowance');
    //     $table->float('Housing_Allowance');
    //     $table->float('NonTax_TA');
    //     $table->float('NonTax_Deseret');
    //     $table->float('NonTax_Position');
    //     $table->float('NonTax_Mobile');
    //     $table->float('NonTax_Cleaning');
    //     $table->float('NonTax_Other');
    //     $table->integer('Uid');
    // });
    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('emp_allowances');
    }
};
