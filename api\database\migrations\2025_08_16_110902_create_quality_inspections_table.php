<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('quality_inspections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('coffee_batch_id')->constrained('coffee_batches');
            $table->decimal('moisture_content')->nullable();
            $table->integer('defect_count')->nullable();
            $table->decimal('cup_score')->nullable();
            $table->string('certification')->nullable();
            $table->enum('pass_fail_status', ['pass', 'fail']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('quality_inspections');
    }
};
