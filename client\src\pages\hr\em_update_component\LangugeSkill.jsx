import * as React from "react";
import { useTheme } from "@mui/material/styles";
import Box from "@mui/material/Box";
import OutlinedInput from "@mui/material/OutlinedInput";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import Chip from "@mui/material/Chip";
import { tokens } from "../../../utils/theme";
import { Button } from "@mui/material";
import api from "../../../api/config/axiosConfig";
import { useState } from "react";
import { useStateContext } from "../../../context/ContextProvider";
import { useEffect } from "react";
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
const langs = [
  "English",
  "Amharic",
  "Oromifa",
  "Tgrigna",
  "Somaligna",
  "Afarigna",
  "sidamgna",
];
function LangugeSkill({ id }) {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);

  const { errors, setErrors, setNotification } = useStateContext();
  const [langugeSkill, setLangugeSkill] = React.useState({
    emp_basic_id:id,
    languges: [],
  });
  const [isLang, setIsLang] = useState(false);

  useEffect(() => {
    if (id) {
      api
        .get(`emp_basics/${id}?all=true`, { params: { all: 'fetch_lang' } })
        .then(({ data }) => {
          if (data.data.language) {
            setLangugeSkill({
              emp_basic_id: id,
              languges: data.data.language.languges,
              id: data.data.language.id,
            });
            setIsLang(true);
          }
        });
    }
  }, [id]);

  const handleChange = (event) => {
    const {
      target: { value },
    } = event;
    setLangugeSkill((prevState) => ({
      ...prevState,
      languges: typeof value === "string" ? value.split(",") : value,
    }));
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    if (isLang) {
      api.put(`emp_language/${langugeSkill.id}`,{emp_basic_id:id,languges:langugeSkill.languges,user_id:null}).then(() => {
        setNotification("updated successfully");
      });
    } else {
      api
        .post("emp_language", {
          emp_basic_id: id,
          languges: langugeSkill.languges,
        })
        .then(() => {
          setNotification("created successfully");
        });
    }
  };
  return (
    <div>
      <FormControl sx={{ m: 1, width: 300 }}>
        <InputLabel id="demo-multiple-chip-label" style={{ color: "success" }}>
          Select Languages
        </InputLabel>
        <Select
          labelId="demo-multiple-chip-label"
          id="demo-multiple-chip"
          multiple
          value={langugeSkill.languges}
          name="languges"
          onChange={handleChange}
          input={<OutlinedInput id="select-multiple-chip" label="Chip" />}
          renderValue={(selected) => (
            <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
              {selected.map((value) => (
                <Chip key={value} label={value} />
              ))}
            </Box>
          )}
          MenuProps={MenuProps}
        >
          {langs.map((name) => (
            <MenuItem
              key={name}
              value={name}
            >
              {name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      <Box sx={{ display: "flex", justifyContent: "space-between" }}>
        <Button
          onClick={handleSubmit}
          sx={{ mt: 3, ml: 1 }}
          variant="contained"
        >
          {isLang ? <div>update</div> : <div>create</div>}
        </Button>
      </Box>
    </div>
  );
}

export default LangugeSkill;
