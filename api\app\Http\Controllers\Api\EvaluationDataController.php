<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\EvaluationData;
use Illuminate\Http\Request;

class EvaluationDataController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return EvaluationData::all();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'applicant_id' => 'required|exists:applicants,id',
            'committee_member_id' => 'required|exists:committee_members,id',
            'evaluation_date' => 'required|date',
            'work_ethic_score' => 'required|integer',
            'comments' => 'nullable|string',
        ]);

        $evaluationData = EvaluationData::create($validatedData);
        return $evaluationData;
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        return EvaluationData::find($id);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $evaluationData = EvaluationData::find($id);

        $validatedData = $request->validate([
            'applicant_id' => 'sometimes|required|exists:applicants,id',
            'committee_member_id' => 'sometimes|required|exists:committee_members,id',
            'evaluation_date' => 'sometimes|required|date',
            'work_ethic_score' => 'sometimes|required|integer',
            'comments' => 'nullable|string',
        ]);

        $evaluationData->update($validatedData);
        return $evaluationData;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        return EvaluationData::destroy($id);
    }
}
