<?php

namespace App\Http\Controllers\Api\finance;

use App\Http\Controllers\Controller;
use App\Models\finance\AddPcrate;
use Illuminate\Http\Request;

class AddPcrateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return response(AddPcrate::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $total = $request->input('no_pc') * $request->input('pc_rate_amount');

        $data = AddPcrate::create([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'no_pc' => $request->input('no_pc'),
            'salary' => $request->input('pc_rate_amount'),
            'total' => $total,
            'pc_type' => $request->input('pc_type'),
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'user_id' => $request->input('user_id')
        ]);

        return response($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\finance\AddPcrate  $addPcrate
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $addPcrate=AddPcrate::where('emp_basic_id',$id)->where('paid',0)->get();
        return response($addPcrate);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\finance\AddPcrate  $addPcrate
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $total = $request->input('no_pc') * $request->input('pc_rate_amount');

        $addPcrate=AddPcrate::find($id);
        $addPcrate->update([
            'emp_basic_id' => $request->input('emp_basic_id'),
            'no_pc' => $request->input('no_pc'),
            'salary' => $request->input('pc_rate_amount'),
            'total' => $total,
            'pc_type' => $request->input('pc_type'),
            'from_date' => $request->input('from_date'),
            'to_date' => $request->input('to_date'),
            'user_id' => $request->input('user_id')
        ]);

        return response($addPcrate);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\finance\AddPcrate  $addPcrate
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $addPcrate=AddPcrate::find($id);
        $addPcrate->delete();

        return response('', 204);
    }
}
