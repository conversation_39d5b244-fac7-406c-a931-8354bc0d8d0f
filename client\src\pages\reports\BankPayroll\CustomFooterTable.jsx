import {
  Table,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useTheme,
  TableBody,
  TableCell,
} from "@mui/material";
import React from "react";
import { tokens } from "../../../utils/theme";

const CustomFooterTable = ({ rows, columns }) => {
  const theme = useTheme();
  const colors = tokens(theme.palette.mode);
  const excludedColumns = [
    "id",
    "fullname",
    "start_date",
    "company_name",
    "company_code",
    "tax_center",
    "tin_no",
    'tax_center_name',
    'region'
  ];

  // Calculate the sum for each column
  const columnSums = {};
  columns.forEach((column) => {
    if (!excludedColumns.includes(column.field)) {
      columnSums[column.field] = rows.reduce(
        (acc, item) => acc + (item[column.field] || 0),
        0
      );
    }
  });

  const sumArray = Object.entries(columnSums).map(([field, sum]) => ({
    headerName:
      columns.find((column) => column.field === field)?.headerName || field,
    value: Math.round(sum, 2),
  }));

  return (
    <TableContainer>
      <Typography variant="h3" sx={{ color: `${colors.blueAccent[500]}` }}>
        total sums
      </Typography>
      <Table>
        <TableHead>
          <TableRow>
          {sumArray.map((header,index)=>(
            <TableCell key={index}>
                {header.headerName}
            </TableCell>
          ))}
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
          {sumArray.map((header,index)=>(
            <TableCell key={index}>
                {header.value}
            </TableCell>
          ))}
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default CustomFooterTable;
