<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('add_pcrates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('emp_basic_id');
            $table->foreign('emp_basic_id')->references('id')->on('emp_basics')->onDelete('cascade');
            $table->integer('no_pc')->default(0)->nullable();
            $table->float('salary')->default(0);
            $table->float('total')->default(0);
            $table->string('pc_type')->nullable();
            $table->date('from_date')->format('Y-m-d');
            $table->date('to_date')->format('Y-m-d');
            $table->tinyInteger('paid')->default(0);
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('add_pcrates');
    }
};
