<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Shipment extends Model
{
    use HasFactory;

    protected $fillable = [
        'export_order_id',
        'shipment_number',
        'container_number',
        'vessel_name',
        'departure_port',
        'arrival_port',
        'departure_date',
        'arrival_date',
        'tracking_number',
        'status',
    ];

    public function exportOrder()
    {
        return $this->belongsTo(ExportOrder::class);
    }
}
