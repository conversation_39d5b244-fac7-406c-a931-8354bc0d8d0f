import { useQuery } from '@tanstack/react-query';
import React from 'react';
import { useLocation } from 'react-router-dom';
import api from '../../../api/config/axiosConfig';
import { Box } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import _CircularProgress from '../../../components/_CircularProgress';
import CustomToolBar from '../../../helper/CustomToolBar';
import Header from '../../../components/Header';
import { useState } from 'react';
import { incomeTaxMessage } from '../formattedMessage';
import CustomFooterTable from './CustomFooterTable';
import {formatedMessage}from "../formattedMessage"
const IncomeTaxList = () => {
  const location = useLocation();
  const recivedData = location.state;
  const [companyName, setCompanyName] = useState('');
  const [region, setRegion] = useState('');
  const {
    data: response,
    isLoading,
    isFetched,
  } = useQuery(['incometax'], () =>
    api.get('incometax', { params: recivedData }).then(({ data }) => {
      //console.log(data)
      if (data.length > 0) {
        //setCompanyName(data[0].company_name);
        setRegion(recivedData.region);
      }

      return data.map((item, index) => ({
        ...item,
        id: index + 1,
      }));
    })
  );

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
    },
    {
      field: 'fullname',
      headerName: 'fullName',
      flex: 1,
    },
    {
      field: 'tin_no',
      headerName: 'Tin Number',
      flex: 1,
    },
    {
      field: 'start_date',
      headerName: 'Empoyeed Date',
      flex: 1,
    },
    {
      field: 'basic_salary',
      headerName: 'Basic_Salary',
      flex: 1,
    },
    {
      field: 'nontax_transport_allowance',
      headerName: 'Non Tax Transport Allowance',
      flex: 1,
    },
    {
      field: 'transport_allowance',
      headerName: 'Transport Allowance',
      flex: 1,
    },
    {
      field: 'gross_salary',
      headerName: 'Taxable Income',
      flex: 1,
    },
    {
      field: 'income_tax',
      headerName: 'Income Tax',
      flex: 1,
    },
    {
      field: 'tax_center_name',
      headerName: 'Tax Center',
      flex: 1,
    },
    {
      field: 'company_name',
      headerName: 'Company',
      flex: 1,
    },
    {
      field: 'company_code',
      headerName: 'Company Code',
      flex: 1,
    },
  ];

  return (
    <Box margin="10px">
      {recivedData.region ? (
        <Header
          title={`${region} INCOME TAX REPORT `}
          subtitle={formatedMessage(
            recivedData.firstDate,
            recivedData.lastDate,
            recivedData.year
          )}
        />
      ) : (
        <Header
          title="INCOME TAX REPORT"
          subtitle={incomeTaxMessage(
            recivedData.firstDate,
            recivedData.lastDate
          )}
        />
      )}
      {isLoading && <_CircularProgress />}
      {isFetched && (
        <DataGrid
          columns={columns}
          rows={response}
          autoHeight
          components={{
            Toolbar: CustomToolBar,
            Footer: () => (
              <CustomFooterTable rows={response} columns={columns} />
            ),
          }}
        />
      )}
    </Box>
  );
};

export default IncomeTaxList;
