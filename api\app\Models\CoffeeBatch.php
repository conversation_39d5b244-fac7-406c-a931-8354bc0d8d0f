<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CoffeeBatch extends Model
{
    use HasFactory;

    protected $fillable = [
        'batch_number',
        'harvest_date',
        'coffee_type',
        'grade',
        'processing_method',
        'quantity_kg',
        'status',
        'warehouse_id',
    ];

    public function warehouse()
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function qualityInspections()
    {
        return $this->hasMany(QualityInspection::class);
    }

    public function certifications()
    {
        return $this->hasMany(Certification::class);
    }

    public function purchasesFromFarmers()
    {
        return $this->hasMany(PurchaseFromFarmer::class);
    }

    public function phytosanitaryCertificates()
    {
        return $this->hasMany(PhytosanitaryCertificate::class);
    }

    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    public function exportOrders()
    {
        return $this->belongsToMany(ExportOrder::class, 'export_order_batches')->withPivot('allocated_weight_kg');
    }
}
