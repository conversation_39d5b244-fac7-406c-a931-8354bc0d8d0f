<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('emp_basics', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('middle_name');
            $table->string('last_name');
            $table->string('amharic_name')->nullable();
            $table->string('company_id')->nullable();
            $table->string('tin_no');
            $table->string('pension_id');
            $table->integer('file_no');
            $table->date('start_date')->format('Y-m-d')->nullable();
            $table->string('gender')->nullable();
            $table->date('date_of_birth')->format('Y-m-d')->nullable();;
            $table->string('position')->nullable();
            $table->float('basic_salary')->nullable();
            $table->string('rate_type')->nullable();
            $table->float('initial_salary');
            $table->string('insurance');
            $table->string('pension');
            $table->string('provident')->nullable();
            $table->boolean('termination_status')->default(0);
            $table->integer('user_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('emp_basics');
    }
};
