<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\EmpBank;
use App\Http\Requests\StoreEmpBankRequest;
use App\Http\Requests\UpdateEmpBankRequest;
use App\Http\Resources\EmpBankResource;
use Illuminate\Http\Request;

class EmpBankController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     * @return  \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index()
    {
        return EmpBankResource::collection(EmpBank::all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\StoreEmpBankRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // $data = $request->validated();
        $data = $request->all();
        $empBank = EmpBank::create([
            'emp_basic_id'=>$data['emp_basic_id'],
            'bank_account'=>$data['bank_account'],
            'bank_code'=>$data['bank_code'],
            'bank_branch'=>$data['bank_branch'],
            'user_id'=>$request->user_id,
        ]);

        return response(new EmpBankResource($empBank),201);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\EmpBank  $empBank
     * @return \Illuminate\Http\Response
     */
    public function show(EmpBank $empBank)
    {
        return response(new EmpBankResource($empBank));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UpdateEmpBankRequest  $request
     * @param  \App\Models\EmpBank  $empBank
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, EmpBank $empBank)
    {
        // $data = $request->validated();
        $data = $request->all();
        // $merged = array_merge($data, $request->input('user_id'));

        $empBank->update([
            'emp_basic_id'=>$data['emp_basic_id'],
            'bank_account'=>$data['bank_account'],
            'bank_code'=>$data['bank_code'],
            'bank_branch'=>$data['bank_branch'],
            'user_id'=>$request->user_id,
        ]);

        return response(new EmpBankResource($empBank));
    }

    /**
     * Return a list of all bank codes
     *
     * @return \Illuminate\Http\Response
     */
    public function company()
    {
        // Select all distinct bank codes
        $bank_code_List = EmpBank::distinct()->pluck('bank_code')->toArray();

        // Return the list of codes as JSON
        return response()->json($bank_code_List);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\EmpBank  $empBank
     * @return \Illuminate\Http\Response
     */
    public function destroy(EmpBank $empBank)
    {
        $empBank->delete();

        return response('', 204);
    }
}
