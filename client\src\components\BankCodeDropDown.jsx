import { TextField, MenuItem, Box } from '@mui/material';
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import api from '../api/config/axiosConfig';
import _CircularProgress from './_CircularProgress';

const BankCodeDropDown = ({ onSelectedCenter }) => {
  const [selectedCenter, setSelectedCenter] = useState('');

  const { data: response, isLoading } = useQuery(
    ['bank_code'],
    async () =>
      await api.get('bank_code').then(({ data }) => {
        console.log(data);
        return data;
      })
  );

  // if (!isLoading) {
  //     console.log(response);
  // }

  const handleCenterChange = (e) => {
    //console.log('selected center', e.target.value);
    const centerValue = e.target.value;
    setSelectedCenter(centerValue);
    onSelectedCenter({
      value: centerValue,
      clicked: true,
    });
    //console.log('onSelectedCenter ',onSelectedCenter);
  };

  if (isLoading) {
    return <_CircularProgress/>
  }

  return (
    <Box>
      <TextField
        select
        name="bank_code"
        value={selectedCenter}
        onChange={handleCenterChange}
        fullWidth
        label="Select Bank Name"
        // variant='standard'
        margin="normal"
        type="text"
        InputLabelProps={{
          color: 'success',
        }}
      >
        {response.map((item) => (
          <MenuItem key={item} value={item}>
            {item}
          </MenuItem>
        ))}
      </TextField>
    </Box>
  );
};

export default BankCodeDropDown;
