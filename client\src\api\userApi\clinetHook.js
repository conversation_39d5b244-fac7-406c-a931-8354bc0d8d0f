import { useQuery } from '@tanstack/react-query';
import api from '../config/axiosConfig';

export const useClientData = () => {
  return useQuery(
    ['client'],
    () => api.get('client').then(async ({ data }) => await data.data),
    {
      refetchOnWindowFocus: false,
      staleTime: 60000,
    }
  );
};

export const useClientCodeData = () => {
  return useQuery(
    ['client-code'],
    () =>
      api
        .get('client-code')
        .then(
          async ({ data }) =>
            await data.map((name, i) => ({ id: i, company_code: name.trim() }))
        ),
    {
      refetchOnWindowFocus: false,
      staleTime: 60000,
    }
  );
};

export const useEmpData = () => {
  return useQuery(
    ['emp_basics'],
    () => api.get('emp_basics').then(async ({ data }) => await data.data),
    {
      refetchOnWindowFocus: false,
      staleTime: 60000,
    }
  );
};

export const useEmployeeData = (emp_basic_id) => {
  return useQuery(
    ['emp', emp_basic_id],
    () => api.get(`emp_basics/${emp_basic_id}`).then(({ data }) => data.data),
    { staleTime: 60000, refetchOnWindowFocus: false, enabled: !!emp_basic_id }
  );
};

export const useTerminationReasons = () => {
  return useQuery(
    ['termination_reasons'],
    () => api.get('termination_reasons').then(({ data }) => data),
    {
      refetchOnWindowFocus: false,
      staleTime: 60000,
    }
  );
};
export const useTeminatedEmployeeSeverance = () => {
  return useQuery(
    ['terminated_emps'],
    async () =>
      await api.get('severance/terminated_emp').then(({ data }) => data),
    {
      refetchOnWindowFocus: false,
      staleTime: 60000,
    }
  );
};
export const useSeveranceCalculateion = (id) => {
  return useQuery(
    ['severance_calculation', id],
    async () =>
      await api.get(`severance/employee/${id}`).then(({ data }) => data),
    {
      enabled: !!id,
    }
  );
};
export const usePositionData = () => {
  return useQuery(
    ['position'],
    () => api.get('positions').then(async ({ data }) => await data.data),
    {
      refetchOnWindowFocus: false,
      staleTime: 60000,
    }
  );
};
export const useUsersData = () => {
  return useQuery(
    ['users'],
    () => api.get('users').then(async ({ data }) => await data),
    {
      refetchOnWindowFocus: false,
      staleTime: 60000,
    }
  );
};

export const useNotificationsCount = () => {
  return useQuery(['notify'], () =>
    api.get('countNotification').then(async ({ data }) => await data)
  );
};

export const useUnreadNotifications = () => {
  return useQuery(['notifications_unread'], () =>
    api.get('notifications/unread').then(async ({ data }) => await data)
  );
};
export const allNotifications = () => {
  return useQuery(['notifications'], () =>
    api.get('notifications').then(async ({ data }) => await data)
  );
};

export const useReadNotifications = () => {
  return useQuery(
    ['notifications_read'],
    () => api.get('notifications/read').then(async ({ data }) => await data),
    {
      // staleTime: 600000,
      refetchOnWindowFocus: false,
    }
  );
};

const useContract = (id) => {
  return useQuery([`contract_${id}`, id], () =>
    api.get(`/contract-follow-up/${id}`).then(({ data }) => data)
  );
};

export const usePaySeverance = () => {
  return useQuery(
    ['pay_severance'],
    async () => await api.get('severance/pay_report').then(({ data }) => data),
    {
      staleTime: 600000,
      refetchOnWindowFocus: false,
    }
  );
};
export const useSeverancePaid = () => {
  return useQuery(
    ['severance_paid'],
    async () => await api.get('severance/paid_report').then(({ data }) => data),
    {
      staleTime: 600000,
      refetchOnWindowFocus: false,
    }
  );
};
export const useSeveranceLiablity = () => {
  return useQuery(
    ['severance_liablity'],
    async () =>
      await api.get('severance/liablity_report').then(({ data }) => data),
    {
      staleTime: 600000,
      refetchOnWindowFocus: false,
    }
  );
};
export const useLeaveLiablity = () => {
  return useQuery(
    ['leave_liablity'],
    async () => await api.get('liablity/leave').then(({ data }) => data),
    {
      staleTime: 600000,
      refetchOnWindowFocus: false,
    }
  );
};
