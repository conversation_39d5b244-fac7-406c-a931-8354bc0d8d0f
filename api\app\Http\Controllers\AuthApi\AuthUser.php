<?php

namespace App\Http\Controllers\AuthApi;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
class AuthUser extends Controller
{
    public function user(){
        $user = Auth::user();
        $user->load('userPermission');
        $ext='';
        if ($user->image) {
            $imageUrl = Storage::path($user->image);
            $imageFile = file_get_contents($imageUrl);
            $ext = pathinfo($imageUrl, PATHINFO_EXTENSION);
            $base64Image = base64_encode($imageFile);
            $user['image'] = $base64Image;
        } 
        
        return response([
            'user' => $user,
            'ext' => $ext,
        ]);
    }
}
