<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QualityInspection extends Model
{
    use HasFactory;

    protected $fillable = [
        'coffee_batch_id',
        'moisture_content',
        'defect_count',
        'cup_score',
        'certification',
        'pass_fail_status',
    ];

    public function coffeeBatch()
    {
        return $this->belongsTo(CoffeeBatch::class);
    }
}
