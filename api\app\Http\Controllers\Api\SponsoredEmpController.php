<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SponsoredEmployee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SponsoredEmpController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = SponsoredEmployee::all();

        return response($data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // $data = $request->validate([
        //     'emp_basic_id' => 'required',
        //     'name' => 'required',
        //     'company_name' => 'required',
        //     'phone' => 'required|numeric',
        //     'expire_date' => 'required',
        //     'effective_date' => 'required',
        //     'office_phone' => 'numeric',
        // ]);

        $created = SponsoredEmployee::create($request->all());

        return response($created);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\SponsoredEmployee  $sponsoredEmployee
     * @return \Illuminate\Http\Response
     */
    public function show(SponsoredEmployee $sponsoredEmployee)
    {
        return response($sponsoredEmployee);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\SponsoredEmployee  $sponsoredEmployee
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // $data = $request->validate([
        //     'emp_basic_id'=>'required',
        //     'name' => 'required',
        //     'company_name' => 'required',
        //     'phone' => 'required|numeric',
        //     'expire_date' => 'required',
        //     'effective_date' => 'required',
        //     'office_phone' => 'numeric',
        // ]);
        $emp=SponsoredEmployee::find($id);
        $emp->update($request->all());
        return response($emp);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\SponsoredEmployee  $sponsoredEmployee
     * @return \Illuminate\Http\Response
     */
    public function destroy(SponsoredEmployee $sponsoredEmployee)
    {
        $sponsoredEmployee->delete();

        return response('', 204);
    }
}
