<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ContractFollowUp;
use App\Models\User;
use App\Notifications\ContractFollowUpNotification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Notification;

class ContractScheduleDueController extends Controller
{

    public function index()
    {
        $data = [];

        $results = ContractFollowUp::with(['client'])->whereMonth('last_triggerd_date', '=', date('m', strtotime(now())))->whereYear('last_triggerd_date', '=', date('Y', strtotime(now())))->whereDate('end_date','>=',now())->get();

        foreach ($results as $result) {
            $overdue = false;
            if ($result['last_triggerd_date'] <= now()) {
                $dueDateLefts = Carbon::parse($result['last_schedule'])->diffInDays(now()) + 1;
                if (date($result['last_schedule']) <= date($result['last_triggerd_date'])) {
                    $overdue = true;
                }

                $data[] = [
                    'id' => $result['id'],
                    'company_name' => $result->client->company_name,
                    'client_id' => $result['client_id'],
                    'daysLeftUntilDue' => $dueDateLefts,
                    'start_date' => $result['start_date'],
                    'end_date' => $result['end_date'],
                    'last_triggering_date'=>$result['last_triggerd_date'],
                    'contract_document' => $result['contract_document'],
                    'contract_schedule' => $result['contract_schedule'],
                    'message' => $result['message'],
                    'last_triggerd_date' => $result['last_triggerd_date'],
                    'last_schedule' => $result['last_schedule'],
                    'overdue' => $overdue,
                ];
                $users = User::whereIn('role', ['admin', 'hr'])->get();
                Notification::send($users, new ContractFollowUpNotification(contract_id: $result['id'], client_id: $result['client_id'], daysLeftUntilDue:$dueDateLefts, start_date: $result['start_date'], end_date: $result['end_date'], contract_document: $result['contract_document'], contract_schedule: $result['contract_schedule'], message: $result['message'], last_triggerd_date: $result['last_triggerd_date'], last_schedule: $result['last_schedule'], overdue: $overdue, company_name: $result->client->company_name,triggering_date:$result['last_triggerd_date']));
            }
        }
        return response($data);
        // return response($results);
    }

    public function updateScheduleReminder($id)
    {
        $data = ContractFollowUp::where('id', $id)->first();
        $contractShecdule = $data['contract_schedule'];
        $nextReminderDate = Carbon::parse($data['last_triggerd_date']);
        $lastScheduleDate = Carbon::parse($data['last_schedule']);

        switch ($contractShecdule) {
            case 'monthly':
                $nextReminderDate->addMonth();
                $lastScheduleDate->addMonth();
                break;
            case 'quarterly':
                $nextReminderDate->addMonths(3);
                $lastScheduleDate->addMonths(3);
                break;
            case 'yearly':
                $nextReminderDate->addYear();
                $lastScheduleDate->addYear();
                break;
            case 'semister':
                $nextReminderDate->addYear();
                $lastScheduleDate->addYear();
                break;
        }
        ContractFollowUp::where('id', $id)->update(['last_triggerd_date' => $nextReminderDate, 'last_schedule' => $lastScheduleDate]);

        return response('successfull update');
    }
}
