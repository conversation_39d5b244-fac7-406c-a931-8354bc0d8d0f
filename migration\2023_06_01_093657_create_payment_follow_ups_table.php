<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_follow_ups', function (Blueprint $table) {
            $table->id();
            $table->integer('client_id');
            $table->date('start_date')->format('Y-m-d');
            $table->date('end_date')->format('Y-m-d');
            $table->date('last_schedule')->format('Y-m-d');
            $table->date('last_triggerd_date')->format('Y-m-d');
            $table->string('collection_schedule')->default('monthly');
            $table->integer('reminder')->default(-5);
            $table->float('amount')->nullable();
            $table->longText('message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_follow_ups');
    }
};
