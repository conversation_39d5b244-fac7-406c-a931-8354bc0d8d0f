<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'country',
        'email',
        'phone',
        'payment_terms',
        'preferred_coffee_type',
    ];

    public function exportOrders()
    {
        return $this->hasMany(ExportOrder::class);
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }
}
