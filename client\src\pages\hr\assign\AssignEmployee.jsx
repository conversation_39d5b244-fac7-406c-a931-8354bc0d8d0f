import {
  Paper,
  useTheme,
  Box,
  Grid,
  TextField,
  MenuItem,
  Button,
} from '@mui/material';
import React, { useEffect } from 'react';
import api from '../../../api/config/axiosConfig';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useStateContext } from '../../../context/ContextProvider';
import { useState } from 'react';
import SearchBar from '../../../components/SearchBar';
import { usePositionData } from '../../../api/userApi/clinetHook';
function AssignEmployee() {
  const useQueryClinet = useQueryClient();
  const { errors, setErrors, setNotification, user } = useStateContext();
  const [emp, setEmp] = useState([]);
  const [formData, setFormData] = useState({
    client_id: '',
    emp_basic_id: '',
    position: '',
    assign_date: '',
    user_id: user.id,
  });

  const { data: client, isLoading } = useQuery(
    ['client'],
    () => api.get('client').then(async ({ data }) => await data.data),
    { refetchOnWindowFocus: false, staleTime: 60000 }
  );

  const { data: positions, isFetched } = usePositionData();

  const {} = useQuery(
    ['assign', formData.position],
    () =>
      api
        .get('assign', { params: { position: formData.position } })
        .then(({ data }) => {
          setEmp(data.data);
        }),
    {
      refetchOnWindowFocus: false,
      staleTime: 60000,
      enabled: !!formData.position,
    }
  );
  const assign = useMutation((data) => api.post('assign', data), {
    onSuccess: () => {
      setNotification(` is succussfully assigned`);
    },
    onError: (err) => {},
    onSettled: () => {
      useQueryClinet.invalidateQueries(['client', 'position']);
    },
  });

  const handleClientSelect = ({ value }) => {
    setFormData({
      ...formData,
      client_id: value.id,
    });
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    assign.mutate(formData);
  };
  if (isLoading) {
    return <div>Loading...</div>;
  }
  return (
    <Box component="form" onSubmit={handleSubmit}>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={4}>
          <SearchBar client={client} onClientSelect={handleClientSelect} />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            error={!errors.position == ''}
            margin="normal"
            label="Position"
            name="position"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={formData.position}
            helperText={errors.position ? errors.position[0] : null}
          >
            {isFetched ? (
              positions.map((val) => (
                <MenuItem key={val.id} value={val.position_name}>
                  {val.position_name}
                </MenuItem>
              ))
            ) : (
              <MenuItem value={''}>Select Positions</MenuItem>
            )}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <TextField
            error={!errors.emp_basic_id == ''}
            margin="normal"
            label="empLoyee To Be Assigned"
            name="emp_basic_id"
            fullWidth
            select
            variant="standard"
            type="text"
            InputLabelProps={{
              color: 'success',
            }}
            onChange={handleChange}
            value={formData.emp_basic_id}
            helperText={errors.emp_basic_id ? errors.emp_basic_id[0] : null}
          >
            {isFetched ? (
              emp.map((val) => (
                <MenuItem key={val.id} value={val.id}>
                  {val.first_name} {val.middle_name} {val.last_name}
                </MenuItem>
              ))
            ) : (
              <MenuItem value="">Select employees</MenuItem>
            )}
          </TextField>
        </Grid>
        <Grid item xs={12} md={3} sm={4}>
          <TextField
            error={!errors.assign_date == ''}
            margin="normal"
            label="Assignment Date"
            name="assign_date"
            fullWidth
            //variant="standard"
            type="date"
            required
            InputLabelProps={{
              //color: 'success',
              shrink: true,
            }}
            onChange={handleChange}
          />
        </Grid>
        <Grid item xs={12} md={3} sm={4}>
          <Button
            type="submit"
            variant="contained"
            sx={{ mt: 3, ml: 1 }}
            color="success"
          >
            submit
          </Button>
        </Grid>
      </Grid>
    </Box>
  );
}

export default AssignEmployee;
